{"name": "make-fetch-happen", "version": "9.1.0", "description": "Opinionated, caching, retrying fetch client", "main": "lib/index.js", "files": ["lib"], "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "npm run eslint -- lib test", "lintfix": "npm run lint -- --fix"}, "repository": "https://github.com/npm/make-fetch-happen", "keywords": ["http", "request", "fetch", "mean girls", "caching", "cache", "subresource integrity"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "twitter": "maybekatz"}, "license": "ISC", "dependencies": {"agentkeepalive": "^4.1.3", "cacache": "^15.2.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "minipass": "^3.1.3", "minipass-collect": "^1.0.2", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "socks-proxy-agent": "^6.0.0", "ssri": "^8.0.0"}, "devDependencies": {"eslint": "^7.26.0", "eslint-plugin-import": "^2.23.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0", "mkdirp": "^1.0.4", "nock": "^13.0.11", "npmlog": "^5.0.0", "require-inject": "^1.4.2", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.0", "tap": "^15.0.9"}, "engines": {"node": ">= 10"}, "tap": {"color": 1, "files": "test/*.js", "check-coverage": true}}