{"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^7.32.0", "eslint-config-semistandard": "^16.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "readme": "README.md", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "files": ["*.{c,h,gyp,gypi}", "package-support.json", "tools/"], "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix"}, "pre-commit": "lint", "version": "7.1.1", "support": true}