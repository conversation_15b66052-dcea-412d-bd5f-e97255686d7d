import 'reflect-metadata';
import { webcrypto } from 'crypto';

// Global test setup for NestJS applications
// This ensures that decorators and metadata work properly in test environment

// Polyfill crypto for Node.js test environment
if (!globalThis.crypto) {
  globalThis.crypto = webcrypto as any;
}

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_HOST = 'localhost';
process.env.DATABASE_PORT = '5432';
process.env.DATABASE_USERNAME = 'postgres';
process.env.DATABASE_PASSWORD = 'password';
process.env.DATABASE_NAME = 'stria_test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.SESSION_SECRET = 'test-session-secret';

// Mock external services for testing
jest.mock('@aws-sdk/client-secrets-manager', () => ({
  SecretsManagerClient: jest.fn(),
  GetSecretValueCommand: jest.fn(),
}));

jest.mock('stripe', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    paymentIntents: {
      create: jest.fn(),
      retrieve: jest.fn(),
      confirm: jest.fn(),
    },
    customers: {
      create: jest.fn(),
      retrieve: jest.fn(),
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
  })),
}));

// Increase test timeout for integration tests
jest.setTimeout(30000);

// stria-backend/test/setup.ts - Add cleanup
afterAll(async () => {
  // Close any open database connections
  await new Promise(resolve => setTimeout(resolve, 500));
});
