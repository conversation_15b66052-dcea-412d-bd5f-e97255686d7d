{"name": "stria-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate -d src/database/data-source.ts", "migration:run": "npm run typeorm -- migration:run -d src/database/data-source.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/database/data-source.ts"}, "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@aws-sdk/client-secrets-manager": "^3.0.0", "@hookform/resolvers": "^5.1.1", "@nestjs/axios": "^4.0.1", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.4.19", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@types/handlebars": "^4.0.40", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "@types/stripe": "^8.0.416", "axios": "^1.10.0", "bcrypt": "^6.0.0", "cache-manager": "^7.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "handlebars": "^4.7.8", "helmet": "^8.1.0", "lucide-react": "^0.525.0", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.3", "puppeteer": "^24.14.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sqlite3": "^5.1.7", "stripe": "^18.3.0", "swagger-ui-express": "^5.0.1", "tailwind-merge": "^3.3.1", "typeorm": "^0.3.25", "zod": "^4.0.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@playwright/test": "^1.54.1", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}