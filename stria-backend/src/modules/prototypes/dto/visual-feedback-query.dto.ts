import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>U<PERSON><PERSON>, IsString, IsInt, <PERSON>, <PERSON>, IsDateString, IsNumber } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { 
  FeedbackType, 
  FeedbackStatus, 
  FeedbackPriority, 
  AnnotationType 
} from '../enums/visual-feedback.enum';

/**
 * Visual Feedback Query DTO
 * Data transfer object for querying visual feedback with filters and pagination
 */
export class VisualFeedbackQueryDto {
  // Pagination
  @ApiPropertyOptional({ 
    description: 'Page number (1-based)',
    minimum: 1,
    default: 1,
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ 
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  // Filtering by relationships
  @ApiPropertyOptional({ 
    description: 'Filter by prototype ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  prototypeId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by version ID',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  versionId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by creator user ID',
    format: 'uuid',
    example: '789e0123-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  createdBy?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by assigned user ID',
    format: 'uuid',
    example: '012e3456-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  assignedTo?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by resolver user ID',
    format: 'uuid',
    example: '345e6789-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  resolvedBy?: string;

  // Filtering by feedback properties
  @ApiPropertyOptional({ 
    description: 'Filter by feedback type',
    enum: FeedbackType,
    example: FeedbackType.DESIGN_ISSUE
  })
  @IsOptional()
  @IsEnum(FeedbackType)
  type?: FeedbackType;

  @ApiPropertyOptional({ 
    description: 'Filter by feedback status',
    enum: FeedbackStatus,
    example: FeedbackStatus.OPEN
  })
  @IsOptional()
  @IsEnum(FeedbackStatus)
  status?: FeedbackStatus;

  @ApiPropertyOptional({ 
    description: 'Filter by feedback priority',
    enum: FeedbackPriority,
    example: FeedbackPriority.HIGH
  })
  @IsOptional()
  @IsEnum(FeedbackPriority)
  priority?: FeedbackPriority;

  @ApiPropertyOptional({ 
    description: 'Filter by annotation type',
    enum: AnnotationType,
    example: AnnotationType.RECTANGLE
  })
  @IsOptional()
  @IsEnum(AnnotationType)
  annotationType?: AnnotationType;

  // Search
  @ApiPropertyOptional({ 
    description: 'Search in feedback title and content',
    example: 'button alignment'
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by tags (comma-separated)',
    example: 'ui,critical,mobile'
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.split(',').map((tag: string) => tag.trim()))
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Filter by category',
    example: 'usability'
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by severity',
    example: 'high'
  })
  @IsOptional()
  @IsString()
  severity?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by target element ID',
    example: 'button-submit'
  })
  @IsOptional()
  @IsString()
  targetElementId?: string;

  // Position-based filtering
  @ApiPropertyOptional({ 
    description: 'Filter by minimum X position',
    example: 100
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minX?: number;

  @ApiPropertyOptional({ 
    description: 'Filter by maximum X position',
    example: 500
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxX?: number;

  @ApiPropertyOptional({ 
    description: 'Filter by minimum Y position',
    example: 200
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minY?: number;

  @ApiPropertyOptional({ 
    description: 'Filter by maximum Y position',
    example: 800
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxY?: number;

  // Date filtering
  @ApiPropertyOptional({ 
    description: 'Filter feedback created after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  createdAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter feedback created before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  createdBefore?: string;

  @ApiPropertyOptional({ 
    description: 'Filter feedback resolved after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  resolvedAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter feedback resolved before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  resolvedBefore?: string;

  // Sorting
  @ApiPropertyOptional({ 
    description: 'Sort field',
    enum: ['createdAt', 'updatedAt', 'priority', 'status', 'type', 'positionX', 'positionY'],
    default: 'createdAt',
    example: 'createdAt'
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['ASC', 'DESC'],
    default: 'DESC',
    example: 'DESC'
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  // Include related data
  @ApiPropertyOptional({ 
    description: 'Include prototype information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includePrototype?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include version information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeVersion?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include creator information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeCreator?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include assignee information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeAssignee?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include soft-deleted feedback',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDeleted?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show unresolved feedback',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  unresolvedOnly?: boolean = false;
}
