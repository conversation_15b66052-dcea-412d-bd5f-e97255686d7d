import { PartialType, OmitType } from '@nestjs/swagger';
import { CreateDesignApprovalWorkflowDto } from './create-design-approval-workflow.dto';

/**
 * Update Design Approval Workflow DTO
 * Data transfer object for updating existing approval workflows
 * Extends CreateDesignApprovalWorkflowDto but makes all fields optional and removes prototypeId
 */
export class UpdateDesignApprovalWorkflowDto extends PartialType(
  OmitType(CreateDesignApprovalWorkflowDto, ['prototypeId'] as const)
) {}
