import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>num, 
  IsUUID, 
  IsBoolean,
  ValidateNested,
  IsArray,
  IsNumber,
  Min
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CommentType } from '../enums/comment-discussion.enum';
import { CommentMentionDto } from './create-comment-discussion.dto';

/**
 * Add Comment DTO
 * Data transfer object for adding comments to existing discussions
 */
export class AddCommentDto {
  @ApiProperty({ 
    description: 'Comment content', 
    example: 'I agree with the previous feedback. The button placement could be improved for better accessibility.' 
  })
  @IsString()
  content: string;

  @ApiPropertyOptional({ 
    description: 'Comment type',
    enum: CommentType,
    example: CommentType.REPLY
  })
  @IsOptional()
  @IsEnum(CommentType)
  type?: CommentType;

  @ApiProperty({ 
    description: 'Discussion ID this comment belongs to',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  discussionId: string;

  @ApiPropertyOptional({ 
    description: 'Parent comment ID for threaded replies',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  parentCommentId?: string;

  @ApiPropertyOptional({ 
    description: 'Array of user mentions in the comment',
    type: [CommentMentionDto]
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CommentMentionDto)
  @IsArray()
  mentions?: CommentMentionDto[];

  @ApiPropertyOptional({ 
    description: 'Whether comment is anonymous',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isAnonymous?: boolean;

  @ApiPropertyOptional({ 
    description: 'Whether comment is private (only visible to moderators)',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;

  @ApiPropertyOptional({ 
    description: 'Whether comment is marked as solution',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isSolution?: boolean;

  @ApiPropertyOptional({ 
    description: 'Comment priority level',
    example: 1,
    minimum: 1,
    maximum: 5
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  priority?: number;

  @ApiPropertyOptional({ 
    description: 'Array of attachment URLs',
    example: ['https://example.com/attachments/mockup.png', 'https://example.com/attachments/specs.pdf']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiPropertyOptional({ 
    description: 'Array of tags for categorization',
    example: ['suggestion', 'accessibility', 'mobile']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Estimated time to implement suggestion (in hours)',
    example: 4.5,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  estimatedHours?: number;

  @ApiPropertyOptional({ 
    description: 'Impact level of the comment/suggestion',
    enum: ['low', 'medium', 'high', 'critical'],
    example: 'medium'
  })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high', 'critical'])
  impactLevel?: string;

  @ApiPropertyOptional({ 
    description: 'Confidence level in the comment/suggestion',
    example: 85,
    minimum: 0,
    maximum: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  confidenceLevel?: number;

  @ApiPropertyOptional({ 
    description: 'Array of related comment IDs',
    example: ['comment-123', 'comment-456']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  relatedComments?: string[];

  @ApiPropertyOptional({ 
    description: 'Array of referenced visual feedback IDs',
    example: ['feedback-123', 'feedback-456']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  referencedFeedback?: string[];

  @ApiPropertyOptional({ 
    description: 'Custom properties object',
    example: { 
      reviewType: 'accessibility',
      testingDevice: 'iPhone 14',
      browserVersion: 'Chrome 120'
    }
  })
  @IsOptional()
  customProperties?: Record<string, any>;
}
