import { 
  Is<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>al, 
  Is<PERSON>num, 
  IsUUID, 
  IsNumber, 
  Min, 
  Max, 
  IsHexColor,
  ValidateNested,
  IsArray
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  FeedbackType, 
  FeedbackStatus, 
  FeedbackPriority, 
  AnnotationType 
} from '../enums/visual-feedback.enum';

/**
 * Create Visual Feedback DTO
 * Data transfer object for creating new visual feedback annotations
 */
export class CreateVisualFeedbackDto {
  @ApiPropertyOptional({ 
    description: 'Feedback title', 
    example: 'Button alignment issue',
    maxLength: 255 
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ 
    description: 'Feedback content/description', 
    example: 'The submit button appears to be misaligned with the form fields. It should be centered.' 
  })
  @IsString()
  content: string;

  @ApiPropertyOptional({ 
    description: 'Feedback type',
    enum: FeedbackType,
    example: FeedbackType.DESIGN_ISSUE
  })
  @IsOptional()
  @IsEnum(FeedbackType)
  type?: FeedbackType;

  @ApiPropertyOptional({ 
    description: 'Feedback status',
    enum: FeedbackStatus,
    example: FeedbackStatus.OPEN
  })
  @IsOptional()
  @IsEnum(FeedbackStatus)
  status?: FeedbackStatus;

  @ApiPropertyOptional({ 
    description: 'Feedback priority',
    enum: FeedbackPriority,
    example: FeedbackPriority.MEDIUM
  })
  @IsOptional()
  @IsEnum(FeedbackPriority)
  priority?: FeedbackPriority;

  @ApiPropertyOptional({ 
    description: 'Annotation type',
    enum: AnnotationType,
    example: AnnotationType.RECTANGLE
  })
  @IsOptional()
  @IsEnum(AnnotationType)
  annotationType?: AnnotationType;

  // Position and dimensions
  @ApiProperty({ 
    description: 'X coordinate position',
    example: 150.5,
    minimum: 0
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  positionX: number;

  @ApiProperty({ 
    description: 'Y coordinate position',
    example: 200.75,
    minimum: 0
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  positionY: number;

  @ApiPropertyOptional({ 
    description: 'Z-index for layering',
    example: 1,
    minimum: 0,
    maximum: 1000
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  positionZ?: number;

  @ApiPropertyOptional({ 
    description: 'Annotation width (for shapes)',
    example: 100.0,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  width?: number;

  @ApiPropertyOptional({ 
    description: 'Annotation height (for shapes)',
    example: 50.0,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  height?: number;

  // Visual styling
  @ApiPropertyOptional({ 
    description: 'Annotation color (hex code)',
    example: '#FF6B6B',
    pattern: '^#[0-9A-Fa-f]{6}$'
  })
  @IsOptional()
  @IsHexColor()
  color?: string;

  @ApiPropertyOptional({ 
    description: 'Border color (hex code)',
    example: '#000000',
    pattern: '^#[0-9A-Fa-f]{6}$'
  })
  @IsOptional()
  @IsHexColor()
  borderColor?: string;

  @ApiPropertyOptional({ 
    description: 'Background color (hex code)',
    example: '#FFFFFF',
    pattern: '^#[0-9A-Fa-f]{6}$'
  })
  @IsOptional()
  @IsHexColor()
  backgroundColor?: string;

  @ApiPropertyOptional({ 
    description: 'Opacity level',
    example: 0.8,
    minimum: 0,
    maximum: 1
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(1)
  opacity?: number;

  // Target element information
  @ApiPropertyOptional({ 
    description: 'Target element ID (Figma node ID or CSS selector)',
    example: 'button-submit'
  })
  @IsOptional()
  @IsString()
  targetElementId?: string;

  @ApiPropertyOptional({ 
    description: 'Target element type',
    example: 'button'
  })
  @IsOptional()
  @IsString()
  targetElementType?: string;

  @ApiPropertyOptional({ 
    description: 'Target element name',
    example: 'Submit Button'
  })
  @IsOptional()
  @IsString()
  targetElementName?: string;

  // Relationships
  @ApiProperty({ 
    description: 'Prototype ID this feedback belongs to',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  prototypeId: string;

  @ApiPropertyOptional({ 
    description: 'Specific prototype version ID',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  versionId?: string;

  @ApiPropertyOptional({ 
    description: 'Assign feedback to user',
    format: 'uuid',
    example: '789e0123-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  assignedTo?: string;

  // Metadata
  @ApiPropertyOptional({ 
    description: 'Screenshot URL',
    example: 'https://example.com/screenshots/feedback-123.png'
  })
  @IsOptional()
  @IsString()
  screenshot?: string;

  @ApiPropertyOptional({ 
    description: 'Array of attachment URLs',
    example: ['https://example.com/attachments/file1.pdf', 'https://example.com/attachments/file2.png']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiPropertyOptional({ 
    description: 'Array of tags for categorization',
    example: ['ui', 'critical', 'mobile']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Feedback category',
    example: 'usability'
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ 
    description: 'Severity level',
    example: 'high'
  })
  @IsOptional()
  @IsString()
  severity?: string;

  @ApiPropertyOptional({ 
    description: 'Estimated effort in hours',
    example: 2.5,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  estimatedEffort?: number;

  @ApiPropertyOptional({ 
    description: 'Array of related feedback IDs',
    example: ['feedback-456', 'feedback-789']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  relatedFeedbacks?: string[];

  @ApiPropertyOptional({ 
    description: 'Custom properties object',
    example: { browser: 'Chrome', device: 'Desktop', resolution: '1920x1080' }
  })
  @IsOptional()
  customFields?: Record<string, any>;
}
