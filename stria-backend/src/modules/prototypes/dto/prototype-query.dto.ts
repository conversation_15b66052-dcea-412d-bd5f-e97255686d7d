import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsI<PERSON>, Min, Max, IsDateString } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PrototypeType, PrototypeStatus, FigmaIntegrationStatus } from '../enums/prototype.enum';

/**
 * Prototype Query DTO
 * Data transfer object for querying prototypes with filters and pagination
 */
export class PrototypeQueryDto {
  // Pagination
  @ApiPropertyOptional({ 
    description: 'Page number (1-based)',
    minimum: 1,
    default: 1,
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ 
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  // Filtering
  @ApiPropertyOptional({ 
    description: 'Filter by project ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  projectId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by prototype type',
    enum: PrototypeType,
    example: PrototypeType.HIGH_FIDELITY
  })
  @IsOptional()
  @IsEnum(PrototypeType)
  type?: PrototypeType;

  @ApiPropertyOptional({ 
    description: 'Filter by prototype status',
    enum: PrototypeStatus,
    example: PrototypeStatus.IN_REVIEW
  })
  @IsOptional()
  @IsEnum(PrototypeStatus)
  status?: PrototypeStatus;

  @ApiPropertyOptional({ 
    description: 'Filter by Figma integration status',
    enum: FigmaIntegrationStatus,
    example: FigmaIntegrationStatus.SYNCED
  })
  @IsOptional()
  @IsEnum(FigmaIntegrationStatus)
  figmaIntegrationStatus?: FigmaIntegrationStatus;

  @ApiPropertyOptional({ 
    description: 'Filter by creator user ID',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  createdBy?: string;

  // Search
  @ApiPropertyOptional({ 
    description: 'Search in prototype name and description',
    example: 'mobile app wireframe'
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by tags (comma-separated)',
    example: 'mobile,responsive,v2.0'
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.split(',').map((tag: string) => tag.trim()))
  tags?: string[];

  // Date filtering
  @ApiPropertyOptional({ 
    description: 'Filter prototypes created after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  createdAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter prototypes created before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  createdBefore?: string;

  @ApiPropertyOptional({ 
    description: 'Filter prototypes updated after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  updatedAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter prototypes updated before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  updatedBefore?: string;

  // Sorting
  @ApiPropertyOptional({ 
    description: 'Sort field',
    enum: ['name', 'createdAt', 'updatedAt', 'status', 'type'],
    default: 'updatedAt',
    example: 'updatedAt'
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'updatedAt';

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['ASC', 'DESC'],
    default: 'DESC',
    example: 'DESC'
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  // Include related data
  @ApiPropertyOptional({ 
    description: 'Include project information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeProject?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include creator information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeCreator?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include version count in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeVersions?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include soft-deleted prototypes',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDeleted?: boolean = false;
}
