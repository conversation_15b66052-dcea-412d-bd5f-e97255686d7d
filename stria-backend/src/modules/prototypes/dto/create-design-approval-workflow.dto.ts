import { 
  IsS<PERSON>, 
  IsOptional, 
  IsEnum, 
  IsUUID, 
  IsNumber, 
  Min, 
  Max, 
  IsBoolean,
  IsDateString,
  ValidateNested,
  IsArray,
  ArrayMinSize
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  WorkflowType, 
  WorkflowStatus, 
  ApprovalStrategy,
  WorkflowPriority
} from '../enums/design-approval-workflow.enum';

/**
 * Approval Step Configuration DTO
 * Defines a step in the approval workflow
 */
export class ApprovalStepConfigDto {
  @ApiProperty({ 
    description: 'Step name', 
    example: 'Design Review',
    maxLength: 255 
  })
  @IsString()
  stepName: string;

  @ApiPropertyOptional({ 
    description: 'Step description', 
    example: 'Review the design for usability and consistency' 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: 'Number of required approvers for this step',
    example: 2,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  requiredApprovers: number;

  @ApiPropertyOptional({ 
    description: 'Array of user IDs who can approve this step',
    example: ['user-123', 'user-456']
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  approverIds?: string[];

  @ApiPropertyOptional({ 
    description: 'Whether this step is required',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isRequired?: boolean;

  @ApiPropertyOptional({ 
    description: 'Whether this step can be skipped',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  allowSkip?: boolean;

  @ApiPropertyOptional({ 
    description: 'Step due date (ISO string)',
    example: '2024-02-01T10:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiPropertyOptional({ 
    description: 'Estimated hours for this step',
    example: 4.5,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  estimatedHours?: number;

  @ApiPropertyOptional({ 
    description: 'Array of step numbers this step depends on',
    example: [1, 2]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  dependsOnSteps?: number[];
}

/**
 * Create Design Approval Workflow DTO
 * Data transfer object for creating new approval workflows
 */
export class CreateDesignApprovalWorkflowDto {
  @ApiProperty({ 
    description: 'Workflow name', 
    example: 'Mobile App Design Approval',
    maxLength: 255 
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({ 
    description: 'Workflow description', 
    example: 'Complete approval process for mobile app design including stakeholder review and technical validation' 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ 
    description: 'Workflow type',
    enum: WorkflowType,
    example: WorkflowType.SEQUENTIAL
  })
  @IsOptional()
  @IsEnum(WorkflowType)
  type?: WorkflowType;

  @ApiPropertyOptional({ 
    description: 'Initial workflow status',
    enum: WorkflowStatus,
    example: WorkflowStatus.DRAFT
  })
  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus;

  @ApiPropertyOptional({ 
    description: 'Approval strategy',
    enum: ApprovalStrategy,
    example: ApprovalStrategy.ALL_REQUIRED
  })
  @IsOptional()
  @IsEnum(ApprovalStrategy)
  approvalStrategy?: ApprovalStrategy;

  @ApiPropertyOptional({ 
    description: 'Total number of required approvals',
    example: 3,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  requiredApprovals?: number;

  @ApiPropertyOptional({ 
    description: 'Allow parallel approval processing',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  allowParallelApproval?: boolean;

  @ApiPropertyOptional({ 
    description: 'Auto-approve when threshold is met',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  autoApproveOnThreshold?: boolean;

  @ApiPropertyOptional({ 
    description: 'Approval threshold percentage (0-100)',
    example: 75.0,
    minimum: 0,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  approvalThresholdPercentage?: number;

  @ApiPropertyOptional({ 
    description: 'Workflow due date (ISO string)',
    example: '2024-02-15T17:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiPropertyOptional({ 
    description: 'Reminder interval in hours',
    example: 24,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  reminderIntervalHours?: number;

  @ApiPropertyOptional({ 
    description: 'Escalation time in hours',
    example: 72,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  escalationHours?: number;

  @ApiPropertyOptional({ 
    description: 'Workflow priority',
    enum: WorkflowPriority,
    example: WorkflowPriority.MEDIUM
  })
  @IsOptional()
  @IsEnum(WorkflowPriority)
  priority?: WorkflowPriority;

  @ApiProperty({ 
    description: 'Prototype ID this workflow belongs to',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  prototypeId: string;

  @ApiPropertyOptional({ 
    description: 'Assign workflow to user',
    format: 'uuid',
    example: '789e0123-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  assignedTo?: string;

  @ApiProperty({ 
    description: 'Array of approval steps configuration',
    type: [ApprovalStepConfigDto],
    minItems: 1
  })
  @ValidateNested({ each: true })
  @Type(() => ApprovalStepConfigDto)
  @IsArray()
  @ArrayMinSize(1)
  steps: ApprovalStepConfigDto[];

  // Notification settings
  @ApiPropertyOptional({ 
    description: 'Enable email notifications',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  enableEmailNotifications?: boolean;

  @ApiPropertyOptional({ 
    description: 'Enable Slack notifications',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  enableSlackNotifications?: boolean;

  @ApiPropertyOptional({ 
    description: 'Custom notification webhook URL',
    example: 'https://api.example.com/webhooks/approval'
  })
  @IsOptional()
  @IsString()
  webhookUrl?: string;

  // Template and customization
  @ApiPropertyOptional({ 
    description: 'Workflow template to use',
    example: 'design_review'
  })
  @IsOptional()
  @IsString()
  template?: string;

  @ApiPropertyOptional({ 
    description: 'Custom properties object',
    example: { 
      department: 'Design', 
      project_phase: 'MVP',
      stakeholders: ['product', 'engineering', 'design']
    }
  })
  @IsOptional()
  customProperties?: Record<string, any>;
}
