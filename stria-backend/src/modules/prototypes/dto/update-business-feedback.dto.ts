import { PartialType, OmitType } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString, IsDateString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreateBusinessFeedbackDto, BusinessFeedbackStatus } from './create-business-feedback.dto';

/**
 * Update Business Feedback DTO
 * 更新业务反馈数据传输对象
 */
export class UpdateBusinessFeedbackDto extends PartialType(
  OmitType(CreateBusinessFeedbackDto, ['prototypeId'] as const)
) {
  @ApiPropertyOptional({ 
    description: 'Feedback status',
    enum: BusinessFeedbackStatus,
    example: BusinessFeedbackStatus.UNDER_REVIEW
  })
  @IsOptional()
  @IsEnum(BusinessFeedbackStatus)
  status?: BusinessFeedbackStatus;

  @ApiPropertyOptional({ 
    description: 'Resolution notes',
    example: '已按照反馈调整登录页面布局，移动端按钮位置已优化'
  })
  @IsOptional()
  @IsString()
  resolutionNotes?: string;

  @ApiPropertyOptional({ 
    description: 'Resolved date',
    format: 'date-time',
    example: '2024-01-25T14:30:00Z'
  })
  @IsOptional()
  @IsDateString()
  resolvedAt?: string;
}
