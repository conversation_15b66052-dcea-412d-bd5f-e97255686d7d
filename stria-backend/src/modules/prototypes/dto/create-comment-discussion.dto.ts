import { 
  <PERSON>String, 
  IsO<PERSON>al, 
  IsEnum, 
  IsUUID, 
  IsBoolean,
  ValidateNested,
  IsArray,
  IsNumber,
  Min,
  Max
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  DiscussionType, 
  DiscussionStatus, 
  DiscussionPriority,
  CommentType
} from '../enums/comment-discussion.enum';

/**
 * Comment Mention DTO
 * Defines a user mention within a comment
 */
export class CommentMentionDto {
  @ApiProperty({ 
    description: 'Mentioned user ID',
    format: 'uuid',
    example: 'user-123'
  })
  @IsUUID()
  userId: string;

  @ApiProperty({ 
    description: 'Position in text where mention starts',
    example: 15,
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  startPosition: number;

  @ApiProperty({ 
    description: 'Length of the mention text',
    example: 8,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  length: number;

  @ApiPropertyOptional({ 
    description: 'Display name of mentioned user',
    example: '@john.doe'
  })
  @IsOptional()
  @IsString()
  displayName?: string;
}

/**
 * Create Comment Discussion DTO
 * Data transfer object for creating new comment discussions
 */
export class CreateCommentDiscussionDto {
  @ApiProperty({ 
    description: 'Discussion title', 
    example: 'Design feedback for mobile layout',
    maxLength: 255 
  })
  @IsString()
  title: string;

  @ApiPropertyOptional({ 
    description: 'Discussion description', 
    example: 'Let\'s discuss the mobile layout design and gather feedback from all stakeholders' 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: 'Initial comment content', 
    example: 'I think the mobile layout needs some adjustments. What do you all think about the button placement?' 
  })
  @IsString()
  content: string;

  @ApiPropertyOptional({ 
    description: 'Discussion type',
    enum: DiscussionType,
    example: DiscussionType.FEEDBACK
  })
  @IsOptional()
  @IsEnum(DiscussionType)
  type?: DiscussionType;

  @ApiPropertyOptional({ 
    description: 'Discussion status',
    enum: DiscussionStatus,
    example: DiscussionStatus.OPEN
  })
  @IsOptional()
  @IsEnum(DiscussionStatus)
  status?: DiscussionStatus;

  @ApiPropertyOptional({ 
    description: 'Discussion priority',
    enum: DiscussionPriority,
    example: DiscussionPriority.MEDIUM
  })
  @IsOptional()
  @IsEnum(DiscussionPriority)
  priority?: DiscussionPriority;

  @ApiPropertyOptional({ 
    description: 'Comment type for initial comment',
    enum: CommentType,
    example: CommentType.GENERAL
  })
  @IsOptional()
  @IsEnum(CommentType)
  commentType?: CommentType;

  @ApiProperty({ 
    description: 'Prototype ID this discussion belongs to',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  prototypeId: string;

  @ApiPropertyOptional({ 
    description: 'Visual feedback ID if discussion is related to specific feedback',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  visualFeedbackId?: string;

  @ApiPropertyOptional({ 
    description: 'Workflow ID if discussion is related to approval workflow',
    format: 'uuid',
    example: '789e0123-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  workflowId?: string;

  @ApiPropertyOptional({ 
    description: 'Parent discussion ID for nested discussions',
    format: 'uuid',
    example: '012e3456-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  parentDiscussionId?: string;

  @ApiPropertyOptional({ 
    description: 'Array of user mentions in the initial comment',
    type: [CommentMentionDto]
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CommentMentionDto)
  @IsArray()
  mentions?: CommentMentionDto[];

  @ApiPropertyOptional({ 
    description: 'Array of participant user IDs',
    example: ['user-123', 'user-456', 'user-789']
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  participants?: string[];

  @ApiPropertyOptional({ 
    description: 'Array of moderator user IDs',
    example: ['moderator-123', 'moderator-456']
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  moderators?: string[];

  @ApiPropertyOptional({ 
    description: 'Whether discussion is private',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;

  @ApiPropertyOptional({ 
    description: 'Whether discussion allows anonymous comments',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  allowAnonymous?: boolean;

  @ApiPropertyOptional({ 
    description: 'Whether discussion requires moderation',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  requiresModeration?: boolean;

  @ApiPropertyOptional({ 
    description: 'Whether discussion is pinned',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isPinned?: boolean;

  @ApiPropertyOptional({ 
    description: 'Whether discussion is locked',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isLocked?: boolean;

  @ApiPropertyOptional({ 
    description: 'Array of attachment URLs for initial comment',
    example: ['https://example.com/attachments/screenshot.png', 'https://example.com/attachments/document.pdf']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiPropertyOptional({ 
    description: 'Array of tags for categorization',
    example: ['design', 'mobile', 'feedback', 'urgent']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Discussion category',
    example: 'design-review'
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ 
    description: 'Expected resolution time in hours',
    example: 48,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  expectedResolutionHours?: number;

  @ApiPropertyOptional({ 
    description: 'Custom properties object',
    example: { 
      department: 'Design', 
      project_phase: 'Review',
      stakeholders: ['product', 'engineering', 'design']
    }
  })
  @IsOptional()
  customProperties?: Record<string, any>;
}
