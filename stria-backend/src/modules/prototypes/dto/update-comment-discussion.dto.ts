import { PartialType, OmitType } from '@nestjs/swagger';
import { CreateCommentDiscussionDto } from './create-comment-discussion.dto';

/**
 * Update Comment Discussion DTO
 * Data transfer object for updating existing comment discussions
 * Extends CreateCommentDiscussionDto but makes all fields optional and removes prototypeId and content
 */
export class UpdateCommentDiscussionDto extends PartialType(
  OmitType(CreateCommentDiscussionDto, ['prototypeId', 'content'] as const)
) {}
