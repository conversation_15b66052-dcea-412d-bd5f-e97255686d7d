import { IsString, IsOptional, IsUrl, Is<PERSON>rray, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Figma Node DTO
 * Represents a Figma node with its properties
 */
export class FigmaNodeDto {
  @ApiProperty({ description: 'Figma node ID', example: 'node123' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Node name', example: 'Login Screen' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Node type', example: 'FRAME' })
  @IsString()
  type: string;

  @ApiPropertyOptional({ description: 'Node properties', example: { width: 375, height: 812 } })
  @IsOptional()
  @IsObject()
  properties?: Record<string, any>;
}

/**
 * Figma Sync Request DTO
 * Data transfer object for syncing prototype with Figma
 */
export class FigmaSyncDto {
  @ApiProperty({ 
    description: 'Figma file ID to sync with',
    example: 'abc123def456'
  })
  @IsString()
  figmaFileId: string;

  @ApiPropertyOptional({ 
    description: 'Figma file URL',
    example: 'https://www.figma.com/file/abc123def456/Mobile-App-Design'
  })
  @IsOptional()
  @IsUrl()
  figmaFileUrl?: string;

  @ApiPropertyOptional({ 
    description: 'Figma embed URL for iframe integration',
    example: 'https://www.figma.com/embed?embed_host=stria&url=https://www.figma.com/file/abc123def456'
  })
  @IsOptional()
  @IsUrl()
  figmaEmbedUrl?: string;

  @ApiPropertyOptional({ 
    description: 'Specific Figma node IDs to sync',
    example: ['node1', 'node2', 'node3']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  nodeIds?: string[];

  @ApiPropertyOptional({ 
    description: 'Force sync even if already up to date',
    example: false
  })
  @IsOptional()
  forceSync?: boolean;
}

/**
 * Figma Sync Response DTO
 * Response object for Figma sync operations
 */
export class FigmaSyncResponseDto {
  @ApiProperty({ 
    description: 'Sync operation success status',
    example: true
  })
  success: boolean;

  @ApiProperty({ 
    description: 'Sync operation message',
    example: 'Successfully synced with Figma file'
  })
  message: string;

  @ApiPropertyOptional({ 
    description: 'Sync timestamp',
    example: '2024-01-23T10:30:00Z'
  })
  syncedAt?: Date;

  @ApiPropertyOptional({ 
    description: 'Synced Figma nodes',
    type: [FigmaNodeDto]
  })
  @ValidateNested({ each: true })
  @Type(() => FigmaNodeDto)
  syncedNodes?: FigmaNodeDto[];

  @ApiPropertyOptional({ 
    description: 'Number of changes detected',
    example: 5
  })
  changesCount?: number;

  @ApiPropertyOptional({ 
    description: 'Detailed sync information',
    example: {
      addedNodes: ['node4', 'node5'],
      updatedNodes: ['node1', 'node2'],
      removedNodes: ['node3'],
      errors: []
    }
  })
  syncDetails?: {
    addedNodes?: string[];
    updatedNodes?: string[];
    removedNodes?: string[];
    errors?: string[];
    [key: string]: any;
  };

  @ApiPropertyOptional({ 
    description: 'Error message if sync failed',
    example: 'Failed to connect to Figma API'
  })
  error?: string;
}

/**
 * Figma Connection Test DTO
 * For testing Figma API connection
 */
export class FigmaConnectionTestDto {
  @ApiProperty({ 
    description: 'Figma file ID to test connection',
    example: 'abc123def456'
  })
  @IsString()
  figmaFileId: string;
}

/**
 * Figma Connection Test Response DTO
 */
export class FigmaConnectionTestResponseDto {
  @ApiProperty({ 
    description: 'Connection test success status',
    example: true
  })
  connected: boolean;

  @ApiProperty({ 
    description: 'Connection test message',
    example: 'Successfully connected to Figma file'
  })
  message: string;

  @ApiPropertyOptional({ 
    description: 'Figma file information',
    example: {
      name: 'Mobile App Design',
      lastModified: '2024-01-23T09:15:00Z',
      version: '1.2.3'
    }
  })
  fileInfo?: {
    name?: string;
    lastModified?: string;
    version?: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({ 
    description: 'Error message if connection failed',
    example: 'Invalid Figma file ID or access denied'
  })
  error?: string;
}
