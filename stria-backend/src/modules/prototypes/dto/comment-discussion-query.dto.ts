import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString, IsInt, Min, Max, IsDateString, IsBoolean } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { 
  DiscussionType, 
  DiscussionStatus, 
  DiscussionPriority,
  CommentType
} from '../enums/comment-discussion.enum';

/**
 * Comment Discussion Query DTO
 * Data transfer object for querying comment discussions with filters and pagination
 */
export class CommentDiscussionQueryDto {
  // Pagination
  @ApiPropertyOptional({ 
    description: 'Page number (1-based)',
    minimum: 1,
    default: 1,
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ 
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  // Filtering by relationships
  @ApiPropertyOptional({ 
    description: 'Filter by prototype ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  prototypeId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by visual feedback ID',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  visualFeedbackId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by workflow ID',
    format: 'uuid',
    example: '789e0123-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  workflowId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by creator user ID',
    format: 'uuid',
    example: '012e3456-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  createdBy?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by participant user ID',
    format: 'uuid',
    example: '345e6789-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  participantId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by moderator user ID',
    format: 'uuid',
    example: '678e9012-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  moderatorId?: string;

  // Filtering by discussion properties
  @ApiPropertyOptional({ 
    description: 'Filter by discussion type',
    enum: DiscussionType,
    example: DiscussionType.FEEDBACK
  })
  @IsOptional()
  @IsEnum(DiscussionType)
  type?: DiscussionType;

  @ApiPropertyOptional({ 
    description: 'Filter by discussion status',
    enum: DiscussionStatus,
    example: DiscussionStatus.OPEN
  })
  @IsOptional()
  @IsEnum(DiscussionStatus)
  status?: DiscussionStatus;

  @ApiPropertyOptional({ 
    description: 'Filter by discussion priority',
    enum: DiscussionPriority,
    example: DiscussionPriority.HIGH
  })
  @IsOptional()
  @IsEnum(DiscussionPriority)
  priority?: DiscussionPriority;

  // Search
  @ApiPropertyOptional({ 
    description: 'Search in discussion title, description, and comments',
    example: 'mobile layout feedback'
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by tags (comma-separated)',
    example: 'design,mobile,feedback'
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.split(',').map((tag: string) => tag.trim()))
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Filter by category',
    example: 'design-review'
  })
  @IsOptional()
  @IsString()
  category?: string;

  // Activity-based filtering
  @ApiPropertyOptional({ 
    description: 'Filter by minimum comment count',
    example: 5,
    minimum: 0
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  minCommentCount?: number;

  @ApiPropertyOptional({ 
    description: 'Filter by maximum comment count',
    example: 50,
    minimum: 0
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  maxCommentCount?: number;

  @ApiPropertyOptional({ 
    description: 'Filter by minimum participant count',
    example: 2,
    minimum: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  minParticipantCount?: number;

  // Date filtering
  @ApiPropertyOptional({ 
    description: 'Filter discussions created after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  createdAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter discussions created before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  createdBefore?: string;

  @ApiPropertyOptional({ 
    description: 'Filter discussions with activity after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  activityAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter discussions resolved after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  resolvedAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter discussions resolved before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  resolvedBefore?: string;

  // Status-based filters
  @ApiPropertyOptional({ 
    description: 'Only show private discussions',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  privateOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show pinned discussions',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  pinnedOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show locked discussions',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  lockedOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show discussions with solutions',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  withSolutionOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show unresolved discussions',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  unresolvedOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show discussions I participate in',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  myParticipationOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show discussions I moderate',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  myModerationOnly?: boolean = false;

  // Sorting
  @ApiPropertyOptional({ 
    description: 'Sort field',
    enum: ['title', 'createdAt', 'updatedAt', 'lastActivityAt', 'commentCount', 'participantCount', 'priority'],
    default: 'lastActivityAt',
    example: 'lastActivityAt'
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'lastActivityAt';

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['ASC', 'DESC'],
    default: 'DESC',
    example: 'DESC'
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  // Include related data
  @ApiPropertyOptional({ 
    description: 'Include prototype information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includePrototype?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include creator information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeCreator?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include participants in response',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeParticipants?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Include comments in response',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeComments?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Include comment reactions in response',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeReactions?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Include soft-deleted discussions',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDeleted?: boolean = false;
}
