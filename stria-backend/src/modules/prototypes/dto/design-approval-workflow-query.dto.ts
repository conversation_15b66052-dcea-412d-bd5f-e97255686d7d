import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString, IsInt, Min, Max, IsDateString, IsBoolean } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { 
  WorkflowType, 
  WorkflowStatus, 
  ApprovalStrategy,
  WorkflowPriority
} from '../enums/design-approval-workflow.enum';
import { StepStatus } from '../enums/approval-step.enum';

/**
 * Design Approval Workflow Query DTO
 * Data transfer object for querying approval workflows with filters and pagination
 */
export class DesignApprovalWorkflowQueryDto {
  // Pagination
  @ApiPropertyOptional({ 
    description: 'Page number (1-based)',
    minimum: 1,
    default: 1,
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ 
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  // Filtering by relationships
  @ApiPropertyOptional({ 
    description: 'Filter by prototype ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  prototypeId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by creator user ID',
    format: 'uuid',
    example: '789e0123-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  createdBy?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by assigned user ID',
    format: 'uuid',
    example: '012e3456-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  assignedTo?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by completed user ID',
    format: 'uuid',
    example: '345e6789-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  completedBy?: string;

  // Filtering by workflow properties
  @ApiPropertyOptional({ 
    description: 'Filter by workflow type',
    enum: WorkflowType,
    example: WorkflowType.SEQUENTIAL
  })
  @IsOptional()
  @IsEnum(WorkflowType)
  type?: WorkflowType;

  @ApiPropertyOptional({ 
    description: 'Filter by workflow status',
    enum: WorkflowStatus,
    example: WorkflowStatus.IN_PROGRESS
  })
  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus;

  @ApiPropertyOptional({ 
    description: 'Filter by approval strategy',
    enum: ApprovalStrategy,
    example: ApprovalStrategy.ALL_REQUIRED
  })
  @IsOptional()
  @IsEnum(ApprovalStrategy)
  approvalStrategy?: ApprovalStrategy;

  @ApiPropertyOptional({ 
    description: 'Filter by workflow priority',
    enum: WorkflowPriority,
    example: WorkflowPriority.HIGH
  })
  @IsOptional()
  @IsEnum(WorkflowPriority)
  priority?: WorkflowPriority;

  // Search
  @ApiPropertyOptional({ 
    description: 'Search in workflow name and description',
    example: 'mobile design approval'
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by template',
    example: 'design_review'
  })
  @IsOptional()
  @IsString()
  template?: string;

  // Progress-based filtering
  @ApiPropertyOptional({ 
    description: 'Filter by minimum completion percentage',
    example: 50,
    minimum: 0,
    maximum: 100
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @Max(100)
  minCompletionPercentage?: number;

  @ApiPropertyOptional({ 
    description: 'Filter by maximum completion percentage',
    example: 90,
    minimum: 0,
    maximum: 100
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @Max(100)
  maxCompletionPercentage?: number;

  @ApiPropertyOptional({ 
    description: 'Filter by current step number',
    example: 2,
    minimum: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  currentStep?: number;

  // Date filtering
  @ApiPropertyOptional({ 
    description: 'Filter workflows created after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  createdAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter workflows created before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  createdBefore?: string;

  @ApiPropertyOptional({ 
    description: 'Filter workflows due after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  dueAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter workflows due before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  dueBefore?: string;

  @ApiPropertyOptional({ 
    description: 'Filter workflows completed after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  completedAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter workflows completed before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  completedBefore?: string;

  // Status-based filters
  @ApiPropertyOptional({ 
    description: 'Only show overdue workflows',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  overdueOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show active workflows',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  activeOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show completed workflows',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  completedOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show workflows pending my approval',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  pendingMyApproval?: boolean = false;

  // Sorting
  @ApiPropertyOptional({ 
    description: 'Sort field',
    enum: ['name', 'createdAt', 'updatedAt', 'dueDate', 'priority', 'status', 'completionPercentage'],
    default: 'createdAt',
    example: 'createdAt'
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['ASC', 'DESC'],
    default: 'DESC',
    example: 'DESC'
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  // Include related data
  @ApiPropertyOptional({ 
    description: 'Include prototype information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includePrototype?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include creator information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeCreator?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include assignee information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeAssignee?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include workflow steps in response',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeSteps?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Include approval actions in response',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeActions?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Include soft-deleted workflows',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDeleted?: boolean = false;
}
