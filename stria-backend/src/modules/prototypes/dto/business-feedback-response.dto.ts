import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { 
  BusinessFeedbackType, 
  BusinessFeedbackStatus, 
  BusinessFeedbackPriority 
} from './create-business-feedback.dto';
import { UserResponseDto, PrototypeSummaryDto } from './visual-feedback-response.dto';

/**
 * Business Feedback Response DTO
 * 业务反馈响应数据传输对象
 */
export class BusinessFeedbackResponseDto {
  @ApiProperty({ description: 'Feedback ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Feedback title' })
  @Expose()
  title: string;

  @ApiProperty({ description: 'Feedback description' })
  @Expose()
  description: string;

  @ApiProperty({ description: 'Feedback type', enum: BusinessFeedbackType })
  @Expose()
  type: BusinessFeedbackType;

  @ApiProperty({ description: 'Feedback priority', enum: BusinessFeedbackPriority })
  @Expose()
  priority: BusinessFeedbackPriority;

  @ApiProperty({ description: 'Feedback status', enum: BusinessFeedbackStatus })
  @Expose()
  status: BusinessFeedbackStatus;

  @ApiProperty({ description: 'Associated prototype', type: PrototypeSummaryDto })
  @Expose()
  @Type(() => PrototypeSummaryDto)
  prototype: PrototypeSummaryDto;

  @ApiProperty({ description: 'Feedback submitter', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  submittedBy: UserResponseDto;

  @ApiPropertyOptional({ description: 'Assigned user', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  assignedTo?: UserResponseDto;

  @ApiPropertyOptional({ description: 'User who resolved feedback', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  resolvedBy?: UserResponseDto;

  @ApiPropertyOptional({ description: 'Related workflow step ID' })
  @Expose()
  workflowStepId?: string;

  @ApiPropertyOptional({ description: 'Related milestone ID' })
  @Expose()
  milestoneId?: string;

  @ApiPropertyOptional({ description: 'Due date for addressing feedback' })
  @Expose()
  dueDate?: string;

  @ApiPropertyOptional({ description: 'Figma page URL reference' })
  @Expose()
  figmaPageUrl?: string;

  @ApiPropertyOptional({ description: 'Screen or page reference' })
  @Expose()
  screenReference?: string;

  @ApiPropertyOptional({ description: 'Resolution notes' })
  @Expose()
  resolutionNotes?: string;

  @ApiProperty({ description: 'Feedback metadata' })
  @Expose()
  metadata: {
    attachments?: string[];
    relatedFeedbackIds?: string[];
    estimatedDelay?: number;
    resourceRequirement?: string;
    riskLevel?: string;
    customProperties?: Record<string, any>;
    [key: string]: any;
  };

  @ApiProperty({ description: 'Submission timestamp' })
  @Expose()
  submittedAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Resolution timestamp' })
  @Expose()
  resolvedAt?: Date;

  @ApiPropertyOptional({ description: 'Soft delete timestamp' })
  @Expose()
  deletedAt?: Date;
}

/**
 * Business Feedback Summary DTO
 * 业务反馈摘要数据传输对象
 */
export class BusinessFeedbackSummaryDto {
  @ApiProperty({ description: 'Feedback ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Feedback title' })
  @Expose()
  title: string;

  @ApiProperty({ description: 'Feedback type', enum: BusinessFeedbackType })
  @Expose()
  type: BusinessFeedbackType;

  @ApiProperty({ description: 'Feedback priority', enum: BusinessFeedbackPriority })
  @Expose()
  priority: BusinessFeedbackPriority;

  @ApiProperty({ description: 'Feedback status', enum: BusinessFeedbackStatus })
  @Expose()
  status: BusinessFeedbackStatus;

  @ApiProperty({ description: 'Prototype name' })
  @Expose()
  prototypeName: string;

  @ApiProperty({ description: 'Submitter name' })
  @Expose()
  submitterName: string;

  @ApiPropertyOptional({ description: 'Assignee name' })
  @Expose()
  assigneeName?: string;

  @ApiPropertyOptional({ description: 'Due date' })
  @Expose()
  dueDate?: string;

  @ApiProperty({ description: 'Submission timestamp' })
  @Expose()
  submittedAt: Date;

  @ApiProperty({ description: 'Days since submission' })
  @Expose()
  daysSinceSubmission: number;

  @ApiProperty({ description: 'Is overdue' })
  @Expose()
  isOverdue: boolean;
}
