import { 
  IsString, 
  IsOptional, 
  IsEnum, 
  IsUUID, 
  Is<PERSON><PERSON>y,
  IsDateString,
  ValidateNested
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Business Feedback Types - 业务导向的反馈分类
 */
export enum BusinessFeedbackType {
  APPROVAL_DECISION = 'approval_decision',
  CHANGE_REQUEST = 'change_request', 
  CLARIFICATION_NEEDED = 'clarification_needed',
  MILESTONE_FEEDBACK = 'milestone_feedback'
}

/**
 * Business Feedback Priority - 业务优先级
 */
export enum BusinessFeedbackPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  BLOCKING = 'blocking'
}

/**
 * Business Feedback Status - 业务状态
 */
export enum BusinessFeedbackStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  CHANGES_REQUESTED = 'changes_requested',
  RESOLVED = 'resolved'
}

/**
 * Create Business Feedback DTO
 * 轻量级反馈创建数据传输对象
 */
export class CreateBusinessFeedbackDto {
  @ApiProperty({ 
    description: 'Feedback title', 
    example: '登录页面布局需要调整',
    maxLength: 255 
  })
  @IsString()
  title: string;

  @ApiProperty({ 
    description: 'Detailed feedback description', 
    example: '当前登录页面在移动端显示时，按钮位置过于靠下，建议调整到更容易点击的位置' 
  })
  @IsString()
  description: string;

  @ApiProperty({ 
    description: 'Business feedback type',
    enum: BusinessFeedbackType,
    example: BusinessFeedbackType.CHANGE_REQUEST
  })
  @IsEnum(BusinessFeedbackType)
  type: BusinessFeedbackType;

  @ApiProperty({ 
    description: 'Feedback priority',
    enum: BusinessFeedbackPriority,
    example: BusinessFeedbackPriority.MEDIUM
  })
  @IsEnum(BusinessFeedbackPriority)
  priority: BusinessFeedbackPriority;

  @ApiProperty({ 
    description: 'Prototype ID this feedback belongs to',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  prototypeId: string;

  @ApiPropertyOptional({ 
    description: 'Related workflow step ID',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  workflowStepId?: string;

  @ApiPropertyOptional({ 
    description: 'Related milestone ID',
    format: 'uuid',
    example: '789e0123-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  milestoneId?: string;

  @ApiPropertyOptional({ 
    description: 'User ID to assign this feedback to',
    format: 'uuid',
    example: '012e3456-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  assignedTo?: string;

  @ApiPropertyOptional({ 
    description: 'Due date for addressing this feedback',
    format: 'date-time',
    example: '2024-02-01T17:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiPropertyOptional({ 
    description: 'Figma page URL for reference',
    example: 'https://www.figma.com/file/abc123/Design?node-id=1%3A2'
  })
  @IsOptional()
  @IsString()
  figmaPageUrl?: string;

  @ApiPropertyOptional({ 
    description: 'Screen or page reference',
    example: '首页 > 登录模块'
  })
  @IsOptional()
  @IsString()
  screenReference?: string;

  @ApiPropertyOptional({ 
    description: 'Array of attachment URLs',
    example: ['https://example.com/attachments/reference.png', 'https://example.com/attachments/mockup.pdf']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiPropertyOptional({ 
    description: 'Array of related feedback IDs',
    example: ['feedback-123', 'feedback-456']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  relatedFeedbackIds?: string[];

  @ApiPropertyOptional({ 
    description: 'Estimated delay in days if this feedback requires changes',
    example: 3
  })
  @IsOptional()
  estimatedDelay?: number;

  @ApiPropertyOptional({ 
    description: 'Additional resource requirements',
    example: '需要UI设计师额外2天时间'
  })
  @IsOptional()
  @IsString()
  resourceRequirement?: string;

  @ApiPropertyOptional({ 
    description: 'Risk level assessment',
    enum: ['low', 'medium', 'high'],
    example: 'medium'
  })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high'])
  riskLevel?: string;

  @ApiPropertyOptional({ 
    description: 'Custom properties object',
    example: { 
      clientDepartment: 'Marketing',
      reviewRound: 2,
      stakeholders: ['product', 'marketing', 'legal']
    }
  })
  @IsOptional()
  customProperties?: Record<string, any>;
}
