import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { 
  FeedbackType, 
  FeedbackStatus, 
  FeedbackPriority, 
  AnnotationType 
} from '../enums/visual-feedback.enum';
import { UserResponseDto } from './prototype-response.dto';

/**
 * Prototype Summary DTO for feedback responses
 */
export class PrototypeSummaryDto {
  @ApiProperty({ description: 'Prototype ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Prototype name', example: 'Mobile App Wireframes' })
  @Expose()
  name: string;

  @ApiProperty({ description: 'Current version', example: '2.1.0' })
  @Expose()
  currentVersion: string;
}

/**
 * Version Summary DTO for feedback responses
 */
export class VersionSummaryDto {
  @ApiProperty({ description: 'Version ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Version number', example: '2.1.0' })
  @Expose()
  versionNumber: string;

  @ApiPropertyOptional({ description: 'Version name', example: 'Mobile Redesign v2.1' })
  @Expose()
  versionName?: string;
}

/**
 * Visual Feedback Response DTO
 * Data transfer object for visual feedback API responses
 */
export class VisualFeedbackResponseDto {
  @ApiProperty({ 
    description: 'Feedback ID', 
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Expose()
  id: string;

  @ApiPropertyOptional({ 
    description: 'Feedback title', 
    example: 'Button alignment issue' 
  })
  @Expose()
  title?: string;

  @ApiProperty({ 
    description: 'Feedback content', 
    example: 'The submit button appears to be misaligned with the form fields.' 
  })
  @Expose()
  content: string;

  @ApiProperty({ 
    description: 'Feedback type',
    enum: FeedbackType,
    example: FeedbackType.DESIGN_ISSUE
  })
  @Expose()
  type: FeedbackType;

  @ApiProperty({ 
    description: 'Feedback status',
    enum: FeedbackStatus,
    example: FeedbackStatus.OPEN
  })
  @Expose()
  status: FeedbackStatus;

  @ApiProperty({ 
    description: 'Feedback priority',
    enum: FeedbackPriority,
    example: FeedbackPriority.MEDIUM
  })
  @Expose()
  priority: FeedbackPriority;

  @ApiProperty({ 
    description: 'Annotation type',
    enum: AnnotationType,
    example: AnnotationType.RECTANGLE
  })
  @Expose()
  annotationType: AnnotationType;

  // Position and dimensions
  @ApiProperty({ 
    description: 'X coordinate position',
    example: 150.5
  })
  @Expose()
  positionX: number;

  @ApiProperty({ 
    description: 'Y coordinate position',
    example: 200.75
  })
  @Expose()
  positionY: number;

  @ApiProperty({ 
    description: 'Z-index for layering',
    example: 1
  })
  @Expose()
  positionZ: number;

  @ApiPropertyOptional({ 
    description: 'Annotation width',
    example: 100.0
  })
  @Expose()
  width?: number;

  @ApiPropertyOptional({ 
    description: 'Annotation height',
    example: 50.0
  })
  @Expose()
  height?: number;

  // Visual styling
  @ApiProperty({ 
    description: 'Annotation color',
    example: '#FF6B6B'
  })
  @Expose()
  color: string;

  @ApiPropertyOptional({ 
    description: 'Border color',
    example: '#000000'
  })
  @Expose()
  borderColor?: string;

  @ApiPropertyOptional({ 
    description: 'Background color',
    example: '#FFFFFF'
  })
  @Expose()
  backgroundColor?: string;

  @ApiProperty({ 
    description: 'Opacity level',
    example: 0.8
  })
  @Expose()
  opacity: number;

  // Target element information
  @ApiPropertyOptional({ 
    description: 'Target element ID',
    example: 'button-submit'
  })
  @Expose()
  targetElementId?: string;

  @ApiPropertyOptional({ 
    description: 'Target element type',
    example: 'button'
  })
  @Expose()
  targetElementType?: string;

  @ApiPropertyOptional({ 
    description: 'Target element name',
    example: 'Submit Button'
  })
  @Expose()
  targetElementName?: string;

  // Relationships
  @ApiProperty({ 
    description: 'Associated prototype information',
    type: PrototypeSummaryDto
  })
  @Expose()
  @Type(() => PrototypeSummaryDto)
  prototype: PrototypeSummaryDto;

  @ApiPropertyOptional({ 
    description: 'Associated version information',
    type: VersionSummaryDto
  })
  @Expose()
  @Type(() => VersionSummaryDto)
  version?: VersionSummaryDto;

  @ApiProperty({ 
    description: 'Feedback creator information',
    type: UserResponseDto
  })
  @Expose()
  @Type(() => UserResponseDto)
  creator: UserResponseDto;

  @ApiPropertyOptional({ 
    description: 'Assigned user information',
    type: UserResponseDto
  })
  @Expose()
  @Type(() => UserResponseDto)
  assignee?: UserResponseDto;

  @ApiPropertyOptional({ 
    description: 'Resolver information',
    type: UserResponseDto
  })
  @Expose()
  @Type(() => UserResponseDto)
  resolver?: UserResponseDto;

  @ApiPropertyOptional({ 
    description: 'Resolution timestamp',
    example: '2024-01-25T14:30:00Z'
  })
  @Expose()
  resolvedAt?: Date;

  @ApiProperty({ 
    description: 'Feedback metadata',
    example: {
      screenshot: 'https://example.com/screenshots/feedback-123.png',
      attachments: ['file1.pdf', 'file2.png'],
      tags: ['ui', 'critical'],
      category: 'usability',
      severity: 'high',
      estimatedEffort: 2.5,
      relatedFeedbacks: ['feedback-456'],
      customFields: { browser: 'Chrome', device: 'Desktop' }
    }
  })
  @Expose()
  metadata: {
    screenshot?: string;
    attachments?: string[];
    tags?: string[];
    category?: string;
    severity?: string;
    estimatedEffort?: number;
    relatedFeedbacks?: string[];
    customFields?: Record<string, any>;
    [key: string]: any;
  };

  @ApiProperty({ 
    description: 'Creation timestamp',
    example: '2024-01-20T10:30:00Z'
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({ 
    description: 'Last update timestamp',
    example: '2024-01-23T15:45:00Z'
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({ 
    description: 'Soft delete timestamp',
    example: '2024-01-25T09:15:00Z'
  })
  @Expose()
  deletedAt?: Date;
}
