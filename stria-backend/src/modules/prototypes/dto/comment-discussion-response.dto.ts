import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { 
  DiscussionType, 
  DiscussionStatus, 
  DiscussionPriority,
  CommentType,
  ReactionType
} from '../enums/comment-discussion.enum';
import { UserResponseDto, PrototypeSummaryDto } from './visual-feedback-response.dto';

/**
 * Comment Mention Response DTO
 */
export class CommentMentionResponseDto {
  @ApiProperty({ description: 'Mention ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Mentioned user', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  user: UserResponseDto;

  @ApiProperty({ description: 'Position in text' })
  @Expose()
  startPosition: number;

  @ApiProperty({ description: 'Length of mention text' })
  @Expose()
  length: number;

  @ApiPropertyOptional({ description: 'Display name' })
  @Expose()
  displayName?: string;

  @ApiProperty({ description: 'Creation timestamp' })
  @Expose()
  createdAt: Date;
}

/**
 * Comment Reaction Response DTO
 */
export class CommentReactionResponseDto {
  @ApiProperty({ description: 'Reaction ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Reaction type', enum: ReactionType })
  @Expose()
  type: ReactionType;

  @ApiProperty({ description: 'User who reacted', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  user: UserResponseDto;

  @ApiPropertyOptional({ description: 'Reaction note' })
  @Expose()
  note?: string;

  @ApiProperty({ description: 'Whether reaction is active' })
  @Expose()
  isActive: boolean;

  @ApiProperty({ description: 'Creation timestamp' })
  @Expose()
  createdAt: Date;
}

/**
 * Comment Response DTO
 */
export class CommentResponseDto {
  @ApiProperty({ description: 'Comment ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Comment content' })
  @Expose()
  content: string;

  @ApiProperty({ description: 'Comment type', enum: CommentType })
  @Expose()
  type: CommentType;

  @ApiProperty({ description: 'Thread level (0 for root comments)' })
  @Expose()
  threadLevel: number;

  @ApiProperty({ description: 'Comment author', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  author: UserResponseDto;

  @ApiPropertyOptional({ description: 'Parent comment ID' })
  @Expose()
  parentCommentId?: string;

  @ApiProperty({ description: 'Whether comment is anonymous' })
  @Expose()
  isAnonymous: boolean;

  @ApiProperty({ description: 'Whether comment is private' })
  @Expose()
  isPrivate: boolean;

  @ApiProperty({ description: 'Whether comment is marked as solution' })
  @Expose()
  isSolution: boolean;

  @ApiProperty({ description: 'Whether comment is edited' })
  @Expose()
  isEdited: boolean;

  @ApiProperty({ description: 'Whether comment is flagged' })
  @Expose()
  isFlagged: boolean;

  @ApiProperty({ description: 'Upvote count' })
  @Expose()
  upvoteCount: number;

  @ApiProperty({ description: 'Downvote count' })
  @Expose()
  downvoteCount: number;

  @ApiProperty({ description: 'Total reaction count' })
  @Expose()
  reactionCount: number;

  @ApiProperty({ description: 'Reply count' })
  @Expose()
  replyCount: number;

  @ApiProperty({ description: 'Comment mentions', type: [CommentMentionResponseDto] })
  @Expose()
  @Type(() => CommentMentionResponseDto)
  mentions: CommentMentionResponseDto[];

  @ApiProperty({ description: 'Comment reactions', type: [CommentReactionResponseDto] })
  @Expose()
  @Type(() => CommentReactionResponseDto)
  reactions: CommentReactionResponseDto[];

  @ApiProperty({ description: 'Comment metadata' })
  @Expose()
  metadata: {
    attachments?: string[];
    tags?: string[];
    estimatedHours?: number;
    impactLevel?: string;
    confidenceLevel?: number;
    relatedComments?: string[];
    referencedFeedback?: string[];
    customProperties?: Record<string, any>;
    [key: string]: any;
  };

  @ApiProperty({ description: 'Creation timestamp' })
  @Expose()
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Last edit timestamp' })
  @Expose()
  lastEditedAt?: Date;

  @ApiPropertyOptional({ description: 'Soft delete timestamp' })
  @Expose()
  deletedAt?: Date;
}

/**
 * Comment Discussion Response DTO
 */
export class CommentDiscussionResponseDto {
  @ApiProperty({ description: 'Discussion ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Discussion title' })
  @Expose()
  title: string;

  @ApiPropertyOptional({ description: 'Discussion description' })
  @Expose()
  description?: string;

  @ApiProperty({ description: 'Discussion type', enum: DiscussionType })
  @Expose()
  type: DiscussionType;

  @ApiProperty({ description: 'Discussion status', enum: DiscussionStatus })
  @Expose()
  status: DiscussionStatus;

  @ApiProperty({ description: 'Discussion priority', enum: DiscussionPriority })
  @Expose()
  priority: DiscussionPriority;

  @ApiProperty({ description: 'Total comment count' })
  @Expose()
  commentCount: number;

  @ApiProperty({ description: 'Participant count' })
  @Expose()
  participantCount: number;

  @ApiProperty({ description: 'Whether discussion is private' })
  @Expose()
  isPrivate: boolean;

  @ApiProperty({ description: 'Whether discussion allows anonymous comments' })
  @Expose()
  allowAnonymous: boolean;

  @ApiProperty({ description: 'Whether discussion requires moderation' })
  @Expose()
  requiresModeration: boolean;

  @ApiProperty({ description: 'Whether discussion is pinned' })
  @Expose()
  isPinned: boolean;

  @ApiProperty({ description: 'Whether discussion is locked' })
  @Expose()
  isLocked: boolean;

  @ApiProperty({ description: 'Whether discussion has solution' })
  @Expose()
  hasSolution: boolean;

  @ApiProperty({ description: 'Associated prototype', type: PrototypeSummaryDto })
  @Expose()
  @Type(() => PrototypeSummaryDto)
  prototype: PrototypeSummaryDto;

  @ApiProperty({ description: 'Discussion creator', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  creator: UserResponseDto;

  @ApiPropertyOptional({ description: 'User who resolved discussion', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  resolvedBy?: UserResponseDto;

  @ApiPropertyOptional({ description: 'Resolution timestamp' })
  @Expose()
  resolvedAt?: Date;

  @ApiPropertyOptional({ description: 'User who closed discussion', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  closedBy?: UserResponseDto;

  @ApiPropertyOptional({ description: 'Close timestamp' })
  @Expose()
  closedAt?: Date;

  @ApiProperty({ description: 'Discussion participants', type: [UserResponseDto] })
  @Expose()
  @Type(() => UserResponseDto)
  participants: UserResponseDto[];

  @ApiProperty({ description: 'Discussion moderators', type: [UserResponseDto] })
  @Expose()
  @Type(() => UserResponseDto)
  moderators: UserResponseDto[];

  @ApiProperty({ description: 'Discussion comments', type: [CommentResponseDto] })
  @Expose()
  @Type(() => CommentResponseDto)
  comments: CommentResponseDto[];

  @ApiProperty({ description: 'Discussion metadata' })
  @Expose()
  metadata: {
    tags?: string[];
    category?: string;
    expectedResolutionHours?: number;
    customProperties?: Record<string, any>;
    [key: string]: any;
  };

  @ApiProperty({ description: 'Creation timestamp' })
  @Expose()
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Last activity timestamp' })
  @Expose()
  lastActivityAt?: Date;

  @ApiPropertyOptional({ description: 'Soft delete timestamp' })
  @Expose()
  deletedAt?: Date;
}
