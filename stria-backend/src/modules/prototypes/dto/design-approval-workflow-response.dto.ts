import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { 
  WorkflowType, 
  WorkflowStatus, 
  ApprovalStrategy,
  WorkflowPriority
} from '../enums/design-approval-workflow.enum';
import { 
  StepType, 
  StepStatus 
} from '../enums/approval-step.enum';
import { 
  ActionType, 
  ApprovalDecision 
} from '../enums/approval-action.enum';
import { UserResponseDto } from './prototype-response.dto';
import { PrototypeSummaryDto } from './visual-feedback-response.dto';

/**
 * Approval Action Response DTO
 */
export class ApprovalActionResponseDto {
  @ApiProperty({ description: 'Action ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Action type', enum: ActionType })
  @Expose()
  type: ActionType;

  @ApiProperty({ description: 'Approval decision', enum: ApprovalDecision })
  @Expose()
  decision: ApprovalDecision;

  @ApiPropertyOptional({ description: 'Comment or feedback' })
  @Expose()
  comment?: string;

  @ApiPropertyOptional({ description: 'Reason for decision' })
  @Expose()
  reason?: string;

  @ApiProperty({ description: 'Whether approval is conditional' })
  @Expose()
  isConditional: boolean;

  @ApiPropertyOptional({ description: 'Conditions array' })
  @Expose()
  conditions?: any[];

  @ApiProperty({ description: 'User who took the action', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  user: UserResponseDto;

  @ApiPropertyOptional({ description: 'User who delegated (if applicable)', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  delegatedBy?: UserResponseDto;

  @ApiProperty({ description: 'Action metadata' })
  @Expose()
  metadata: {
    attachments?: string[];
    referencedFeedback?: string[];
    tags?: string[];
    qualityScore?: number;
    usabilityScore?: number;
    brandComplianceScore?: number;
    customProperties?: Record<string, any>;
    [key: string]: any;
  };

  @ApiProperty({ description: 'Creation timestamp' })
  @Expose()
  createdAt: Date;
}

/**
 * Approval Step Response DTO
 */
export class ApprovalStepResponseDto {
  @ApiProperty({ description: 'Step ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Step number' })
  @Expose()
  stepNumber: number;

  @ApiProperty({ description: 'Step name' })
  @Expose()
  stepName: string;

  @ApiPropertyOptional({ description: 'Step description' })
  @Expose()
  description?: string;

  @ApiProperty({ description: 'Step type', enum: StepType })
  @Expose()
  type: StepType;

  @ApiProperty({ description: 'Step status', enum: StepStatus })
  @Expose()
  status: StepStatus;

  @ApiProperty({ description: 'Whether step is required' })
  @Expose()
  isRequired: boolean;

  @ApiProperty({ description: 'Whether step can be skipped' })
  @Expose()
  allowSkip: boolean;

  @ApiProperty({ description: 'Number of required approvers' })
  @Expose()
  requiredApprovers: number;

  @ApiPropertyOptional({ description: 'Step due date' })
  @Expose()
  dueDate?: Date;

  @ApiPropertyOptional({ description: 'Estimated hours' })
  @Expose()
  estimatedHours?: number;

  @ApiPropertyOptional({ description: 'Actual hours spent' })
  @Expose()
  actualHours?: number;

  @ApiProperty({ description: 'Dependencies on other steps' })
  @Expose()
  dependsOnSteps: number[];

  @ApiProperty({ description: 'Steps blocked by this step' })
  @Expose()
  blocksSteps: number[];

  @ApiProperty({ description: 'Current approval count' })
  @Expose()
  approvalCount: number;

  @ApiProperty({ description: 'Current rejection count' })
  @Expose()
  rejectionCount: number;

  @ApiPropertyOptional({ description: 'Final decision', enum: ApprovalDecision })
  @Expose()
  finalDecision?: ApprovalDecision;

  @ApiPropertyOptional({ description: 'Assigned user', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  assignee?: UserResponseDto;

  @ApiPropertyOptional({ description: 'User who completed step', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  completedBy?: UserResponseDto;

  @ApiPropertyOptional({ description: 'Completion timestamp' })
  @Expose()
  completedAt?: Date;

  @ApiProperty({ description: 'Step actions', type: [ApprovalActionResponseDto] })
  @Expose()
  @Type(() => ApprovalActionResponseDto)
  actions: ApprovalActionResponseDto[];

  @ApiProperty({ description: 'Creation timestamp' })
  @Expose()
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Expose()
  updatedAt: Date;
}

/**
 * Design Approval Workflow Response DTO
 */
export class DesignApprovalWorkflowResponseDto {
  @ApiProperty({ description: 'Workflow ID', format: 'uuid' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Workflow name' })
  @Expose()
  name: string;

  @ApiPropertyOptional({ description: 'Workflow description' })
  @Expose()
  description?: string;

  @ApiProperty({ description: 'Workflow type', enum: WorkflowType })
  @Expose()
  type: WorkflowType;

  @ApiProperty({ description: 'Workflow status', enum: WorkflowStatus })
  @Expose()
  status: WorkflowStatus;

  @ApiProperty({ description: 'Approval strategy', enum: ApprovalStrategy })
  @Expose()
  approvalStrategy: ApprovalStrategy;

  @ApiProperty({ description: 'Required approvals count' })
  @Expose()
  requiredApprovals: number;

  @ApiProperty({ description: 'Allow parallel approval' })
  @Expose()
  allowParallelApproval: boolean;

  @ApiProperty({ description: 'Auto-approve on threshold' })
  @Expose()
  autoApproveOnThreshold: boolean;

  @ApiProperty({ description: 'Approval threshold percentage' })
  @Expose()
  approvalThresholdPercentage: number;

  @ApiPropertyOptional({ description: 'Workflow due date' })
  @Expose()
  dueDate?: Date;

  @ApiProperty({ description: 'Reminder interval in hours' })
  @Expose()
  reminderIntervalHours: number;

  @ApiPropertyOptional({ description: 'Escalation hours' })
  @Expose()
  escalationHours?: number;

  @ApiProperty({ description: 'Workflow priority', enum: WorkflowPriority })
  @Expose()
  priority: WorkflowPriority;

  @ApiProperty({ description: 'Current step number' })
  @Expose()
  currentStep: number;

  @ApiProperty({ description: 'Total steps count' })
  @Expose()
  totalSteps: number;

  @ApiProperty({ description: 'Completed steps count' })
  @Expose()
  completedSteps: number;

  @ApiProperty({ description: 'Approval percentage' })
  @Expose()
  approvalPercentage: number;

  @ApiProperty({ description: 'Associated prototype', type: PrototypeSummaryDto })
  @Expose()
  @Type(() => PrototypeSummaryDto)
  prototype: PrototypeSummaryDto;

  @ApiProperty({ description: 'Workflow creator', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  creator: UserResponseDto;

  @ApiPropertyOptional({ description: 'Assigned user', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  assignee?: UserResponseDto;

  @ApiPropertyOptional({ description: 'User who completed workflow', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  completedBy?: UserResponseDto;

  @ApiPropertyOptional({ description: 'Completion timestamp' })
  @Expose()
  completedAt?: Date;

  @ApiPropertyOptional({ description: 'User who escalated', type: UserResponseDto })
  @Expose()
  @Type(() => UserResponseDto)
  escalatedTo?: UserResponseDto;

  @ApiPropertyOptional({ description: 'Escalation timestamp' })
  @Expose()
  escalatedAt?: Date;

  @ApiProperty({ description: 'Workflow steps', type: [ApprovalStepResponseDto] })
  @Expose()
  @Type(() => ApprovalStepResponseDto)
  steps: ApprovalStepResponseDto[];

  @ApiProperty({ description: 'Workflow metadata' })
  @Expose()
  metadata: {
    enableEmailNotifications?: boolean;
    enableSlackNotifications?: boolean;
    webhookUrl?: string;
    template?: string;
    customProperties?: Record<string, any>;
    [key: string]: any;
  };

  @ApiProperty({ description: 'Creation timestamp' })
  @Expose()
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Start timestamp' })
  @Expose()
  startedAt?: Date;

  @ApiPropertyOptional({ description: 'Soft delete timestamp' })
  @Expose()
  deletedAt?: Date;
}
