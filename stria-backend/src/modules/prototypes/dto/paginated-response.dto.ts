import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * Pagination Meta DTO
 * Contains pagination metadata information
 */
export class PaginationMetaDto {
  @ApiProperty({ description: 'Current page number', example: 1 })
  @Expose()
  page: number;

  @ApiProperty({ description: 'Number of items per page', example: 20 })
  @Expose()
  limit: number;

  @ApiProperty({ description: 'Total number of items', example: 150 })
  @Expose()
  total: number;

  @ApiProperty({ description: 'Total number of pages', example: 8 })
  @Expose()
  totalPages: number;

  @ApiProperty({ description: 'Whether there is a next page', example: true })
  @Expose()
  hasNext: boolean;

  @ApiProperty({ description: 'Whether there is a previous page', example: false })
  @Expose()
  hasPrev: boolean;

  @ApiProperty({ description: 'Next page number (if exists)', example: 2, required: false })
  @Expose()
  nextPage?: number;

  @ApiProperty({ description: 'Previous page number (if exists)', required: false })
  @Expose()
  prevPage?: number;
}

/**
 * Generic Paginated Response DTO
 * Template for paginated API responses
 */
export class PaginatedResponseDto<T> {
  @ApiProperty({ description: 'Array of items for current page' })
  @Expose()
  data: T[];

  @ApiProperty({ description: 'Pagination metadata', type: PaginationMetaDto })
  @Expose()
  @Type(() => PaginationMetaDto)
  meta: PaginationMetaDto;

  constructor(data: T[], meta: PaginationMetaDto) {
    this.data = data;
    this.meta = meta;
  }

  /**
   * Create a paginated response with calculated metadata
   */
  static create<T>(
    data: T[],
    total: number,
    page: number,
    limit: number,
  ): PaginatedResponseDto<T> {
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const meta: PaginationMetaDto = {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev,
      nextPage: hasNext ? page + 1 : undefined,
      prevPage: hasPrev ? page - 1 : undefined,
    };

    return new PaginatedResponseDto(data, meta);
  }
}
