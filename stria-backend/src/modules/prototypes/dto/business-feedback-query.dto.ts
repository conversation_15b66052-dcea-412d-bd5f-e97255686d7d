import { <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>, IsU<PERSON>D, IsString, IsInt, Min, Max, IsDateString, IsBoolean } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { 
  BusinessFeedbackType, 
  BusinessFeedbackStatus, 
  BusinessFeedbackPriority 
} from './create-business-feedback.dto';

/**
 * Business Feedback Query DTO
 * 业务反馈查询数据传输对象
 */
export class BusinessFeedbackQueryDto {
  // Pagination
  @ApiPropertyOptional({ 
    description: 'Page number (1-based)',
    minimum: 1,
    default: 1,
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ 
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  // Filtering by relationships
  @ApiPropertyOptional({ 
    description: 'Filter by prototype ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  prototypeId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by submitter user ID',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  submittedBy?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by assigned user ID',
    format: 'uuid',
    example: '789e0123-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  assignedTo?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by workflow step ID',
    format: 'uuid',
    example: '012e3456-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  workflowStepId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by milestone ID',
    format: 'uuid',
    example: '345e6789-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  milestoneId?: string;

  // Filtering by feedback properties
  @ApiPropertyOptional({ 
    description: 'Filter by feedback type',
    enum: BusinessFeedbackType,
    example: BusinessFeedbackType.CHANGE_REQUEST
  })
  @IsOptional()
  @IsEnum(BusinessFeedbackType)
  type?: BusinessFeedbackType;

  @ApiPropertyOptional({ 
    description: 'Filter by feedback status',
    enum: BusinessFeedbackStatus,
    example: BusinessFeedbackStatus.UNDER_REVIEW
  })
  @IsOptional()
  @IsEnum(BusinessFeedbackStatus)
  status?: BusinessFeedbackStatus;

  @ApiPropertyOptional({ 
    description: 'Filter by feedback priority',
    enum: BusinessFeedbackPriority,
    example: BusinessFeedbackPriority.HIGH
  })
  @IsOptional()
  @IsEnum(BusinessFeedbackPriority)
  priority?: BusinessFeedbackPriority;

  // Search
  @ApiPropertyOptional({ 
    description: 'Search in feedback title and description',
    example: '登录页面'
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by screen reference',
    example: '首页'
  })
  @IsOptional()
  @IsString()
  screenReference?: string;

  // Date filtering
  @ApiPropertyOptional({ 
    description: 'Filter feedback submitted after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  submittedAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter feedback submitted before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  submittedBefore?: string;

  @ApiPropertyOptional({ 
    description: 'Filter feedback due after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  dueAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter feedback due before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  dueBefore?: string;

  @ApiPropertyOptional({ 
    description: 'Filter feedback resolved after this date',
    format: 'date-time',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  resolvedAfter?: string;

  @ApiPropertyOptional({ 
    description: 'Filter feedback resolved before this date',
    format: 'date-time',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  resolvedBefore?: string;

  // Status-based filters
  @ApiPropertyOptional({ 
    description: 'Only show overdue feedback',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  overdueOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show unresolved feedback',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  unresolvedOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show feedback assigned to me',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  assignedToMeOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show feedback submitted by me',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  submittedByMeOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show high priority feedback',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  highPriorityOnly?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Only show blocking feedback',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  blockingOnly?: boolean = false;

  // Sorting
  @ApiPropertyOptional({ 
    description: 'Sort field',
    enum: ['title', 'submittedAt', 'updatedAt', 'dueDate', 'priority', 'status', 'type'],
    default: 'submittedAt',
    example: 'submittedAt'
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'submittedAt';

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['ASC', 'DESC'],
    default: 'DESC',
    example: 'DESC'
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  // Include related data
  @ApiPropertyOptional({ 
    description: 'Include prototype information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includePrototype?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include submitter information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeSubmitter?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include assignee information in response',
    default: true,
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeAssignee?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Include soft-deleted feedback',
    default: false,
    example: false
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDeleted?: boolean = false;
}
