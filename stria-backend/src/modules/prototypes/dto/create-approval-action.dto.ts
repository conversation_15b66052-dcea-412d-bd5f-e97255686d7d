import { 
  IsString, 
  IsOptional, 
  IsEnum, 
  IsUUID, 
  IsBoolean,
  ValidateNested,
  IsArray
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  ActionType, 
  ApprovalDecision 
} from '../enums/approval-action.enum';

/**
 * Approval Condition DTO
 * Defines conditions for conditional approvals
 */
export class ApprovalConditionDto {
  @ApiProperty({ 
    description: 'Condition description', 
    example: 'Fix color contrast issues'
  })
  @IsString()
  description: string;

  @ApiPropertyOptional({ 
    description: 'Condition priority',
    enum: ['low', 'medium', 'high'],
    example: 'high'
  })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high'])
  priority?: string;

  @ApiPropertyOptional({ 
    description: 'Condition due date (ISO string)',
    example: '2024-01-30T17:00:00Z'
  })
  @IsOptional()
  @IsString()
  dueDate?: string;

  @ApiPropertyOptional({ 
    description: 'Assigned user for this condition',
    format: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  assignedTo?: string;
}

/**
 * Create Approval Action DTO
 * Data transfer object for creating approval actions
 */
export class CreateApprovalActionDto {
  @ApiProperty({ 
    description: 'Action type',
    enum: ActionType,
    example: ActionType.APPROVE
  })
  @IsEnum(ActionType)
  type: ActionType;

  @ApiProperty({ 
    description: 'Approval decision',
    enum: ApprovalDecision,
    example: ApprovalDecision.APPROVED
  })
  @IsEnum(ApprovalDecision)
  decision: ApprovalDecision;

  @ApiPropertyOptional({ 
    description: 'Comment or feedback', 
    example: 'The design looks great! Just need to adjust the button colors as discussed.'
  })
  @IsOptional()
  @IsString()
  comment?: string;

  @ApiPropertyOptional({ 
    description: 'Reason for the decision', 
    example: 'Meets all design requirements and follows brand guidelines'
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiPropertyOptional({ 
    description: 'Whether this is a conditional approval',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isConditional?: boolean;

  @ApiPropertyOptional({ 
    description: 'Array of conditions for conditional approvals',
    type: [ApprovalConditionDto]
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ApprovalConditionDto)
  @IsArray()
  conditions?: ApprovalConditionDto[];

  @ApiProperty({ 
    description: 'Approval step ID this action belongs to',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  stepId: string;

  @ApiPropertyOptional({ 
    description: 'User ID if action is delegated',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  delegatedBy?: string;

  @ApiPropertyOptional({ 
    description: 'Reason for delegation', 
    example: 'I am not available this week, delegating to team lead'
  })
  @IsOptional()
  @IsString()
  delegationReason?: string;

  // Attachments and references
  @ApiPropertyOptional({ 
    description: 'Array of attachment URLs',
    example: ['https://example.com/attachments/review-notes.pdf']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiPropertyOptional({ 
    description: 'Array of referenced feedback IDs',
    example: ['feedback-123', 'feedback-456']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  referencedFeedback?: string[];

  @ApiPropertyOptional({ 
    description: 'Array of tags for categorization',
    example: ['design', 'usability', 'branding']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  // Scoring and metrics
  @ApiPropertyOptional({ 
    description: 'Design quality score (1-10)',
    example: 8.5,
    minimum: 1,
    maximum: 10
  })
  @IsOptional()
  qualityScore?: number;

  @ApiPropertyOptional({ 
    description: 'Usability score (1-10)',
    example: 9.0,
    minimum: 1,
    maximum: 10
  })
  @IsOptional()
  usabilityScore?: number;

  @ApiPropertyOptional({ 
    description: 'Brand compliance score (1-10)',
    example: 7.5,
    minimum: 1,
    maximum: 10
  })
  @IsOptional()
  brandComplianceScore?: number;

  @ApiPropertyOptional({ 
    description: 'Custom properties object',
    example: { 
      reviewType: 'detailed',
      timeSpent: 45,
      reviewAreas: ['layout', 'colors', 'typography']
    }
  })
  @IsOptional()
  customProperties?: Record<string, any>;
}
