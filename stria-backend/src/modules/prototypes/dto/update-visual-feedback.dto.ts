import { PartialType, OmitType } from '@nestjs/swagger';
import { CreateVisualFeedbackDto } from './create-visual-feedback.dto';

/**
 * Update Visual Feedback DTO
 * Data transfer object for updating existing visual feedback
 * Extends CreateVisualFeedbackDto but makes all fields optional and removes prototypeId
 */
export class UpdateVisualFeedbackDto extends PartialType(
  OmitType(CreateVisualFeedbackDto, ['prototypeId'] as const)
) {}
