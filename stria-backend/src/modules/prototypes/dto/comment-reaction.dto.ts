import { 
  IsString, 
  IsOptional, 
  <PERSON>Enum, 
  IsUUID, 
  IsBoolean
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ReactionType } from '../enums/comment-discussion.enum';

/**
 * Add Comment Reaction DTO
 * Data transfer object for adding reactions to comments
 */
export class AddCommentReactionDto {
  @ApiProperty({ 
    description: 'Reaction type',
    enum: ReactionType,
    example: ReactionType.LIKE
  })
  @IsEnum(ReactionType)
  type: ReactionType;

  @ApiProperty({ 
    description: 'Comment ID to react to',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  commentId: string;

  @ApiPropertyOptional({ 
    description: 'Optional reaction note',
    example: 'Great suggestion!'
  })
  @IsOptional()
  @IsString()
  note?: string;
}

/**
 * Update Comment Reaction DTO
 * Data transfer object for updating existing reactions
 */
export class UpdateCommentReactionDto {
  @ApiPropertyOptional({ 
    description: 'Updated reaction type',
    enum: ReactionType,
    example: ReactionType.LOVE
  })
  @IsOptional()
  @IsEnum(ReactionType)
  type?: ReactionType;

  @ApiPropertyOptional({ 
    description: 'Updated reaction note',
    example: 'Excellent point!'
  })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({ 
    description: 'Whether reaction is active',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

/**
 * Comment Vote DTO
 * Data transfer object for voting on comments (upvote/downvote)
 */
export class CommentVoteDto {
  @ApiProperty({ 
    description: 'Vote type',
    enum: ['upvote', 'downvote'],
    example: 'upvote'
  })
  @IsEnum(['upvote', 'downvote'])
  voteType: 'upvote' | 'downvote';

  @ApiProperty({ 
    description: 'Comment ID to vote on',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  commentId: string;

  @ApiPropertyOptional({ 
    description: 'Optional vote reason',
    example: 'This addresses the core issue'
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * Mark Comment as Solution DTO
 * Data transfer object for marking comments as solutions
 */
export class MarkCommentSolutionDto {
  @ApiProperty({ 
    description: 'Comment ID to mark as solution',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  commentId: string;

  @ApiPropertyOptional({ 
    description: 'Reason for marking as solution',
    example: 'This solution addresses all the accessibility concerns raised'
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiPropertyOptional({ 
    description: 'Whether to close the discussion after marking solution',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  closeDiscussion?: boolean;
}

/**
 * Flag Comment DTO
 * Data transfer object for flagging inappropriate comments
 */
export class FlagCommentDto {
  @ApiProperty({ 
    description: 'Comment ID to flag',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  commentId: string;

  @ApiProperty({ 
    description: 'Flag reason',
    enum: ['spam', 'inappropriate', 'off_topic', 'harassment', 'other'],
    example: 'inappropriate'
  })
  @IsEnum(['spam', 'inappropriate', 'off_topic', 'harassment', 'other'])
  reason: string;

  @ApiPropertyOptional({ 
    description: 'Additional details about the flag',
    example: 'This comment contains offensive language'
  })
  @IsOptional()
  @IsString()
  details?: string;
}
