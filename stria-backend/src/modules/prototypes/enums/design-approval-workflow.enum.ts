/**
 * Workflow Status Enum
 * Defines the lifecycle states of an approval workflow
 */
export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  IN_PROGRESS = 'in_progress',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  EXPIRED = 'expired',
}

export const DEFAULT_WORKFLOW_STATUS = WorkflowStatus.DRAFT;

/**
 * Workflow Type Enum
 * Defines different types of approval workflows
 */
export enum WorkflowType {
  SEQUENTIAL = 'sequential',
  PARALLEL = 'parallel',
  HYBRID = 'hybrid',
  CONDITIONAL = 'conditional',
  ESCALATION = 'escalation',
  CONSENSUS = 'consensus',
}

export const DEFAULT_WORKFLOW_TYPE = WorkflowType.SEQUENTIAL;

/**
 * Approval Strategy Enum
 * Defines how approvals are evaluated
 */
export enum ApprovalStrategy {
  ALL_REQUIRED = 'all_required',
  MAJORITY_REQUIRED = 'majority_required',
  ANY_REQUIRED = 'any_required',
  THRESHOLD_BASED = 'threshold_based',
  WEIGHTED_VOTING = 'weighted_voting',
  ROLE_BASED = 'role_based',
}

export const DEFAULT_APPROVAL_STRATEGY = ApprovalStrategy.ALL_REQUIRED;

/**
 * Workflow Priority Enum
 * Defines priority levels for workflows
 */
export enum WorkflowPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
  URGENT = 'urgent',
}

export const DEFAULT_WORKFLOW_PRIORITY = WorkflowPriority.MEDIUM;

/**
 * Escalation Type Enum
 * Defines types of escalation in workflows
 */
export enum EscalationType {
  TIME_BASED = 'time_based',
  REJECTION_BASED = 'rejection_based',
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
  CONDITIONAL = 'conditional',
}

/**
 * Notification Type Enum
 * Defines types of notifications in workflows
 */
export enum NotificationType {
  EMAIL = 'email',
  SLACK = 'slack',
  WEBHOOK = 'webhook',
  IN_APP = 'in_app',
  SMS = 'sms',
}

/**
 * Workflow Template Enum
 * Predefined workflow templates
 */
export enum WorkflowTemplate {
  SIMPLE_APPROVAL = 'simple_approval',
  DESIGN_REVIEW = 'design_review',
  STAKEHOLDER_APPROVAL = 'stakeholder_approval',
  TECHNICAL_REVIEW = 'technical_review',
  LEGAL_REVIEW = 'legal_review',
  FINAL_APPROVAL = 'final_approval',
  CUSTOM = 'custom',
}
