/**
 * Comment Status Enum
 * Defines the lifecycle states of a comment
 */
export enum CommentStatus {
  ACTIVE = 'active',
  EDITED = 'edited',
  DELETED = 'deleted',
  HIDDEN = 'hidden',
  FLAGGED = 'flagged',
  APPROVED = 'approved',
  PENDING_MODERATION = 'pending_moderation',
}

export const DEFAULT_COMMENT_STATUS = CommentStatus.ACTIVE;

/**
 * Comment Type Enum
 * Defines different types of comments
 */
export enum CommentType {
  GENERAL = 'general',
  REPLY = 'reply',
  FEEDBACK = 'feedback',
  QUESTION = 'question',
  SUGGESTION = 'suggestion',
  ISSUE = 'issue',
  PRAISE = 'praise',
  CLARIFICATION = 'clarification',
  DECISION = 'decision',
  ACTION_ITEM = 'action_item',
}

export const DEFAULT_COMMENT_TYPE = CommentType.GENERAL;

/**
 * Discussion Status Enum
 * Defines the overall status of a discussion thread
 */
export enum DiscussionStatus {
  OPEN = 'open',
  ACTIVE = 'active',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
  ARCHIVED = 'archived',
  LOCKED = 'locked',
}

export const DEFAULT_DISCUSSION_STATUS = DiscussionStatus.OPEN;

/**
 * Discussion Type Enum
 * Defines the different types of discussions
 */
export enum DiscussionType {
  GENERAL = 'general',
  FEEDBACK = 'feedback',
  QUESTION = 'question',
  SUGGESTION = 'suggestion',
  BUG_REPORT = 'bug_report',
  FEATURE_REQUEST = 'feature_request',
  DESIGN_REVIEW = 'design_review',
  TECHNICAL_REVIEW = 'technical_review',
  STAKEHOLDER_REVIEW = 'stakeholder_review',
  ANNOUNCEMENT = 'announcement',
}

export const DEFAULT_DISCUSSION_TYPE = DiscussionType.GENERAL;

/**
 * Discussion Priority Enum
 * Defines the priority levels for discussions
 */
export enum DiscussionPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical',
}

export const DEFAULT_DISCUSSION_PRIORITY = DiscussionPriority.MEDIUM;

/**
 * Comment Priority Enum
 * Defines priority levels for comments
 */
export enum CommentPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export const DEFAULT_COMMENT_PRIORITY = CommentPriority.MEDIUM;

/**
 * Comment Sentiment Enum
 * Defines sentiment analysis results for comments
 */
export enum CommentSentiment {
  POSITIVE = 'positive',
  NEUTRAL = 'neutral',
  NEGATIVE = 'negative',
}

/**
 * Comment Reaction Type Enum
 * Defines types of reactions users can have to comments
 */
export enum CommentReactionType {
  LIKE = 'like',
  DISLIKE = 'dislike',
  LOVE = 'love',
  LAUGH = 'laugh',
  CONFUSED = 'confused',
  ANGRY = 'angry',
  CELEBRATE = 'celebrate',
  THUMBS_UP = 'thumbs_up',
  THUMBS_DOWN = 'thumbs_down',
}

/**
 * Reaction Type Enum (alias for CommentReactionType)
 * For backward compatibility
 */
export const ReactionType = CommentReactionType;

/**
 * Discussion Resolution Type Enum
 * Defines how a discussion was resolved
 */
export enum DiscussionResolutionType {
  ANSWERED = 'answered',
  IMPLEMENTED = 'implemented',
  WONT_FIX = 'wont_fix',
  DUPLICATE = 'duplicate',
  INVALID = 'invalid',
  OUT_OF_SCOPE = 'out_of_scope',
  DEFERRED = 'deferred',
}

/**
 * Comment Visibility Enum
 * Defines visibility levels for comments
 */
export enum CommentVisibility {
  PUBLIC = 'public',
  TEAM = 'team',
  PROJECT = 'project',
  PRIVATE = 'private',
  INTERNAL = 'internal',
}
