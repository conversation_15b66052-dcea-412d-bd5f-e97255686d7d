/**
 * Prototype Status Enum
 * Defines the lifecycle states of a prototype
 */
export enum PrototypeStatus {
  DRAFT = 'draft',
  IN_REVIEW = 'in_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  IN_DEVELOPMENT = 'in_development',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
}

export const DEFAULT_PROTOTYPE_STATUS = PrototypeStatus.DRAFT;

/**
 * Prototype Type Enum
 * Defines different types of prototypes
 */
export enum PrototypeType {
  WIREFRAME = 'wireframe',
  LOW_FIDELITY = 'low_fidelity',
  HIGH_FIDELITY = 'high_fidelity',
  INTERACTIVE = 'interactive',
  MOBILE = 'mobile',
  WEB = 'web',
  DESKTOP = 'desktop',
}

export const DEFAULT_PROTOTYPE_TYPE = PrototypeType.LOW_FIDELITY;

/**
 * Figma Integration Status Enum
 * Tracks the status of Figma integration for prototypes
 */
export enum FigmaIntegrationStatus {
  NOT_CONNECTED = 'not_connected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  SYNC_PENDING = 'sync_pending',
  SYNCING = 'syncing',
  SYNCED = 'synced',
  SYNC_ERROR = 'sync_error',
  DISCONNECTED = 'disconnected',
}

export const DEFAULT_FIGMA_INTEGRATION_STATUS = FigmaIntegrationStatus.NOT_CONNECTED;

/**
 * Prototype Priority Enum
 * Defines priority levels for prototype development
 */
export enum PrototypePriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export const DEFAULT_PROTOTYPE_PRIORITY = PrototypePriority.MEDIUM;

/**
 * Prototype Access Level Enum
 * Defines who can access and modify the prototype
 */
export enum PrototypeAccessLevel {
  PRIVATE = 'private',
  TEAM = 'team',
  PROJECT = 'project',
  PUBLIC = 'public',
}

export const DEFAULT_PROTOTYPE_ACCESS_LEVEL = PrototypeAccessLevel.PROJECT;
