/**
 * Feedback Status Enum
 * Defines the lifecycle states of visual feedback
 */
export enum FeedbackStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  PENDING_REVIEW = 'pending_review',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
  REJECTED = 'rejected',
  DEFERRED = 'deferred',
}

export const DEFAULT_FEEDBACK_STATUS = FeedbackStatus.OPEN;

/**
 * Feedback Type Enum
 * Categorizes different types of feedback
 */
export enum FeedbackType {
  BUG = 'bug',
  IMPROVEMENT = 'improvement',
  SUGGESTION = 'suggestion',
  QUESTION = 'question',
  PRAISE = 'praise',
  DESIGN_ISSUE = 'design_issue',
  USABILITY = 'usability',
  ACCESSIBILITY = 'accessibility',
  CONTENT = 'content',
  FUNCTIONALITY = 'functionality',
}

export const DEFAULT_FEEDBACK_TYPE = FeedbackType.SUGGESTION;

/**
 * Feedback Priority Enum
 * Defines priority levels for feedback items
 */
export enum FeedbackPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
  BLOCKER = 'blocker',
}

export const DEFAULT_FEEDBACK_PRIORITY = FeedbackPriority.MEDIUM;

/**
 * Annotation Type Enum
 * Defines different types of visual annotations
 */
export enum AnnotationType {
  POINT = 'point',
  RECTANGLE = 'rectangle',
  CIRCLE = 'circle',
  ARROW = 'arrow',
  LINE = 'line',
  HIGHLIGHT = 'highlight',
  TEXT_BOX = 'text_box',
  STICKY_NOTE = 'sticky_note',
  FREEHAND = 'freehand',
}

export const DEFAULT_ANNOTATION_TYPE = AnnotationType.POINT;

/**
 * Feedback Category Enum
 * High-level categorization of feedback
 */
export enum FeedbackCategory {
  VISUAL_DESIGN = 'visual_design',
  USER_EXPERIENCE = 'user_experience',
  FUNCTIONALITY = 'functionality',
  CONTENT = 'content',
  PERFORMANCE = 'performance',
  ACCESSIBILITY = 'accessibility',
  TECHNICAL = 'technical',
  BUSINESS = 'business',
}

/**
 * Feedback Severity Enum
 * Indicates the impact level of the feedback
 */
export enum FeedbackSeverity {
  MINOR = 'minor',
  MODERATE = 'moderate',
  MAJOR = 'major',
  CRITICAL = 'critical',
}

/**
 * Feedback Resolution Enum
 * Describes how the feedback was resolved
 */
export enum FeedbackResolution {
  FIXED = 'fixed',
  WONT_FIX = 'wont_fix',
  DUPLICATE = 'duplicate',
  INVALID = 'invalid',
  WORKS_AS_DESIGNED = 'works_as_designed',
  DEFERRED = 'deferred',
}

/**
 * Annotation Shape Enum
 * Defines available shapes for annotations
 */
export enum AnnotationShape {
  SQUARE = 'square',
  RECTANGLE = 'rectangle',
  CIRCLE = 'circle',
  ELLIPSE = 'ellipse',
  TRIANGLE = 'triangle',
  DIAMOND = 'diamond',
  STAR = 'star',
  CUSTOM = 'custom',
}
