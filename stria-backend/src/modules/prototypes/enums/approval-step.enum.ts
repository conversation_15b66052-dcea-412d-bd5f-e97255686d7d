/**
 * Step Status Enum
 * Defines the lifecycle states of an approval step
 */
export enum StepStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  WAITING_FOR_APPROVAL = 'waiting_for_approval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  SKIPPED = 'skipped',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  OVERDUE = 'overdue',
}

export const DEFAULT_STEP_STATUS = StepStatus.PENDING;

/**
 * Step Type Enum
 * Defines different types of approval steps
 */
export enum StepType {
  REVIEW = 'review',
  APPROVAL = 'approval',
  FEEDBACK = 'feedback',
  VALIDATION = 'validation',
  SIGN_OFF = 'sign_off',
  NOTIFICATION = 'notification',
  CONDITIONAL = 'conditional',
  AUTOMATED = 'automated',
}

export const DEFAULT_STEP_TYPE = StepType.REVIEW;

/**
 * Approval Decision Enum
 * Defines possible decisions for approval actions
 */
export enum ApprovalDecision {
  APPROVED = 'approved',
  REJECTED = 'rejected',
  APPROVED_WITH_CONDITIONS = 'approved_with_conditions',
  NEEDS_CHANGES = 'needs_changes',
  DEFERRED = 'deferred',
  ABSTAIN = 'abstain',
}

/**
 * Step Priority Enum
 * Defines priority levels for approval steps
 */
export enum StepPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export const DEFAULT_STEP_PRIORITY = StepPriority.MEDIUM;

/**
 * Step Execution Mode Enum
 * Defines how steps are executed
 */
export enum StepExecutionMode {
  SEQUENTIAL = 'sequential',
  PARALLEL = 'parallel',
  CONDITIONAL = 'conditional',
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
}

/**
 * Step Dependency Type Enum
 * Defines types of dependencies between steps
 */
export enum StepDependencyType {
  BLOCKS = 'blocks',
  REQUIRES = 'requires',
  OPTIONAL = 'optional',
  CONDITIONAL = 'conditional',
}

/**
 * Step Completion Criteria Enum
 * Defines criteria for step completion
 */
export enum StepCompletionCriteria {
  ALL_APPROVERS = 'all_approvers',
  MAJORITY_APPROVERS = 'majority_approvers',
  ANY_APPROVER = 'any_approver',
  THRESHOLD_MET = 'threshold_met',
  TIME_ELAPSED = 'time_elapsed',
  CONDITION_MET = 'condition_met',
}
