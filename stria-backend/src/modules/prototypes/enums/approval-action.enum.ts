/**
 * Action Type Enum
 * Defines different types of approval actions
 */
export enum ActionType {
  APPROVE = 'approve',
  REJECT = 'reject',
  REQUEST_CHANGES = 'request_changes',
  COMMENT = 'comment',
  DELEGATE = 'delegate',
  ESCALATE = 'escalate',
  SKIP = 'skip',
  DEFER = 'defer',
}

export const DEFAULT_ACTION_TYPE = ActionType.COMMENT;

/**
 * Approval Decision Enum
 * Defines possible decisions for approval actions
 */
export enum ApprovalDecision {
  APPROVED = 'approved',
  REJECTED = 'rejected',
  APPROVED_WITH_CONDITIONS = 'approved_with_conditions',
  NEEDS_CHANGES = 'needs_changes',
  DEFERRED = 'deferred',
  ABSTAIN = 'abstain',
  DELEGATED = 'delegated',
  ESCALATED = 'escalated',
}

/**
 * Action Status Enum
 * Defines the status of approval actions
 */
export enum ActionStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
}

/**
 * Delegation Reason Enum
 * Predefined reasons for delegation
 */
export enum DelegationReason {
  OUT_OF_OFFICE = 'out_of_office',
  LACK_OF_EXPERTISE = 'lack_of_expertise',
  CONFLICT_OF_INTEREST = 'conflict_of_interest',
  WORKLOAD = 'workload',
  AUTHORITY_LEVEL = 'authority_level',
  OTHER = 'other',
}

/**
 * Escalation Reason Enum
 * Predefined reasons for escalation
 */
export enum EscalationReason {
  OVERDUE = 'overdue',
  DISAGREEMENT = 'disagreement',
  COMPLEXITY = 'complexity',
  AUTHORITY_REQUIRED = 'authority_required',
  POLICY_VIOLATION = 'policy_violation',
  MANUAL_REQUEST = 'manual_request',
}

/**
 * Action Priority Enum
 * Defines priority levels for actions
 */
export enum ActionPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
  URGENT = 'urgent',
}

/**
 * Review Confidence Level Enum
 * Indicates reviewer's confidence in their decision
 */
export enum ReviewConfidenceLevel {
  VERY_LOW = 1,
  LOW = 2,
  MEDIUM = 3,
  HIGH = 4,
  VERY_HIGH = 5,
}
