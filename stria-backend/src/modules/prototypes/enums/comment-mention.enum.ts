/**
 * Mention Type Enum
 * Defines different types of user mentions
 */
export enum MentionType {
  USER = 'user',
  TEAM = 'team',
  ROLE = 'role',
  EVERYONE = 'everyone',
  HERE = 'here',
}

export const DEFAULT_MENTION_TYPE = MentionType.USER;

/**
 * Mention Status Enum
 * Defines the status of a mention
 */
export enum MentionStatus {
  PENDING = 'pending',
  NOTIFIED = 'notified',
  READ = 'read',
  ACKNOWLEDGED = 'acknowledged',
  IGNORED = 'ignored',
  EXPIRED = 'expired',
}

export const DEFAULT_MENTION_STATUS = MentionStatus.PENDING;

/**
 * Mention Priority Enum
 * Defines priority levels for mentions
 */
export enum MentionPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export const DEFAULT_MENTION_PRIORITY = MentionPriority.MEDIUM;

/**
 * Notification Channel Enum
 * Defines channels through which mentions can be notified
 */
export enum NotificationChannel {
  EMAIL = 'email',
  SLACK = 'slack',
  IN_APP = 'in_app',
  SMS = 'sms',
  WEBHOOK = 'webhook',
  PUSH = 'push',
}

/**
 * Mention Context Enum
 * Defines the context in which a mention was made
 */
export enum MentionContext {
  DIRECT_MENTION = 'direct_mention',
  REPLY_MENTION = 'reply_mention',
  THREAD_MENTION = 'thread_mention',
  ASSIGNMENT = 'assignment',
  REVIEW_REQUEST = 'review_request',
  FYI = 'fyi',
}
