/**
 * Version Status Enum
 * Defines the lifecycle states of a prototype version
 */
export enum VersionStatus {
  DRAFT = 'draft',
  PENDING_REVIEW = 'pending_review',
  IN_REVIEW = 'in_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  MERGED = 'merged',
  ARCHIVED = 'archived',
}

export const DEFAULT_VERSION_STATUS = VersionStatus.DRAFT;

/**
 * Version Type Enum
 * Defines different types of version changes
 */
export enum VersionType {
  MAJOR = 'major',
  MINOR = 'minor',
  PATCH = 'patch',
  HOTFIX = 'hotfix',
  FEATURE = 'feature',
  BUGFIX = 'bugfix',
  EXPERIMENTAL = 'experimental',
}

export const DEFAULT_VERSION_TYPE = VersionType.MINOR;

/**
 * Version Change Type Enum
 * Categorizes the types of changes in a version
 */
export enum VersionChangeType {
  DESIGN_UPDATE = 'design_update',
  CONTENT_CHANGE = 'content_change',
  LAYOUT_MODIFICATION = 'layout_modification',
  INTERACTION_CHANGE = 'interaction_change',
  STYLE_UPDATE = 'style_update',
  COMPONENT_ADDITION = 'component_addition',
  COMPONENT_REMOVAL = 'component_removal',
  BUG_FIX = 'bug_fix',
}

/**
 * Version Merge Strategy Enum
 * Defines how versions should be merged
 */
export enum VersionMergeStrategy {
  FAST_FORWARD = 'fast_forward',
  MERGE_COMMIT = 'merge_commit',
  SQUASH_MERGE = 'squash_merge',
  REBASE_MERGE = 'rebase_merge',
}

export const DEFAULT_VERSION_MERGE_STRATEGY = VersionMergeStrategy.MERGE_COMMIT;
