import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Prototype } from '../prototype.entity';
import { PrototypeStatus, PrototypeType, FigmaIntegrationStatus } from '../../enums/prototype.enum';

describe('Prototype Entity', () => {
  let repository: Repository<Prototype>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: getRepositoryToken(Prototype),
          useClass: Repository,
        },
      ],
    }).compile();

    repository = module.get<Repository<Prototype>>(getRepositoryToken(Prototype));
  });

  describe('Entity Creation', () => {
    it('should create a prototype with default values', () => {
      const prototype = new Prototype();
      prototype.name = 'Test Prototype';
      prototype.projectId = 'project-uuid';
      prototype.createdBy = 'user-uuid';

      expect(prototype.name).toBe('Test Prototype');
      expect(prototype.projectId).toBe('project-uuid');
      expect(prototype.createdBy).toBe('user-uuid');
      // Note: Default values from decorators only apply at database level
      // JavaScript object instantiation doesn't automatically set these values
    });

    it('should allow setting custom values', () => {
      const prototype = new Prototype();
      prototype.name = 'High Fidelity Prototype';
      prototype.description = 'A detailed prototype for testing';
      prototype.type = PrototypeType.HIGH_FIDELITY;
      prototype.status = PrototypeStatus.IN_REVIEW;
      prototype.figmaIntegrationStatus = FigmaIntegrationStatus.CONNECTED;
      prototype.figmaFileId = 'figma-file-123';
      prototype.figmaFileUrl = 'https://figma.com/file/123';
      prototype.currentVersion = '2.1.0';
      prototype.versionCount = 5;
      prototype.projectId = 'project-uuid';
      prototype.createdBy = 'user-uuid';

      expect(prototype.name).toBe('High Fidelity Prototype');
      expect(prototype.description).toBe('A detailed prototype for testing');
      expect(prototype.type).toBe(PrototypeType.HIGH_FIDELITY);
      expect(prototype.status).toBe(PrototypeStatus.IN_REVIEW);
      expect(prototype.figmaIntegrationStatus).toBe(FigmaIntegrationStatus.CONNECTED);
      expect(prototype.figmaFileId).toBe('figma-file-123');
      expect(prototype.figmaFileUrl).toBe('https://figma.com/file/123');
      expect(prototype.currentVersion).toBe('2.1.0');
      expect(prototype.versionCount).toBe(5);
    });

    it('should handle metadata correctly', () => {
      const prototype = new Prototype();
      prototype.name = 'Test Prototype';
      prototype.projectId = 'project-uuid';
      prototype.createdBy = 'user-uuid';
      prototype.metadata = {
        figmaNodeIds: ['node1', 'node2'],
        designSpecs: {
          width: 1920,
          height: 1080,
          scale: 1.0,
        },
        collaborators: ['user1', 'user2'],
        tags: ['mobile', 'responsive'],
        customProperties: {
          priority: 'high',
          deadline: '2024-12-31',
        },
      };

      expect(prototype.metadata.figmaNodeIds).toEqual(['node1', 'node2']);
      expect(prototype.metadata.designSpecs.width).toBe(1920);
      expect(prototype.metadata.collaborators).toEqual(['user1', 'user2']);
      expect(prototype.metadata.tags).toEqual(['mobile', 'responsive']);
      expect(prototype.metadata.customProperties.priority).toBe('high');
    });
  });

  describe('Entity Validation', () => {
    it('should require name field', () => {
      const prototype = new Prototype();
      prototype.projectId = 'project-uuid';
      prototype.createdBy = 'user-uuid';

      // Name is required, so this should fail validation
      expect(prototype.name).toBeUndefined();
    });

    it('should require projectId field', () => {
      const prototype = new Prototype();
      prototype.name = 'Test Prototype';
      prototype.createdBy = 'user-uuid';

      // ProjectId is required, so this should fail validation
      expect(prototype.projectId).toBeUndefined();
    });

    it('should require createdBy field', () => {
      const prototype = new Prototype();
      prototype.name = 'Test Prototype';
      prototype.projectId = 'project-uuid';

      // CreatedBy is required, so this should fail validation
      expect(prototype.createdBy).toBeUndefined();
    });
  });

  describe('Entity Structure', () => {
    it('should be a valid entity class', () => {
      const prototype = new Prototype();
      prototype.name = 'Test Prototype';
      prototype.projectId = 'project-uuid';
      prototype.createdBy = 'user-uuid';

      // Basic entity structure validation
      expect(prototype).toBeInstanceOf(Prototype);
      expect(typeof prototype.name).toBe('string');
      expect(typeof prototype.projectId).toBe('string');
      expect(typeof prototype.createdBy).toBe('string');
    });
  });

  describe('Soft Delete Support', () => {
    it('should support soft delete fields', () => {
      const prototype = new Prototype();
      prototype.name = 'Test Prototype';
      prototype.projectId = 'project-uuid';
      prototype.createdBy = 'user-uuid';
      prototype.deletedAt = new Date();
      prototype.deletedBy = 'admin-uuid';

      expect(prototype.deletedAt).toBeInstanceOf(Date);
      expect(prototype.deletedBy).toBe('admin-uuid');
    });
  });
});
