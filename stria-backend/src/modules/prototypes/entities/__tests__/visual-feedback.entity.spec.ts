import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VisualFeedback } from '../visual-feedback.entity';
import { 
  FeedbackStatus, 
  FeedbackType, 
  FeedbackPriority, 
  AnnotationType 
} from '../../enums/visual-feedback.enum';

describe('VisualFeedback Entity', () => {
  let repository: Repository<VisualFeedback>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: getRepositoryToken(VisualFeedback),
          useClass: Repository,
        },
      ],
    }).compile();

    repository = module.get<Repository<VisualFeedback>>(getRepositoryToken(VisualFeedback));
  });

  describe('Entity Creation', () => {
    it('should create a visual feedback with required fields', () => {
      const feedback = new VisualFeedback();
      feedback.content = 'This needs improvement';
      feedback.positionX = 100.5;
      feedback.positionY = 200.75;
      feedback.prototypeId = 'prototype-uuid';
      feedback.createdBy = 'user-uuid';

      expect(feedback.content).toBe('This needs improvement');
      expect(feedback.positionX).toBe(100.5);
      expect(feedback.positionY).toBe(200.75);
      expect(feedback.prototypeId).toBe('prototype-uuid');
      expect(feedback.createdBy).toBe('user-uuid');
      // Note: Default values from decorators only apply at database level
    });

    it('should allow setting custom values', () => {
      const feedback = new VisualFeedback();
      feedback.title = 'Critical Bug';
      feedback.content = 'Button is not clickable';
      feedback.type = FeedbackType.BUG;
      feedback.status = FeedbackStatus.IN_PROGRESS;
      feedback.priority = FeedbackPriority.CRITICAL;
      feedback.annotationType = AnnotationType.RECTANGLE;
      feedback.positionX = 150.25;
      feedback.positionY = 300.5;
      feedback.positionZ = 5;
      feedback.width = 100;
      feedback.height = 50;
      feedback.color = '#FF0000';
      feedback.borderColor = '#000000';
      feedback.backgroundColor = '#FFFF00';
      feedback.opacity = 0.8;
      feedback.targetElementId = 'button-submit';
      feedback.targetElementType = 'button';
      feedback.targetElementName = 'Submit Button';
      feedback.prototypeId = 'prototype-uuid';
      feedback.versionId = 'version-uuid';
      feedback.createdBy = 'user-uuid';
      feedback.assignedTo = 'developer-uuid';

      expect(feedback.title).toBe('Critical Bug');
      expect(feedback.content).toBe('Button is not clickable');
      expect(feedback.type).toBe(FeedbackType.BUG);
      expect(feedback.status).toBe(FeedbackStatus.IN_PROGRESS);
      expect(feedback.priority).toBe(FeedbackPriority.CRITICAL);
      expect(feedback.annotationType).toBe(AnnotationType.RECTANGLE);
      expect(feedback.positionX).toBe(150.25);
      expect(feedback.positionY).toBe(300.5);
      expect(feedback.positionZ).toBe(5);
      expect(feedback.width).toBe(100);
      expect(feedback.height).toBe(50);
      expect(feedback.color).toBe('#FF0000');
      expect(feedback.borderColor).toBe('#000000');
      expect(feedback.backgroundColor).toBe('#FFFF00');
      expect(feedback.opacity).toBe(0.8);
      expect(feedback.targetElementId).toBe('button-submit');
      expect(feedback.targetElementType).toBe('button');
      expect(feedback.targetElementName).toBe('Submit Button');
      expect(feedback.assignedTo).toBe('developer-uuid');
    });

    it('should handle metadata correctly', () => {
      const feedback = new VisualFeedback();
      feedback.content = 'Test feedback';
      feedback.positionX = 100;
      feedback.positionY = 200;
      feedback.prototypeId = 'prototype-uuid';
      feedback.createdBy = 'user-uuid';
      feedback.metadata = {
        screenshot: 'screenshot-url',
        attachments: ['file1.png', 'file2.pdf'],
        tags: ['ui', 'critical'],
        category: 'usability',
        severity: 'high',
        estimatedEffort: 4,
        relatedFeedbacks: ['feedback-1', 'feedback-2'],
        customFields: {
          browser: 'Chrome',
          device: 'Desktop',
          resolution: '1920x1080',
        },
      };

      expect(feedback.metadata.screenshot).toBe('screenshot-url');
      expect(feedback.metadata.attachments).toEqual(['file1.png', 'file2.pdf']);
      expect(feedback.metadata.tags).toEqual(['ui', 'critical']);
      expect(feedback.metadata.category).toBe('usability');
      expect(feedback.metadata.severity).toBe('high');
      expect(feedback.metadata.estimatedEffort).toBe(4);
      expect(feedback.metadata.relatedFeedbacks).toEqual(['feedback-1', 'feedback-2']);
      expect(feedback.metadata.customFields.browser).toBe('Chrome');
    });
  });

  describe('Entity Validation', () => {
    it('should require content field', () => {
      const feedback = new VisualFeedback();
      feedback.positionX = 100;
      feedback.positionY = 200;
      feedback.prototypeId = 'prototype-uuid';
      feedback.createdBy = 'user-uuid';

      // Content is required, so this should fail validation
      expect(feedback.content).toBeUndefined();
    });

    it('should require position coordinates', () => {
      const feedback = new VisualFeedback();
      feedback.content = 'Test feedback';
      feedback.prototypeId = 'prototype-uuid';
      feedback.createdBy = 'user-uuid';

      // Position coordinates are required
      expect(feedback.positionX).toBeUndefined();
      expect(feedback.positionY).toBeUndefined();
    });

    it('should require prototypeId field', () => {
      const feedback = new VisualFeedback();
      feedback.content = 'Test feedback';
      feedback.positionX = 100;
      feedback.positionY = 200;
      feedback.createdBy = 'user-uuid';

      // PrototypeId is required
      expect(feedback.prototypeId).toBeUndefined();
    });

    it('should require createdBy field', () => {
      const feedback = new VisualFeedback();
      feedback.content = 'Test feedback';
      feedback.positionX = 100;
      feedback.positionY = 200;
      feedback.prototypeId = 'prototype-uuid';

      // CreatedBy is required
      expect(feedback.createdBy).toBeUndefined();
    });
  });

  describe('Entity Structure', () => {
    it('should be a valid entity class', () => {
      const feedback = new VisualFeedback();
      feedback.content = 'Test feedback';
      feedback.positionX = 100;
      feedback.positionY = 200;
      feedback.prototypeId = 'prototype-uuid';
      feedback.createdBy = 'user-uuid';

      // Basic entity structure validation
      expect(feedback).toBeInstanceOf(VisualFeedback);
      expect(typeof feedback.content).toBe('string');
      expect(typeof feedback.positionX).toBe('number');
      expect(typeof feedback.positionY).toBe('number');
    });
  });

  describe('Coordinate System', () => {
    it('should handle decimal coordinates correctly', () => {
      const feedback = new VisualFeedback();
      feedback.content = 'Test feedback';
      feedback.positionX = 123.456;
      feedback.positionY = 789.012;
      feedback.positionZ = 3;
      feedback.width = 50.25;
      feedback.height = 75.75;
      feedback.prototypeId = 'prototype-uuid';
      feedback.createdBy = 'user-uuid';

      expect(feedback.positionX).toBe(123.456);
      expect(feedback.positionY).toBe(789.012);
      expect(feedback.positionZ).toBe(3);
      expect(feedback.width).toBe(50.25);
      expect(feedback.height).toBe(75.75);
    });
  });

  describe('Color Validation', () => {
    it('should handle hex color codes', () => {
      const feedback = new VisualFeedback();
      feedback.content = 'Test feedback';
      feedback.positionX = 100;
      feedback.positionY = 200;
      feedback.color = '#FF6B6B';
      feedback.borderColor = '#000000';
      feedback.backgroundColor = '#FFFFFF';
      feedback.prototypeId = 'prototype-uuid';
      feedback.createdBy = 'user-uuid';

      expect(feedback.color).toBe('#FF6B6B');
      expect(feedback.borderColor).toBe('#000000');
      expect(feedback.backgroundColor).toBe('#FFFFFF');
    });
  });

  describe('Resolution Tracking', () => {
    it('should track resolution details', () => {
      const feedback = new VisualFeedback();
      feedback.content = 'Test feedback';
      feedback.positionX = 100;
      feedback.positionY = 200;
      feedback.prototypeId = 'prototype-uuid';
      feedback.createdBy = 'user-uuid';
      feedback.resolvedBy = 'resolver-uuid';
      feedback.resolvedAt = new Date();

      expect(feedback.resolvedBy).toBe('resolver-uuid');
      expect(feedback.resolvedAt).toBeInstanceOf(Date);
    });
  });
});
