import {
  <PERSON>tity,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Prototype } from './prototype.entity';
import { VisualFeedback } from './visual-feedback.entity';
import {
  VersionStatus,
  VersionType,
  DEFAULT_VERSION_STATUS,
  DEFAULT_VERSION_TYPE,
} from '../enums/prototype-version.enum';

/**
 * PrototypeVersion Entity
 * Represents a specific version of a prototype with version control capabilities
 * Supports branching, merging, and version comparison for design iterations
 */
@Entity('prototype_versions')
export class PrototypeVersion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Version identification
  @Column({ name: 'version_number', type: 'varchar', length: 50 })
  @Index('idx_prototype_version_number')
  versionNumber: string;

  @Column({ name: 'version_name', type: 'varchar', length: 255, nullable: true })
  versionName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Version classification
  @Column({
    type: 'enum',
    enum: VersionType,
    default: DEFAULT_VERSION_TYPE,
  })
  @Index('idx_prototype_version_type')
  type: VersionType;

  @Column({
    type: 'enum',
    enum: VersionStatus,
    default: DEFAULT_VERSION_STATUS,
  })
  @Index('idx_prototype_version_status')
  status: VersionStatus;

  // Version hierarchy and branching
  @ManyToOne(() => PrototypeVersion, { nullable: true })
  @JoinColumn({ name: 'parent_version_id' })
  parentVersion: PrototypeVersion;

  @Column({ name: 'parent_version_id', type: 'uuid', nullable: true })
  @Index('idx_prototype_version_parent')
  parentVersionId: string;

  @Column({ name: 'branch_name', type: 'varchar', length: 100, nullable: true })
  @Index('idx_prototype_version_branch')
  branchName: string;

  @Column({ name: 'is_main_branch', type: 'boolean', default: false })
  @Index('idx_prototype_version_main_branch')
  isMainBranch: boolean;

  // Figma-specific version data
  @Column({ name: 'figma_version_id', type: 'varchar', length: 255, nullable: true })
  @Index('idx_prototype_version_figma_id')
  figmaVersionId: string;

  @Column({ name: 'figma_snapshot_url', type: 'text', nullable: true })
  figmaSnapshotUrl: string;

  @Column({ name: 'figma_thumbnail_url', type: 'text', nullable: true })
  figmaThumbnailUrl: string;

  // Version comparison data
  @Column({ name: 'changes_summary', type: 'text', nullable: true })
  changesSummary: string;

  @Column({ name: 'diff_data', type: 'jsonb', default: '{}' })
  diffData: {
    addedElements?: string[];
    removedElements?: string[];
    modifiedElements?: Array<{
      elementId: string;
      changes: Record<string, any>;
    }>;
    styleChanges?: Record<string, any>;
    [key: string]: any;
  };

  // Prototype relationship
  @ManyToOne(() => Prototype, prototype => prototype.versions, { nullable: false })
  @JoinColumn({ name: 'prototype_id' })
  prototype: Prototype;

  @Column({ name: 'prototype_id', type: 'uuid' })
  @Index('idx_prototype_version_prototype_id')
  prototypeId: string;

  // User relationships
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'created_by', type: 'uuid' })
  @Index('idx_prototype_version_created_by')
  createdBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approver: User;

  @Column({ name: 'approved_by', type: 'uuid', nullable: true })
  approvedBy: string;

  @Column({ name: 'approved_at', type: 'timestamp', nullable: true })
  approvedAt: Date;

  // Relationships
  @OneToMany(() => VisualFeedback, feedback => feedback.version, { lazy: true })
  visualFeedbacks: Promise<VisualFeedback[]>;

  // Version metadata
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    commitMessage?: string;
    tags?: string[];
    reviewers?: string[];
    testingNotes?: string;
    deploymentInfo?: Record<string, any>;
    performanceMetrics?: Record<string, any>;
    [key: string]: any;
  };

  // Timestamps
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Soft delete support
  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt: Date;

  @Column({ name: 'deleted_by', type: 'uuid', nullable: true })
  deletedBy: string;
}
