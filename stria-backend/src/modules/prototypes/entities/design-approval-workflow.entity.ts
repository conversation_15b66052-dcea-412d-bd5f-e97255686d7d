import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Prototype } from './prototype.entity';
import { ApprovalStep } from './approval-step.entity';
import {
  WorkflowStatus,
  WorkflowType,
  ApprovalStrategy,
  DEFAULT_WORKFLOW_STATUS,
  DEFAULT_WORKFLOW_TYPE,
  DEFAULT_APPROVAL_STRATEGY,
} from '../enums/design-approval-workflow.enum';

/**
 * DesignApprovalWorkflow Entity
 * Represents a multi-step approval process for design prototypes
 * Supports various approval strategies and reviewer assignment
 */
@Entity('design_approval_workflows')
export class DesignApprovalWorkflow {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Basic workflow information
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Workflow classification
  @Column({
    type: 'enum',
    enum: WorkflowType,
    default: DEFAULT_WORKFLOW_TYPE,
  })
  @Index('idx_approval_workflow_type')
  type: WorkflowType;

  @Column({
    type: 'enum',
    enum: WorkflowStatus,
    default: DEFAULT_WORKFLOW_STATUS,
  })
  @Index('idx_approval_workflow_status')
  status: WorkflowStatus;

  @Column({
    name: 'approval_strategy',
    type: 'enum',
    enum: ApprovalStrategy,
    default: DEFAULT_APPROVAL_STRATEGY,
  })
  @Index('idx_approval_workflow_strategy')
  approvalStrategy: ApprovalStrategy;

  // Workflow configuration
  @Column({ name: 'required_approvals', type: 'integer', default: 1 })
  requiredApprovals: number;

  @Column({ name: 'allow_parallel_approval', type: 'boolean', default: true })
  allowParallelApproval: boolean;

  @Column({ name: 'auto_approve_on_threshold', type: 'boolean', default: false })
  autoApproveOnThreshold: boolean;

  @Column({ name: 'approval_threshold_percentage', type: 'decimal', precision: 5, scale: 2, default: 100.0 })
  approvalThresholdPercentage: number;

  // Timing configuration
  @Column({ name: 'due_date', type: 'timestamp', nullable: true })
  dueDate: Date;

  @Column({ name: 'reminder_interval_hours', type: 'integer', default: 24 })
  reminderIntervalHours: number;

  @Column({ name: 'escalation_hours', type: 'integer', nullable: true })
  escalationHours: number;

  // Progress tracking
  @Column({ name: 'current_step', type: 'integer', default: 1 })
  currentStep: number;

  @Column({ name: 'total_steps', type: 'integer', default: 1 })
  totalSteps: number;

  @Column({ name: 'completed_steps', type: 'integer', default: 0 })
  completedSteps: number;

  @Column({ name: 'approval_percentage', type: 'decimal', precision: 5, scale: 2, default: 0.0 })
  approvalPercentage: number;

  // Prototype relationship
  @ManyToOne(() => Prototype, prototype => prototype.approvalWorkflows, { nullable: false })
  @JoinColumn({ name: 'prototype_id' })
  prototype: Prototype;

  @Column({ name: 'prototype_id', type: 'uuid' })
  @Index('idx_approval_workflow_prototype_id')
  prototypeId: string;

  // User relationships
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'created_by', type: 'uuid' })
  @Index('idx_approval_workflow_created_by')
  createdBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee: User;

  @Column({ name: 'assigned_to', type: 'uuid', nullable: true })
  @Index('idx_approval_workflow_assigned_to')
  assignedTo: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'completed_by' })
  completer: User;

  @Column({ name: 'completed_by', type: 'uuid', nullable: true })
  completedBy: string;

  @Column({ name: 'completed_at', type: 'timestamp', nullable: true })
  completedAt: Date;

  // Escalation
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'escalated_to' })
  escalatedTo: User;

  @Column({ name: 'escalated_to', type: 'uuid', nullable: true })
  escalatedToId: string;

  @Column({ name: 'escalated_at', type: 'timestamp', nullable: true })
  escalatedAt: Date;

  // Relationships
  @OneToMany(() => ApprovalStep, step => step.workflow, { lazy: true })
  steps: Promise<ApprovalStep[]>;

  // Workflow metadata
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    template?: string;
    customFields?: Record<string, any>;
    notifications?: {
      emailEnabled: boolean;
      slackEnabled: boolean;
      webhookUrl?: string;
    };
    reviewCriteria?: string[];
    approvalRules?: Record<string, any>;
    escalationRules?: Array<{
      condition: string;
      action: string;
      targetUserId?: string;
    }>;
    [key: string]: any;
  };

  // Timestamps
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'started_at', type: 'timestamp', nullable: true })
  startedAt: Date;

  // Soft delete support
  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt: Date;

  @Column({ name: 'deleted_by', type: 'uuid', nullable: true })
  deletedBy: string;
}
