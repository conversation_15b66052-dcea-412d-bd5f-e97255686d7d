import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { CommentDiscussion } from './comment-discussion.entity';
import {
  MentionType,
  MentionStatus,
  DEFAULT_MENTION_TYPE,
  DEFAULT_MENTION_STATUS,
} from '../enums/comment-mention.enum';

/**
 * CommentMention Entity
 * Represents user mentions in comments for notification purposes
 * Supports different types of mentions and notification tracking
 */
@Entity('comment_mentions')
export class CommentMention {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Mention details
  @Column({
    type: 'enum',
    enum: MentionType,
    default: DEFAULT_MENTION_TYPE,
  })
  @Index('idx_comment_mention_type')
  type: MentionType;

  @Column({
    type: 'enum',
    enum: MentionStatus,
    default: DEFAULT_MENTION_STATUS,
  })
  @Index('idx_comment_mention_status')
  status: MentionStatus;

  // Position in content
  @Column({ name: 'start_position', type: 'integer' })
  startPosition: number;

  @Column({ name: 'end_position', type: 'integer' })
  endPosition: number;

  @Column({ name: 'mention_text', type: 'varchar', length: 255 })
  mentionText: string; // The actual text that was mentioned (e.g., "@john.doe")

  // Relationships
  @ManyToOne(() => CommentDiscussion, comment => comment.mentions, { nullable: false })
  @JoinColumn({ name: 'comment_id' })
  comment: CommentDiscussion;

  @Column({ name: 'comment_id', type: 'uuid' })
  @Index('idx_comment_mention_comment_id')
  commentId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'mentioned_user_id' })
  mentionedUser: User;

  @Column({ name: 'mentioned_user_id', type: 'uuid' })
  @Index('idx_comment_mention_mentioned_user_id')
  mentionedUserId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'mentioned_by_user_id' })
  mentionedByUser: User;

  @Column({ name: 'mentioned_by_user_id', type: 'uuid' })
  @Index('idx_comment_mention_mentioned_by_user_id')
  mentionedByUserId: string;

  // Notification tracking
  @Column({ name: 'notification_sent', type: 'boolean', default: false })
  notificationSent: boolean;

  @Column({ name: 'notification_sent_at', type: 'timestamp', nullable: true })
  notificationSentAt: Date;

  @Column({ name: 'notification_read', type: 'boolean', default: false })
  notificationRead: boolean;

  @Column({ name: 'notification_read_at', type: 'timestamp', nullable: true })
  notificationReadAt: Date;

  // Mention metadata
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    notificationChannels?: string[]; // email, slack, in-app, etc.
    context?: string; // Additional context about the mention
    priority?: 'low' | 'medium' | 'high';
    customFields?: Record<string, any>;
    [key: string]: any;
  };

  // Timestamps
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Soft delete support
  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt: Date;

  @Column({ name: 'deleted_by', type: 'uuid', nullable: true })
  deletedBy: string;
}
