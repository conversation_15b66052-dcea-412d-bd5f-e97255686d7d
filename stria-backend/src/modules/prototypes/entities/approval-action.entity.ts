import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ApprovalStep } from './approval-step.entity';
import {
  ActionType,
  ApprovalDecision,
  DEFAULT_ACTION_TYPE,
} from '../enums/approval-action.enum';

/**
 * ApprovalAction Entity
 * Records individual approval actions taken by reviewers
 * Maintains audit trail of all approval decisions and comments
 */
@Entity('approval_actions')
export class ApprovalAction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Action details
  @Column({
    type: 'enum',
    enum: ActionType,
    default: DEFAULT_ACTION_TYPE,
  })
  @Index('idx_approval_action_type')
  type: ActionType;

  @Column({
    type: 'enum',
    enum: ApprovalDecision,
  })
  @Index('idx_approval_action_decision')
  decision: ApprovalDecision;

  @Column({ type: 'text', nullable: true })
  comment: string;

  @Column({ type: 'text', nullable: true })
  reason: string;

  // Conditional approval
  @Column({ name: 'is_conditional', type: 'boolean', default: false })
  isConditional: boolean;

  @Column({ type: 'jsonb', default: '[]' })
  conditions: Array<{
    description: string;
    priority: 'low' | 'medium' | 'high';
    dueDate?: string;
    assignedTo?: string;
  }>;

  // Step relationship
  @ManyToOne(() => ApprovalStep, step => step.actions, { nullable: false })
  @JoinColumn({ name: 'step_id' })
  step: ApprovalStep;

  @Column({ name: 'step_id', type: 'uuid' })
  @Index('idx_approval_action_step_id')
  stepId: string;

  // User relationship
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'user_id', type: 'uuid' })
  @Index('idx_approval_action_user_id')
  userId: string;

  // Delegation support
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'delegated_by' })
  delegatedBy: User;

  @Column({ name: 'delegated_by', type: 'uuid', nullable: true })
  delegatedById: string;

  @Column({ name: 'delegation_reason', type: 'text', nullable: true })
  delegationReason: string;

  // Action metadata
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    ipAddress?: string;
    userAgent?: string;
    attachments?: string[];
    reviewDuration?: number; // in minutes
    confidence?: number; // 1-10 scale
    expertise?: string;
    customFields?: Record<string, any>;
    [key: string]: any;
  };

  // Timestamps
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Soft delete support
  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt: Date;

  @Column({ name: 'deleted_by', type: 'uuid', nullable: true })
  deletedBy: string;
}
