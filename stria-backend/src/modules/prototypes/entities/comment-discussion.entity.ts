import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
  Tree,
  TreeParent,
  TreeChildren,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { VisualFeedback } from './visual-feedback.entity';
import { Prototype } from './prototype.entity';
import { CommentMention } from './comment-mention.entity';
import {
  CommentStatus,
  CommentType,
  DiscussionStatus,
  DEFAULT_COMMENT_STATUS,
  DEFAULT_COMMENT_TYPE,
  DEFAULT_DISCUSSION_STATUS,
} from '../enums/comment-discussion.enum';

/**
 * CommentDiscussion Entity
 * Represents threaded discussions and comments on prototypes and feedback
 * Supports hierarchical comment structures, mentions, and notifications
 */
@Entity('comment_discussions')
@Tree('nested-set')
export class CommentDiscussion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Comment content
  @Column({ type: 'text' })
  content: string;

  @Column({ name: 'formatted_content', type: 'text', nullable: true })
  formattedContent: string; // HTML or markdown formatted content

  // Comment classification
  @Column({
    type: 'enum',
    enum: CommentType,
    default: DEFAULT_COMMENT_TYPE,
  })
  @Index('idx_comment_discussion_type')
  type: CommentType;

  @Column({
    type: 'enum',
    enum: CommentStatus,
    default: DEFAULT_COMMENT_STATUS,
  })
  @Index('idx_comment_discussion_status')
  status: CommentStatus;

  @Column({
    name: 'discussion_status',
    type: 'enum',
    enum: DiscussionStatus,
    default: DEFAULT_DISCUSSION_STATUS,
  })
  @Index('idx_comment_discussion_discussion_status')
  discussionStatus: DiscussionStatus;

  // Thread hierarchy
  @TreeParent()
  parent: CommentDiscussion;

  @TreeChildren()
  children: CommentDiscussion[];

  @Column({ name: 'thread_depth', type: 'integer', default: 0 })
  @Index('idx_comment_discussion_thread_depth')
  threadDepth: number;

  @Column({ name: 'reply_count', type: 'integer', default: 0 })
  replyCount: number;

  // Relationships
  @ManyToOne(() => VisualFeedback, feedback => feedback.discussions, { nullable: true })
  @JoinColumn({ name: 'visual_feedback_id' })
  visualFeedback: VisualFeedback;

  @Column({ name: 'visual_feedback_id', type: 'uuid', nullable: true })
  @Index('idx_comment_discussion_visual_feedback_id')
  visualFeedbackId: string;

  @ManyToOne(() => Prototype, { nullable: true })
  @JoinColumn({ name: 'prototype_id' })
  prototype: Prototype;

  @Column({ name: 'prototype_id', type: 'uuid', nullable: true })
  @Index('idx_comment_discussion_prototype_id')
  prototypeId: string;

  // User relationships
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'author_id' })
  author: User;

  @Column({ name: 'author_id', type: 'uuid' })
  @Index('idx_comment_discussion_author_id')
  authorId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  @Column({ name: 'updated_by', type: 'uuid', nullable: true })
  updatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'resolved_by' })
  resolver: User;

  @Column({ name: 'resolved_by', type: 'uuid', nullable: true })
  resolvedBy: string;

  @Column({ name: 'resolved_at', type: 'timestamp', nullable: true })
  resolvedAt: Date;

  // Engagement metrics
  @Column({ name: 'like_count', type: 'integer', default: 0 })
  likeCount: number;

  @Column({ name: 'dislike_count', type: 'integer', default: 0 })
  dislikeCount: number;

  @Column({ name: 'view_count', type: 'integer', default: 0 })
  viewCount: number;

  // Content moderation
  @Column({ name: 'is_edited', type: 'boolean', default: false })
  isEdited: boolean;

  @Column({ name: 'edit_count', type: 'integer', default: 0 })
  editCount: number;

  @Column({ name: 'last_edited_at', type: 'timestamp', nullable: true })
  lastEditedAt: Date;

  @Column({ name: 'is_pinned', type: 'boolean', default: false })
  isPinned: boolean;

  @Column({ name: 'is_highlighted', type: 'boolean', default: false })
  isHighlighted: boolean;

  // Mentions relationship
  @OneToMany(() => CommentMention, mention => mention.comment, { lazy: true })
  mentions: Promise<CommentMention[]>;

  // Comment metadata
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    attachments?: Array<{
      id: string;
      name: string;
      url: string;
      type: string;
      size: number;
    }>;
    reactions?: Record<string, string[]>; // emoji -> user IDs
    tags?: string[];
    priority?: string;
    category?: string;
    sentiment?: 'positive' | 'neutral' | 'negative';
    language?: string;
    editHistory?: Array<{
      timestamp: string;
      content: string;
      editedBy: string;
    }>;
    customFields?: Record<string, any>;
    [key: string]: any;
  };

  // Timestamps
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Soft delete support
  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt: Date;

  @Column({ name: 'deleted_by', type: 'uuid', nullable: true })
  deletedBy: string;
}
