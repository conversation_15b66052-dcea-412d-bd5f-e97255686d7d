import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { DesignApprovalWorkflow } from './design-approval-workflow.entity';
import { ApprovalAction } from './approval-action.entity';
import {
  StepStatus,
  StepType,
  ApprovalDecision,
  DEFAULT_STEP_STATUS,
  DEFAULT_STEP_TYPE,
} from '../enums/approval-step.enum';

/**
 * ApprovalStep Entity
 * Represents individual steps in a design approval workflow
 * Supports sequential and parallel approval processes
 */
@Entity('approval_steps')
export class ApprovalStep {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Step identification
  @Column({ name: 'step_number', type: 'integer' })
  @Index('idx_approval_step_number')
  stepNumber: number;

  @Column({ name: 'step_name', type: 'varchar', length: 255 })
  stepName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Step classification
  @Column({
    type: 'enum',
    enum: StepType,
    default: DEFAULT_STEP_TYPE,
  })
  @Index('idx_approval_step_type')
  type: StepType;

  @Column({
    type: 'enum',
    enum: StepStatus,
    default: DEFAULT_STEP_STATUS,
  })
  @Index('idx_approval_step_status')
  status: StepStatus;

  // Step configuration
  @Column({ name: 'is_required', type: 'boolean', default: true })
  isRequired: boolean;

  @Column({ name: 'allow_skip', type: 'boolean', default: false })
  allowSkip: boolean;

  @Column({ name: 'auto_approve', type: 'boolean', default: false })
  autoApprove: boolean;

  @Column({ name: 'required_approvers', type: 'integer', default: 1 })
  requiredApprovers: number;

  // Timing
  @Column({ name: 'due_date', type: 'timestamp', nullable: true })
  dueDate: Date;

  @Column({ name: 'estimated_hours', type: 'decimal', precision: 8, scale: 2, nullable: true })
  estimatedHours: number;

  @Column({ name: 'actual_hours', type: 'decimal', precision: 8, scale: 2, nullable: true })
  actualHours: number;

  // Dependencies
  @Column({ name: 'depends_on_steps', type: 'jsonb', default: '[]' })
  dependsOnSteps: string[]; // Array of step IDs

  @Column({ name: 'blocks_steps', type: 'jsonb', default: '[]' })
  blocksSteps: string[]; // Array of step IDs

  // Approval tracking
  @Column({ name: 'approval_count', type: 'integer', default: 0 })
  approvalCount: number;

  @Column({ name: 'rejection_count', type: 'integer', default: 0 })
  rejectionCount: number;

  @Column({ name: 'final_decision', type: 'enum', enum: ApprovalDecision, nullable: true })
  finalDecision: ApprovalDecision;

  // Workflow relationship
  @ManyToOne(() => DesignApprovalWorkflow, workflow => workflow.steps, { nullable: false })
  @JoinColumn({ name: 'workflow_id' })
  workflow: DesignApprovalWorkflow;

  @Column({ name: 'workflow_id', type: 'uuid' })
  @Index('idx_approval_step_workflow_id')
  workflowId: string;

  // User relationships
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'created_by', type: 'uuid' })
  @Index('idx_approval_step_created_by')
  createdBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee: User;

  @Column({ name: 'assigned_to', type: 'uuid', nullable: true })
  @Index('idx_approval_step_assigned_to')
  assignedTo: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'completed_by' })
  completer: User;

  @Column({ name: 'completed_by', type: 'uuid', nullable: true })
  completedBy: string;

  @Column({ name: 'completed_at', type: 'timestamp', nullable: true })
  completedAt: Date;

  // Relationships
  @OneToMany(() => ApprovalAction, action => action.step, { lazy: true })
  actions: Promise<ApprovalAction[]>;

  // Step metadata
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    reviewCriteria?: string[];
    requiredDocuments?: string[];
    approverRoles?: string[];
    customFields?: Record<string, any>;
    notifications?: {
      onStart?: boolean;
      onComplete?: boolean;
      onOverdue?: boolean;
    };
    conditions?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
    [key: string]: any;
  };

  // Timestamps
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'started_at', type: 'timestamp', nullable: true })
  startedAt: Date;

  // Soft delete support
  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt: Date;

  @Column({ name: 'deleted_by', type: 'uuid', nullable: true })
  deletedBy: string;
}
