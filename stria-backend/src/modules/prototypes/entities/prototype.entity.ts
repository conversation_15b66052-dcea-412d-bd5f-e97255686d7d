import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Project } from '../../projects/entities/project.entity';
import { PrototypeVersion } from './prototype-version.entity';
import { VisualFeedback } from './visual-feedback.entity';
import { DesignApprovalWorkflow } from './design-approval-workflow.entity';
import {
  PrototypeStatus,
  PrototypeType,
  FigmaIntegrationStatus,
  DEFAULT_PROTOTYPE_STATUS,
  DEFAULT_PROTOTYPE_TYPE,
  DEFAULT_FIGMA_INTEGRATION_STATUS,
} from '../enums/prototype.enum';

/**
 * Prototype Entity
 * Represents a design prototype with Figma integration for Sprint 8 functionality
 * Supports version management, visual feedback, and approval workflows
 */
@Entity('prototypes')
export class Prototype {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Basic prototype information
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Prototype classification
  @Column({
    type: 'enum',
    enum: PrototypeType,
    default: DEFAULT_PROTOTYPE_TYPE,
  })
  @Index('idx_prototype_type')
  type: PrototypeType;

  @Column({
    type: 'enum',
    enum: PrototypeStatus,
    default: DEFAULT_PROTOTYPE_STATUS,
  })
  @Index('idx_prototype_status')
  status: PrototypeStatus;

  // Figma integration
  @Column({ name: 'figma_file_id', type: 'varchar', length: 255, nullable: true })
  @Index('idx_prototype_figma_file_id')
  figmaFileId: string;

  @Column({ name: 'figma_file_url', type: 'text', nullable: true })
  figmaFileUrl: string;

  @Column({ name: 'figma_embed_url', type: 'text', nullable: true })
  figmaEmbedUrl: string;

  @Column({
    name: 'figma_integration_status',
    type: 'enum',
    enum: FigmaIntegrationStatus,
    default: DEFAULT_FIGMA_INTEGRATION_STATUS,
  })
  @Index('idx_prototype_figma_status')
  figmaIntegrationStatus: FigmaIntegrationStatus;

  @Column({ name: 'figma_last_sync', type: 'timestamp', nullable: true })
  figmaLastSync: Date;

  // Version management
  @Column({ name: 'current_version', type: 'varchar', length: 50, default: '1.0.0' })
  currentVersion: string;

  @Column({ name: 'version_count', type: 'integer', default: 1 })
  versionCount: number;

  // Project and user relationships
  @ManyToOne(() => Project, { nullable: false })
  @JoinColumn({ name: 'project_id' })
  project: Project;

  @Column({ name: 'project_id', type: 'uuid' })
  @Index('idx_prototype_project_id')
  projectId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'created_by', type: 'uuid' })
  @Index('idx_prototype_created_by')
  createdBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  @Column({ name: 'updated_by', type: 'uuid', nullable: true })
  updatedBy: string;

  // Relationships
  @OneToMany(() => PrototypeVersion, version => version.prototype, { lazy: true })
  versions: Promise<PrototypeVersion[]>;

  @OneToMany(() => VisualFeedback, feedback => feedback.prototype, { lazy: true })
  visualFeedbacks: Promise<VisualFeedback[]>;

  @OneToMany(() => DesignApprovalWorkflow, workflow => workflow.prototype, { lazy: true })
  approvalWorkflows: Promise<DesignApprovalWorkflow[]>;

  // Metadata for additional configuration
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    figmaNodeIds?: string[];
    designSpecs?: {
      width?: number;
      height?: number;
      scale?: number;
    };
    collaborators?: string[];
    tags?: string[];
    customProperties?: Record<string, any>;
    [key: string]: any;
  };

  // Timestamps
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Soft delete support
  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt: Date;

  @Column({ name: 'deleted_by', type: 'uuid', nullable: true })
  deletedBy: string;
}
