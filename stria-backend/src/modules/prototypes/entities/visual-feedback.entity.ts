import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Prototype } from './prototype.entity';
import { PrototypeVersion } from './prototype-version.entity';
import { CommentDiscussion } from './comment-discussion.entity';
import {
  FeedbackStatus,
  FeedbackType,
  FeedbackPriority,
  AnnotationType,
  DEFAULT_FEEDBACK_STATUS,
  DEFAULT_FEEDBACK_TYPE,
  DEFAULT_FEEDBACK_PRIORITY,
  DEFAULT_ANNOTATION_TYPE,
} from '../enums/visual-feedback.enum';

/**
 * VisualFeedback Entity
 * Represents coordinate-based visual feedback and annotations on prototypes
 * Supports various annotation types, categorization, and status tracking
 */
@Entity('visual_feedbacks')
export class VisualFeedback {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Basic feedback information
  @Column({ type: 'varchar', length: 255, nullable: true })
  title: string;

  @Column({ type: 'text' })
  content: string;

  // Feedback classification
  @Column({
    type: 'enum',
    enum: FeedbackType,
    default: DEFAULT_FEEDBACK_TYPE,
  })
  @Index('idx_visual_feedback_type')
  type: FeedbackType;

  @Column({
    type: 'enum',
    enum: FeedbackStatus,
    default: DEFAULT_FEEDBACK_STATUS,
  })
  @Index('idx_visual_feedback_status')
  status: FeedbackStatus;

  @Column({
    type: 'enum',
    enum: FeedbackPriority,
    default: DEFAULT_FEEDBACK_PRIORITY,
  })
  @Index('idx_visual_feedback_priority')
  priority: FeedbackPriority;

  // Annotation details
  @Column({
    name: 'annotation_type',
    type: 'enum',
    enum: AnnotationType,
    default: DEFAULT_ANNOTATION_TYPE,
  })
  @Index('idx_visual_feedback_annotation_type')
  annotationType: AnnotationType;

  // Coordinate-based positioning
  @Column({ name: 'position_x', type: 'decimal', precision: 10, scale: 2 })
  positionX: number;

  @Column({ name: 'position_y', type: 'decimal', precision: 10, scale: 2 })
  positionY: number;

  @Column({ name: 'position_z', type: 'integer', default: 0 })
  positionZ: number; // Z-index for layering

  // Annotation dimensions (for shapes, highlights, etc.)
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  width: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  height: number;

  // Visual styling
  @Column({ type: 'varchar', length: 7, default: '#FF6B6B' })
  color: string; // Hex color code

  @Column({ name: 'border_color', type: 'varchar', length: 7, nullable: true })
  borderColor: string;

  @Column({ name: 'background_color', type: 'varchar', length: 7, nullable: true })
  backgroundColor: string;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 1.0 })
  opacity: number;

  // Target element information
  @Column({ name: 'target_element_id', type: 'varchar', length: 255, nullable: true })
  @Index('idx_visual_feedback_target_element')
  targetElementId: string; // Figma node ID or CSS selector

  @Column({ name: 'target_element_type', type: 'varchar', length: 100, nullable: true })
  targetElementType: string;

  @Column({ name: 'target_element_name', type: 'varchar', length: 255, nullable: true })
  targetElementName: string;

  // Relationships
  @ManyToOne(() => Prototype, prototype => prototype.visualFeedbacks, { nullable: false })
  @JoinColumn({ name: 'prototype_id' })
  prototype: Prototype;

  @Column({ name: 'prototype_id', type: 'uuid' })
  @Index('idx_visual_feedback_prototype_id')
  prototypeId: string;

  @ManyToOne(() => PrototypeVersion, version => version.visualFeedbacks, { nullable: true })
  @JoinColumn({ name: 'version_id' })
  version: PrototypeVersion;

  @Column({ name: 'version_id', type: 'uuid', nullable: true })
  @Index('idx_visual_feedback_version_id')
  versionId: string;

  // User relationships
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'created_by', type: 'uuid' })
  @Index('idx_visual_feedback_created_by')
  createdBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee: User;

  @Column({ name: 'assigned_to', type: 'uuid', nullable: true })
  @Index('idx_visual_feedback_assigned_to')
  assignedTo: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'resolved_by' })
  resolver: User;

  @Column({ name: 'resolved_by', type: 'uuid', nullable: true })
  resolvedBy: string;

  @Column({ name: 'resolved_at', type: 'timestamp', nullable: true })
  resolvedAt: Date;

  // Discussion relationship
  @OneToMany(() => CommentDiscussion, comment => comment.visualFeedback, { lazy: true })
  discussions: Promise<CommentDiscussion[]>;

  // Feedback metadata
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    screenshot?: string;
    attachments?: string[];
    tags?: string[];
    category?: string;
    severity?: string;
    estimatedEffort?: number;
    relatedFeedbacks?: string[];
    customFields?: Record<string, any>;
    [key: string]: any;
  };

  // Timestamps
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Soft delete support
  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt: Date;

  @Column({ name: 'deleted_by', type: 'uuid', nullable: true })
  deletedBy: string;
}
