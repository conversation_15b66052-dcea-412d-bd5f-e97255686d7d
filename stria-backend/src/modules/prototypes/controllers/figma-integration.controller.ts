import {
  Controller,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { FigmaIntegrationService } from '../services/figma-integration.service';
import {
  FigmaSyncDto,
  FigmaSyncResponseDto,
  FigmaConnectionTestDto,
  FigmaConnectionTestResponseDto,
} from '../dto/figma-sync.dto';

/**
 * FigmaIntegrationController
 * 
 * REST API controller for Figma integration functionality
 * Handles synchronization and connection testing with Figma
 */
@ApiTags('Figma Integration')
@Controller('prototypes')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FigmaIntegrationController {
  constructor(
    private readonly figmaIntegrationService: FigmaIntegrationService,
  ) {}

  /**
   * Sync prototype with Figma file
   */
  @Post(':id/figma/sync')
  @ApiOperation({ 
    summary: 'Sync prototype with Figma',
    description: 'Synchronizes a prototype with its associated Figma file, updating design data and metadata'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Figma sync completed successfully',
    type: FigmaSyncResponseDto,
  })
  @ApiBadRequestResponse({ 
    description: 'Invalid input data or sync configuration',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Invalid Figma file ID' },
        error: { type: 'string', example: 'File not found or access denied' },
      }
    }
  })
  @ApiNotFoundResponse({ description: 'Prototype not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async syncWithFigma(
    @Param('id', ParseUUIDPipe) prototypeId: string,
    @Body() syncDto: FigmaSyncDto,
    @Request() req: any,
  ): Promise<FigmaSyncResponseDto> {
    return this.figmaIntegrationService.syncWithFigma(
      prototypeId, 
      syncDto, 
      req.user.id
    );
  }

  /**
   * Test connection to Figma file
   */
  @Post('figma/test-connection')
  @ApiOperation({ 
    summary: 'Test Figma connection',
    description: 'Tests connectivity to a Figma file to verify access permissions and file validity'
  })
  @ApiOkResponse({
    description: 'Connection test completed',
    type: FigmaConnectionTestResponseDto,
  })
  @ApiBadRequestResponse({ 
    description: 'Invalid Figma file ID',
    schema: {
      type: 'object',
      properties: {
        connected: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Invalid file ID format' },
        error: { type: 'string', example: 'File ID must be a valid Figma file identifier' },
      }
    }
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async testConnection(
    @Body() testDto: FigmaConnectionTestDto,
  ): Promise<FigmaConnectionTestResponseDto> {
    return this.figmaIntegrationService.testConnection(testDto);
  }

  /**
   * Force sync prototype with Figma (ignores cache)
   */
  @Post(':id/figma/force-sync')
  @ApiOperation({ 
    summary: 'Force sync with Figma',
    description: 'Forces a complete synchronization with Figma, ignoring any cached data or sync timestamps'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Force sync completed successfully',
    type: FigmaSyncResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid sync configuration' })
  @ApiNotFoundResponse({ description: 'Prototype not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async forceSyncWithFigma(
    @Param('id', ParseUUIDPipe) prototypeId: string,
    @Body() syncDto: FigmaSyncDto,
    @Request() req: any,
  ): Promise<FigmaSyncResponseDto> {
    // Set forceSync to true for this operation
    const forceSyncDto = { ...syncDto, forceSync: true };
    
    return this.figmaIntegrationService.syncWithFigma(
      prototypeId, 
      forceSyncDto, 
      req.user.id
    );
  }

  /**
   * Get Figma sync status for prototype
   */
  @Post(':id/figma/status')
  @ApiOperation({ 
    summary: 'Get Figma sync status',
    description: 'Retrieves the current Figma integration status and last sync information for a prototype'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Sync status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        prototypeId: { type: 'string', format: 'uuid' },
        figmaFileId: { type: 'string', example: 'abc123def456' },
        integrationStatus: { 
          type: 'string', 
          enum: ['not_connected', 'connected', 'syncing', 'synced', 'sync_error'],
          example: 'synced'
        },
        lastSync: { type: 'string', format: 'date-time', example: '2024-01-23T10:30:00Z' },
        syncHistory: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              timestamp: { type: 'string', format: 'date-time' },
              changesCount: { type: 'number' },
              syncedBy: { type: 'string', format: 'uuid' },
            }
          }
        },
        nodeCount: { type: 'number', example: 15 },
        hasErrors: { type: 'boolean', example: false },
      }
    }
  })
  @ApiNotFoundResponse({ description: 'Prototype not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async getFigmaStatus(
    @Param('id', ParseUUIDPipe) prototypeId: string,
  ): Promise<any> {
    // This would be implemented in the service
    // For now, return a placeholder response
    return {
      prototypeId,
      figmaFileId: null,
      integrationStatus: 'not_connected',
      lastSync: null,
      syncHistory: [],
      nodeCount: 0,
      hasErrors: false,
    };
  }
}
