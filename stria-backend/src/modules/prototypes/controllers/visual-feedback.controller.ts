import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { VisualFeedbackService } from '../services/visual-feedback.service';
import {
  CreateVisualFeedbackDto,
  UpdateVisualFeedbackDto,
  VisualFeedbackResponseDto,
  VisualFeedbackQueryDto,
  PaginatedResponseDto,
} from '../dto';

/**
 * VisualFeedbackController
 * 
 * REST API controller for visual feedback management
 * Handles coordinate-based annotations, feedback status, and assignments
 */
@ApiTags('Visual Feedback')
@Controller('visual-feedback')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class VisualFeedbackController {
  constructor(private readonly visualFeedbackService: VisualFeedbackService) {}

  /**
   * Create a new visual feedback
   */
  @Post()
  @ApiOperation({ 
    summary: 'Create visual feedback',
    description: 'Creates a new coordinate-based visual feedback annotation on a prototype'
  })
  @ApiCreatedResponse({
    description: 'Visual feedback created successfully',
    type: VisualFeedbackResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async create(
    @Body() createVisualFeedbackDto: CreateVisualFeedbackDto,
    @Request() req: any,
  ): Promise<VisualFeedbackResponseDto> {
    return this.visualFeedbackService.create(createVisualFeedbackDto, req.user.id);
  }

  /**
   * Get all visual feedback with filtering and pagination
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get all visual feedback',
    description: 'Retrieves visual feedback with filtering, searching, and pagination support'
  })
  @ApiOkResponse({
    description: 'Visual feedback retrieved successfully',
    type: PaginatedResponseDto<VisualFeedbackResponseDto>,
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (1-based)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (max 100)' })
  @ApiQuery({ name: 'prototypeId', required: false, description: 'Filter by prototype ID' })
  @ApiQuery({ name: 'versionId', required: false, description: 'Filter by version ID' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by feedback type' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by feedback status' })
  @ApiQuery({ name: 'priority', required: false, description: 'Filter by priority' })
  @ApiQuery({ name: 'assignedTo', required: false, description: 'Filter by assigned user' })
  @ApiQuery({ name: 'search', required: false, description: 'Search in title and content' })
  @ApiQuery({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'unresolvedOnly', required: false, description: 'Show only unresolved feedback' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findAll(
    @Query() query: VisualFeedbackQueryDto,
  ): Promise<PaginatedResponseDto<VisualFeedbackResponseDto>> {
    return this.visualFeedbackService.findAll(query);
  }

  /**
   * Get a specific visual feedback by ID
   */
  @Get(':id')
  @ApiOperation({ 
    summary: 'Get visual feedback by ID',
    description: 'Retrieves a specific visual feedback with all related information'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Visual feedback UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Visual feedback retrieved successfully',
    type: VisualFeedbackResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Visual feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<VisualFeedbackResponseDto> {
    return this.visualFeedbackService.findOne(id);
  }

  /**
   * Update a visual feedback
   */
  @Patch(':id')
  @ApiOperation({ 
    summary: 'Update visual feedback',
    description: 'Updates an existing visual feedback with new information'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Visual feedback UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Visual feedback updated successfully',
    type: VisualFeedbackResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Visual feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateVisualFeedbackDto: UpdateVisualFeedbackDto,
    @Request() req: any,
  ): Promise<VisualFeedbackResponseDto> {
    return this.visualFeedbackService.update(id, updateVisualFeedbackDto, req.user.id);
  }

  /**
   * Delete a visual feedback (soft delete)
   */
  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete visual feedback',
    description: 'Soft deletes a visual feedback (can be restored later)'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Visual feedback UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({ description: 'Visual feedback deleted successfully' })
  @ApiNotFoundResponse({ description: 'Visual feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.visualFeedbackService.remove(id, req.user.id);
    return { message: 'Visual feedback deleted successfully' };
  }

  /**
   * Get visual feedback by prototype ID
   */
  @Get('prototype/:prototypeId')
  @ApiOperation({ 
    summary: 'Get feedback by prototype',
    description: 'Retrieves all visual feedback for a specific prototype'
  })
  @ApiParam({ 
    name: 'prototypeId', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Prototype visual feedback retrieved successfully',
    type: PaginatedResponseDto<VisualFeedbackResponseDto>,
  })
  @ApiNotFoundResponse({ description: 'Prototype not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findByPrototype(
    @Param('prototypeId', ParseUUIDPipe) prototypeId: string,
    @Query() query: VisualFeedbackQueryDto,
  ): Promise<PaginatedResponseDto<VisualFeedbackResponseDto>> {
    // Override prototypeId in query with the one from URL
    const prototypeQuery = { ...query, prototypeId };
    return this.visualFeedbackService.findAll(prototypeQuery);
  }

  /**
   * Get visual feedback by version ID
   */
  @Get('version/:versionId')
  @ApiOperation({ 
    summary: 'Get feedback by version',
    description: 'Retrieves all visual feedback for a specific prototype version'
  })
  @ApiParam({ 
    name: 'versionId', 
    description: 'Version UUID',
    format: 'uuid',
    example: '456e7890-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Version visual feedback retrieved successfully',
    type: PaginatedResponseDto<VisualFeedbackResponseDto>,
  })
  @ApiNotFoundResponse({ description: 'Version not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findByVersion(
    @Param('versionId', ParseUUIDPipe) versionId: string,
    @Query() query: VisualFeedbackQueryDto,
  ): Promise<PaginatedResponseDto<VisualFeedbackResponseDto>> {
    // Override versionId in query with the one from URL
    const versionQuery = { ...query, versionId };
    return this.visualFeedbackService.findAll(versionQuery);
  }

  /**
   * Assign visual feedback to user
   */
  @Patch(':id/assign')
  @ApiOperation({ 
    summary: 'Assign visual feedback',
    description: 'Assigns a visual feedback to a specific user'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Visual feedback UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Visual feedback assigned successfully',
    type: VisualFeedbackResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Visual feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async assign(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignDto: { assignedTo: string },
    @Request() req: any,
  ): Promise<VisualFeedbackResponseDto> {
    return this.visualFeedbackService.update(
      id, 
      { assignedTo: assignDto.assignedTo }, 
      req.user.id
    );
  }

  /**
   * Resolve visual feedback
   */
  @Patch(':id/resolve')
  @ApiOperation({ 
    summary: 'Resolve visual feedback',
    description: 'Marks a visual feedback as resolved'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Visual feedback UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Visual feedback resolved successfully',
    type: VisualFeedbackResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Visual feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async resolve(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<VisualFeedbackResponseDto> {
    return this.visualFeedbackService.update(
      id, 
      { status: 'resolved' as any }, 
      req.user.id
    );
  }

  /**
   * Get visual feedback statistics
   */
  @Get('stats/summary')
  @ApiOperation({ 
    summary: 'Get visual feedback statistics',
    description: 'Retrieves summary statistics for visual feedback'
  })
  @ApiOkResponse({
    description: 'Statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: 'Total number of feedback items' },
        byStatus: { 
          type: 'object', 
          description: 'Count by status',
          additionalProperties: { type: 'number' }
        },
        byType: { 
          type: 'object', 
          description: 'Count by type',
          additionalProperties: { type: 'number' }
        },
        byPriority: { 
          type: 'object', 
          description: 'Count by priority',
          additionalProperties: { type: 'number' }
        },
        unresolved: { type: 'number', description: 'Number of unresolved items' },
        assignedToMe: { type: 'number', description: 'Items assigned to current user' },
      }
    }
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async getStatistics(): Promise<any> {
    // This would be implemented in the service
    // For now, return a placeholder
    return {
      total: 0,
      byStatus: {},
      byType: {},
      byPriority: {},
      unresolved: 0,
      assignedToMe: 0,
    };
  }
}
