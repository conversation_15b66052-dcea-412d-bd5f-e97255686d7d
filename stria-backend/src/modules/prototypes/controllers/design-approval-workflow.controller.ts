import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { DesignApprovalWorkflowService } from '../services/design-approval-workflow.service';
import { ApprovalActionService } from '../services/approval-action.service';
import {
  CreateDesignApprovalWorkflowDto,
  UpdateDesignApprovalWorkflowDto,
  CreateApprovalActionDto,
  DesignApprovalWorkflowResponseDto,
  DesignApprovalWorkflowQueryDto,
  PaginatedResponseDto,
} from '../dto';

/**
 * DesignApprovalWorkflowController
 * 
 * REST API controller for design approval workflow management
 * Handles workflow creation, step management, and approval processing
 */
@ApiTags('Design Approval Workflows')
@Controller('design-approval-workflows')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DesignApprovalWorkflowController {
  constructor(
    private readonly workflowService: DesignApprovalWorkflowService,
    private readonly actionService: ApprovalActionService,
  ) {}

  /**
   * Create a new design approval workflow
   */
  @Post()
  @ApiOperation({ 
    summary: 'Create design approval workflow',
    description: 'Creates a new multi-step design approval workflow with configurable approval strategy'
  })
  @ApiCreatedResponse({
    description: 'Design approval workflow created successfully',
    type: DesignApprovalWorkflowResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async create(
    @Body() createWorkflowDto: CreateDesignApprovalWorkflowDto,
    @Request() req: any,
  ): Promise<DesignApprovalWorkflowResponseDto> {
    return this.workflowService.create(createWorkflowDto, req.user.id);
  }

  /**
   * Get all design approval workflows with filtering and pagination
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get all design approval workflows',
    description: 'Retrieves design approval workflows with filtering, searching, and pagination support'
  })
  @ApiOkResponse({
    description: 'Design approval workflows retrieved successfully',
    type: PaginatedResponseDto<DesignApprovalWorkflowResponseDto>,
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (1-based)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (max 100)' })
  @ApiQuery({ name: 'prototypeId', required: false, description: 'Filter by prototype ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by workflow status' })
  @ApiQuery({ name: 'priority', required: false, description: 'Filter by priority' })
  @ApiQuery({ name: 'assignedTo', required: false, description: 'Filter by assigned user' })
  @ApiQuery({ name: 'search', required: false, description: 'Search in name and description' })
  @ApiQuery({ name: 'activeOnly', required: false, description: 'Show only active workflows' })
  @ApiQuery({ name: 'overdueOnly', required: false, description: 'Show only overdue workflows' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findAll(
    @Query() query: DesignApprovalWorkflowQueryDto,
  ): Promise<PaginatedResponseDto<DesignApprovalWorkflowResponseDto>> {
    return this.workflowService.findAll(query);
  }

  /**
   * Get a specific design approval workflow by ID
   */
  @Get(':id')
  @ApiOperation({ 
    summary: 'Get design approval workflow by ID',
    description: 'Retrieves a specific design approval workflow with all steps and actions'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Design approval workflow UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Design approval workflow retrieved successfully',
    type: DesignApprovalWorkflowResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Design approval workflow not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<DesignApprovalWorkflowResponseDto> {
    return this.workflowService.findOne(id);
  }

  /**
   * Update a design approval workflow
   */
  @Patch(':id')
  @ApiOperation({ 
    summary: 'Update design approval workflow',
    description: 'Updates an existing design approval workflow configuration'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Design approval workflow UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Design approval workflow updated successfully',
    type: DesignApprovalWorkflowResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Design approval workflow not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateWorkflowDto: UpdateDesignApprovalWorkflowDto,
    @Request() req: any,
  ): Promise<DesignApprovalWorkflowResponseDto> {
    return this.workflowService.update(id, updateWorkflowDto, req.user.id);
  }

  /**
   * Delete a design approval workflow (soft delete)
   */
  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete design approval workflow',
    description: 'Soft deletes a design approval workflow (can be restored later)'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Design approval workflow UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({ description: 'Design approval workflow deleted successfully' })
  @ApiNotFoundResponse({ description: 'Design approval workflow not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.workflowService.remove(id, req.user.id);
    return { message: 'Design approval workflow deleted successfully' };
  }

  /**
   * Start a workflow
   */
  @Post(':id/start')
  @ApiOperation({ 
    summary: 'Start design approval workflow',
    description: 'Starts a draft workflow and activates the first approval step'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Design approval workflow UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Design approval workflow started successfully',
    type: DesignApprovalWorkflowResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Workflow cannot be started' })
  @ApiNotFoundResponse({ description: 'Design approval workflow not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async startWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<DesignApprovalWorkflowResponseDto> {
    return this.workflowService.startWorkflow(id, req.user.id);
  }

  /**
   * Submit approval action
   */
  @Post(':id/actions')
  @ApiOperation({ 
    summary: 'Submit approval action',
    description: 'Submits an approval, rejection, or other action for a workflow step'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Design approval workflow UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiCreatedResponse({
    description: 'Approval action submitted successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        type: { type: 'string' },
        decision: { type: 'string' },
        comment: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
      }
    }
  })
  @ApiBadRequestResponse({ description: 'Invalid action or step not available' })
  @ApiNotFoundResponse({ description: 'Design approval workflow not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async submitAction(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Body() createActionDto: CreateApprovalActionDto,
    @Request() req: any,
  ): Promise<any> {
    const action = await this.actionService.createAction(createActionDto, req.user.id);
    return {
      id: action.id,
      type: action.type,
      decision: action.decision,
      comment: action.comment,
      createdAt: action.createdAt,
    };
  }

  /**
   * Delegate approval step
   */
  @Post(':id/steps/:stepId/delegate')
  @ApiOperation({ 
    summary: 'Delegate approval step',
    description: 'Delegates an approval step to another user'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Design approval workflow UUID',
    format: 'uuid'
  })
  @ApiParam({ 
    name: 'stepId', 
    description: 'Approval step UUID',
    format: 'uuid'
  })
  @ApiOkResponse({ description: 'Approval step delegated successfully' })
  @ApiBadRequestResponse({ description: 'Step cannot be delegated' })
  @ApiNotFoundResponse({ description: 'Workflow or step not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async delegateStep(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Param('stepId', ParseUUIDPipe) stepId: string,
    @Body() delegateDto: { delegateToUserId: string; reason: string },
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.actionService.delegateApproval(
      stepId, 
      delegateDto.delegateToUserId, 
      delegateDto.reason,
      req.user.id
    );
    return { message: 'Approval step delegated successfully' };
  }

  /**
   * Skip approval step
   */
  @Post(':id/steps/:stepId/skip')
  @ApiOperation({ 
    summary: 'Skip approval step',
    description: 'Skips an approval step (if allowed by configuration)'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Design approval workflow UUID',
    format: 'uuid'
  })
  @ApiParam({ 
    name: 'stepId', 
    description: 'Approval step UUID',
    format: 'uuid'
  })
  @ApiOkResponse({ description: 'Approval step skipped successfully' })
  @ApiBadRequestResponse({ description: 'Step cannot be skipped' })
  @ApiNotFoundResponse({ description: 'Workflow or step not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async skipStep(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Param('stepId', ParseUUIDPipe) stepId: string,
    @Body() skipDto: { reason: string },
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.actionService.skipStep(stepId, skipDto.reason, req.user.id);
    return { message: 'Approval step skipped successfully' };
  }

  /**
   * Escalate approval step
   */
  @Post(':id/steps/:stepId/escalate')
  @ApiOperation({ 
    summary: 'Escalate approval step',
    description: 'Escalates an approval step to a higher authority'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Design approval workflow UUID',
    format: 'uuid'
  })
  @ApiParam({ 
    name: 'stepId', 
    description: 'Approval step UUID',
    format: 'uuid'
  })
  @ApiOkResponse({ description: 'Approval step escalated successfully' })
  @ApiNotFoundResponse({ description: 'Workflow or step not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async escalateStep(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Param('stepId', ParseUUIDPipe) stepId: string,
    @Body() escalateDto: { escalateToUserId: string; reason: string },
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.actionService.escalateStep(
      stepId, 
      escalateDto.escalateToUserId, 
      escalateDto.reason,
      req.user.id
    );
    return { message: 'Approval step escalated successfully' };
  }

  /**
   * Get workflows by prototype ID
   */
  @Get('prototype/:prototypeId')
  @ApiOperation({ 
    summary: 'Get workflows by prototype',
    description: 'Retrieves all design approval workflows for a specific prototype'
  })
  @ApiParam({ 
    name: 'prototypeId', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Prototype workflows retrieved successfully',
    type: PaginatedResponseDto<DesignApprovalWorkflowResponseDto>,
  })
  @ApiNotFoundResponse({ description: 'Prototype not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findByPrototype(
    @Param('prototypeId', ParseUUIDPipe) prototypeId: string,
    @Query() query: DesignApprovalWorkflowQueryDto,
  ): Promise<PaginatedResponseDto<DesignApprovalWorkflowResponseDto>> {
    // Override prototypeId in query with the one from URL
    const prototypeQuery = { ...query, prototypeId };
    return this.workflowService.findAll(prototypeQuery);
  }

  /**
   * Get workflow statistics
   */
  @Get('stats/summary')
  @ApiOperation({ 
    summary: 'Get workflow statistics',
    description: 'Retrieves summary statistics for design approval workflows'
  })
  @ApiOkResponse({
    description: 'Statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: 'Total number of workflows' },
        byStatus: { 
          type: 'object', 
          description: 'Count by status',
          additionalProperties: { type: 'number' }
        },
        byPriority: { 
          type: 'object', 
          description: 'Count by priority',
          additionalProperties: { type: 'number' }
        },
        active: { type: 'number', description: 'Number of active workflows' },
        overdue: { type: 'number', description: 'Number of overdue workflows' },
        pendingMyApproval: { type: 'number', description: 'Workflows pending current user approval' },
      }
    }
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async getStatistics(): Promise<any> {
    // This would be implemented in the service
    // For now, return a placeholder
    return {
      total: 0,
      byStatus: {},
      byPriority: {},
      active: 0,
      overdue: 0,
      pendingMyApproval: 0,
    };
  }
}
