import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CommentDiscussionService } from '../services/comment-discussion.service';
import {
  CreateCommentDiscussionDto,
  UpdateCommentDiscussionDto,
  AddCommentDto,
  AddCommentReactionDto,
  CommentVoteDto,
  MarkCommentSolutionDto,
  FlagCommentDto,
  CommentDiscussionResponseDto,
  CommentDiscussionQueryDto,
  PaginatedResponseDto,
} from '../dto';

/**
 * CommentDiscussionController
 * 
 * REST API controller for comment discussion management
 * Handles threaded discussions, comments, reactions, and moderation
 */
@ApiTags('Comment Discussions')
@Controller('comment-discussions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CommentDiscussionController {
  constructor(
    private readonly discussionService: CommentDiscussionService,
  ) {}

  /**
   * Create a new comment discussion
   */
  @Post()
  @ApiOperation({ 
    summary: 'Create comment discussion',
    description: 'Creates a new threaded comment discussion with initial comment'
  })
  @ApiCreatedResponse({
    description: 'Comment discussion created successfully',
    type: CommentDiscussionResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async create(
    @Body() createDiscussionDto: CreateCommentDiscussionDto,
    @Request() req: any,
  ): Promise<CommentDiscussionResponseDto> {
    return this.discussionService.create(createDiscussionDto, req.user.id);
  }

  /**
   * Get all comment discussions with filtering and pagination
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get all comment discussions',
    description: 'Retrieves comment discussions with filtering, searching, and pagination support'
  })
  @ApiOkResponse({
    description: 'Comment discussions retrieved successfully',
    type: PaginatedResponseDto<CommentDiscussionResponseDto>,
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (1-based)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (max 100)' })
  @ApiQuery({ name: 'prototypeId', required: false, description: 'Filter by prototype ID' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by discussion type' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by discussion status' })
  @ApiQuery({ name: 'priority', required: false, description: 'Filter by priority' })
  @ApiQuery({ name: 'search', required: false, description: 'Search in title and description' })
  @ApiQuery({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'unresolvedOnly', required: false, description: 'Show only unresolved discussions' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findAll(
    @Query() query: CommentDiscussionQueryDto,
    @Request() req: any,
  ): Promise<PaginatedResponseDto<CommentDiscussionResponseDto>> {
    return this.discussionService.findAll(query, req.user.id);
  }

  /**
   * Get a specific comment discussion by ID
   */
  @Get(':id')
  @ApiOperation({ 
    summary: 'Get comment discussion by ID',
    description: 'Retrieves a specific comment discussion with all comments and reactions'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Comment discussion UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Comment discussion retrieved successfully',
    type: CommentDiscussionResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Comment discussion not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<CommentDiscussionResponseDto> {
    return this.discussionService.findOne(id);
  }

  /**
   * Update a comment discussion
   */
  @Patch(':id')
  @ApiOperation({ 
    summary: 'Update comment discussion',
    description: 'Updates an existing comment discussion (creator only)'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Comment discussion UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Comment discussion updated successfully',
    type: CommentDiscussionResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Comment discussion not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Only creator can update discussion' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDiscussionDto: UpdateCommentDiscussionDto,
    @Request() req: any,
  ): Promise<CommentDiscussionResponseDto> {
    return this.discussionService.update(id, updateDiscussionDto, req.user.id);
  }

  /**
   * Delete a comment discussion (soft delete)
   */
  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete comment discussion',
    description: 'Soft deletes a comment discussion (creator only)'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Comment discussion UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({ description: 'Comment discussion deleted successfully' })
  @ApiNotFoundResponse({ description: 'Comment discussion not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Only creator can delete discussion' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.discussionService.remove(id, req.user.id);
    return { message: 'Comment discussion deleted successfully' };
  }

  /**
   * Add comment to discussion
   */
  @Post(':id/comments')
  @ApiOperation({ 
    summary: 'Add comment to discussion',
    description: 'Adds a new comment to an existing discussion thread'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Comment discussion UUID',
    format: 'uuid'
  })
  @ApiCreatedResponse({
    description: 'Comment added successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        content: { type: 'string' },
        type: { type: 'string' },
        threadLevel: { type: 'number' },
        createdAt: { type: 'string', format: 'date-time' },
      }
    }
  })
  @ApiBadRequestResponse({ description: 'Invalid comment data or discussion locked' })
  @ApiNotFoundResponse({ description: 'Comment discussion not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async addComment(
    @Param('id', ParseUUIDPipe) discussionId: string,
    @Body() addCommentDto: AddCommentDto,
    @Request() req: any,
  ): Promise<any> {
    // This would be implemented to add a comment
    // For now, return a placeholder
    return {
      id: 'comment-123',
      content: addCommentDto.content,
      type: addCommentDto.type,
      threadLevel: 0,
      createdAt: new Date(),
    };
  }

  /**
   * React to a comment
   */
  @Post(':id/comments/:commentId/reactions')
  @ApiOperation({ 
    summary: 'React to comment',
    description: 'Adds a reaction (like, love, etc.) to a comment'
  })
  @ApiParam({ name: 'id', description: 'Discussion UUID', format: 'uuid' })
  @ApiParam({ name: 'commentId', description: 'Comment UUID', format: 'uuid' })
  @ApiCreatedResponse({ description: 'Reaction added successfully' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async addReaction(
    @Param('id', ParseUUIDPipe) discussionId: string,
    @Param('commentId', ParseUUIDPipe) commentId: string,
    @Body() reactionDto: AddCommentReactionDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    // This would be implemented to add a reaction
    return { message: 'Reaction added successfully' };
  }

  /**
   * Vote on a comment
   */
  @Post(':id/comments/:commentId/vote')
  @ApiOperation({ 
    summary: 'Vote on comment',
    description: 'Upvotes or downvotes a comment'
  })
  @ApiParam({ name: 'id', description: 'Discussion UUID', format: 'uuid' })
  @ApiParam({ name: 'commentId', description: 'Comment UUID', format: 'uuid' })
  @ApiOkResponse({ description: 'Vote recorded successfully' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async voteComment(
    @Param('id', ParseUUIDPipe) discussionId: string,
    @Param('commentId', ParseUUIDPipe) commentId: string,
    @Body() voteDto: CommentVoteDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    // This would be implemented to record a vote
    return { message: 'Vote recorded successfully' };
  }

  /**
   * Mark comment as solution
   */
  @Post(':id/comments/:commentId/mark-solution')
  @ApiOperation({ 
    summary: 'Mark comment as solution',
    description: 'Marks a comment as the solution to the discussion'
  })
  @ApiParam({ name: 'id', description: 'Discussion UUID', format: 'uuid' })
  @ApiParam({ name: 'commentId', description: 'Comment UUID', format: 'uuid' })
  @ApiOkResponse({ description: 'Comment marked as solution successfully' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async markSolution(
    @Param('id', ParseUUIDPipe) discussionId: string,
    @Param('commentId', ParseUUIDPipe) commentId: string,
    @Body() solutionDto: MarkCommentSolutionDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    // This would be implemented to mark as solution
    return { message: 'Comment marked as solution successfully' };
  }

  /**
   * Flag comment for moderation
   */
  @Post(':id/comments/:commentId/flag')
  @ApiOperation({ 
    summary: 'Flag comment',
    description: 'Flags a comment for moderator review'
  })
  @ApiParam({ name: 'id', description: 'Discussion UUID', format: 'uuid' })
  @ApiParam({ name: 'commentId', description: 'Comment UUID', format: 'uuid' })
  @ApiOkResponse({ description: 'Comment flagged successfully' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async flagComment(
    @Param('id', ParseUUIDPipe) discussionId: string,
    @Param('commentId', ParseUUIDPipe) commentId: string,
    @Body() flagDto: FlagCommentDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    // This would be implemented to flag a comment
    return { message: 'Comment flagged successfully' };
  }

  /**
   * Get discussions by prototype ID
   */
  @Get('prototype/:prototypeId')
  @ApiOperation({ 
    summary: 'Get discussions by prototype',
    description: 'Retrieves all comment discussions for a specific prototype'
  })
  @ApiParam({ 
    name: 'prototypeId', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Prototype discussions retrieved successfully',
    type: PaginatedResponseDto<CommentDiscussionResponseDto>,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findByPrototype(
    @Param('prototypeId', ParseUUIDPipe) prototypeId: string,
    @Query() query: CommentDiscussionQueryDto,
    @Request() req: any,
  ): Promise<PaginatedResponseDto<CommentDiscussionResponseDto>> {
    // Override prototypeId in query with the one from URL
    const prototypeQuery = { ...query, prototypeId };
    return this.discussionService.findAll(prototypeQuery, req.user.id);
  }

  /**
   * Get discussion statistics
   */
  @Get('stats/summary')
  @ApiOperation({ 
    summary: 'Get discussion statistics',
    description: 'Retrieves summary statistics for comment discussions'
  })
  @ApiOkResponse({
    description: 'Statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: 'Total number of discussions' },
        byStatus: { 
          type: 'object', 
          description: 'Count by status',
          additionalProperties: { type: 'number' }
        },
        byType: { 
          type: 'object', 
          description: 'Count by type',
          additionalProperties: { type: 'number' }
        },
        active: { type: 'number', description: 'Number of active discussions' },
        withSolutions: { type: 'number', description: 'Discussions with solutions' },
        myParticipation: { type: 'number', description: 'Discussions I participate in' },
      }
    }
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async getStatistics(): Promise<any> {
    // This would be implemented in the service
    // For now, return a placeholder
    return {
      total: 0,
      byStatus: {},
      byType: {},
      active: 0,
      withSolutions: 0,
      myParticipation: 0,
    };
  }
}
