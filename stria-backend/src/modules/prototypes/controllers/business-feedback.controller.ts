import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { BusinessFeedbackService } from '../services/business-feedback.service';
import {
  CreateBusinessFeedbackDto,
  UpdateBusinessFeedbackDto,
  BusinessFeedbackResponseDto,
  BusinessFeedbackQueryDto,
  PaginatedResponseDto,
} from '../dto';

/**
 * BusinessFeedbackController
 * 
 * REST API controller for lightweight business feedback management
 * Focused on structured client-service provider communication
 */
@ApiTags('Business Feedback')
@Controller('business-feedback')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BusinessFeedbackController {
  constructor(
    private readonly businessFeedbackService: BusinessFeedbackService,
  ) {}

  /**
   * Create a new business feedback
   */
  @Post()
  @ApiOperation({ 
    summary: 'Create business feedback',
    description: 'Creates a new lightweight business feedback for structured client communication'
  })
  @ApiCreatedResponse({
    description: 'Business feedback created successfully',
    type: BusinessFeedbackResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async create(
    @Body() createFeedbackDto: CreateBusinessFeedbackDto,
    @Request() req: any,
  ): Promise<BusinessFeedbackResponseDto> {
    return this.businessFeedbackService.create(createFeedbackDto, req.user.id);
  }

  /**
   * Get all business feedback with filtering and pagination
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get all business feedback',
    description: 'Retrieves business feedback with filtering, searching, and pagination support'
  })
  @ApiOkResponse({
    description: 'Business feedback retrieved successfully',
    type: PaginatedResponseDto<BusinessFeedbackResponseDto>,
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (1-based)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (max 100)' })
  @ApiQuery({ name: 'prototypeId', required: false, description: 'Filter by prototype ID' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by feedback type' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by feedback status' })
  @ApiQuery({ name: 'priority', required: false, description: 'Filter by priority' })
  @ApiQuery({ name: 'search', required: false, description: 'Search in title and description' })
  @ApiQuery({ name: 'assignedToMeOnly', required: false, description: 'Show only feedback assigned to me' })
  @ApiQuery({ name: 'unresolvedOnly', required: false, description: 'Show only unresolved feedback' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findAll(
    @Query() query: BusinessFeedbackQueryDto,
    @Request() req: any,
  ): Promise<PaginatedResponseDto<BusinessFeedbackResponseDto>> {
    return this.businessFeedbackService.findAll(query, req.user.id);
  }

  /**
   * Get a specific business feedback by ID
   */
  @Get(':id')
  @ApiOperation({ 
    summary: 'Get business feedback by ID',
    description: 'Retrieves a specific business feedback with all details'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Business feedback UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Business feedback retrieved successfully',
    type: BusinessFeedbackResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Business feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<BusinessFeedbackResponseDto> {
    return this.businessFeedbackService.findOne(id);
  }

  /**
   * Update a business feedback
   */
  @Patch(':id')
  @ApiOperation({ 
    summary: 'Update business feedback',
    description: 'Updates an existing business feedback'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Business feedback UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Business feedback updated successfully',
    type: BusinessFeedbackResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Business feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateFeedbackDto: UpdateBusinessFeedbackDto,
    @Request() req: any,
  ): Promise<BusinessFeedbackResponseDto> {
    return this.businessFeedbackService.update(id, updateFeedbackDto, req.user.id);
  }

  /**
   * Delete a business feedback (soft delete)
   */
  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete business feedback',
    description: 'Soft deletes a business feedback'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Business feedback UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({ description: 'Business feedback deleted successfully' })
  @ApiNotFoundResponse({ description: 'Business feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.businessFeedbackService.remove(id, req.user.id);
    return { message: 'Business feedback deleted successfully' };
  }

  /**
   * Assign feedback to a user
   */
  @Post(':id/assign')
  @ApiOperation({ 
    summary: 'Assign feedback to user',
    description: 'Assigns a business feedback to a specific user'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Business feedback UUID',
    format: 'uuid'
  })
  @ApiOkResponse({
    description: 'Feedback assigned successfully',
    type: BusinessFeedbackResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Business feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async assignFeedback(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { assigneeId: string },
    @Request() req: any,
  ): Promise<BusinessFeedbackResponseDto> {
    return this.businessFeedbackService.assignFeedback(id, body.assigneeId, req.user.id);
  }

  /**
   * Resolve feedback
   */
  @Post(':id/resolve')
  @ApiOperation({ 
    summary: 'Resolve feedback',
    description: 'Marks a business feedback as resolved with resolution notes'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Business feedback UUID',
    format: 'uuid'
  })
  @ApiOkResponse({
    description: 'Feedback resolved successfully',
    type: BusinessFeedbackResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Business feedback not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async resolveFeedback(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { resolutionNotes: string },
    @Request() req: any,
  ): Promise<BusinessFeedbackResponseDto> {
    return this.businessFeedbackService.resolveFeedback(id, body.resolutionNotes, req.user.id);
  }

  /**
   * Get feedback by prototype ID
   */
  @Get('prototype/:prototypeId')
  @ApiOperation({ 
    summary: 'Get feedback by prototype',
    description: 'Retrieves all business feedback for a specific prototype'
  })
  @ApiParam({ 
    name: 'prototypeId', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Prototype feedback retrieved successfully',
    type: PaginatedResponseDto<BusinessFeedbackResponseDto>,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findByPrototype(
    @Param('prototypeId', ParseUUIDPipe) prototypeId: string,
    @Query() query: BusinessFeedbackQueryDto,
    @Request() req: any,
  ): Promise<PaginatedResponseDto<BusinessFeedbackResponseDto>> {
    // Override prototypeId in query with the one from URL
    const prototypeQuery = { ...query, prototypeId };
    return this.businessFeedbackService.findAll(prototypeQuery, req.user.id);
  }

  /**
   * Get feedback statistics
   */
  @Get('stats/summary')
  @ApiOperation({ 
    summary: 'Get feedback statistics',
    description: 'Retrieves summary statistics for business feedback'
  })
  @ApiOkResponse({
    description: 'Statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: 'Total number of feedback items' },
        byType: { 
          type: 'object', 
          description: 'Count by feedback type',
          additionalProperties: { type: 'number' }
        },
        byStatus: { 
          type: 'object', 
          description: 'Count by status',
          additionalProperties: { type: 'number' }
        },
        byPriority: { 
          type: 'object', 
          description: 'Count by priority',
          additionalProperties: { type: 'number' }
        },
        overdue: { type: 'number', description: 'Number of overdue feedback items' },
        resolved: { type: 'number', description: 'Number of resolved feedback items' },
        averageResolutionTime: { type: 'number', description: 'Average resolution time in days' },
      }
    }
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async getStatistics(
    @Query() filters: BusinessFeedbackQueryDto
  ): Promise<any> {
    return this.businessFeedbackService.getStatistics(filters);
  }

  /**
   * Get my assigned feedback
   */
  @Get('assigned/me')
  @ApiOperation({ 
    summary: 'Get my assigned feedback',
    description: 'Retrieves feedback assigned to the current user'
  })
  @ApiOkResponse({
    description: 'Assigned feedback retrieved successfully',
    type: PaginatedResponseDto<BusinessFeedbackResponseDto>,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async getMyAssignedFeedback(
    @Query() query: BusinessFeedbackQueryDto,
    @Request() req: any,
  ): Promise<PaginatedResponseDto<BusinessFeedbackResponseDto>> {
    const assignedQuery = { ...query, assignedToMeOnly: true };
    return this.businessFeedbackService.findAll(assignedQuery, req.user.id);
  }

  /**
   * Get my submitted feedback
   */
  @Get('submitted/me')
  @ApiOperation({ 
    summary: 'Get my submitted feedback',
    description: 'Retrieves feedback submitted by the current user'
  })
  @ApiOkResponse({
    description: 'Submitted feedback retrieved successfully',
    type: PaginatedResponseDto<BusinessFeedbackResponseDto>,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async getMySubmittedFeedback(
    @Query() query: BusinessFeedbackQueryDto,
    @Request() req: any,
  ): Promise<PaginatedResponseDto<BusinessFeedbackResponseDto>> {
    const submittedQuery = { ...query, submittedByMeOnly: true };
    return this.businessFeedbackService.findAll(submittedQuery, req.user.id);
  }
}
