import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { PrototypesService } from '../services/prototypes.service';
import {
  CreatePrototypeDto,
  UpdatePrototypeDto,
  PrototypeResponseDto,
  PrototypeQueryDto,
  PaginatedResponseDto,
} from '../dto';

/**
 * PrototypesController
 * 
 * REST API controller for prototype management
 * Handles CRUD operations and Figma integration
 */
@ApiTags('Prototypes')
@Controller('prototypes')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PrototypesController {
  constructor(private readonly prototypesService: PrototypesService) {}

  /**
   * Create a new prototype
   */
  @Post()
  @ApiOperation({ 
    summary: 'Create a new prototype',
    description: 'Creates a new design prototype with optional Figma integration'
  })
  @ApiCreatedResponse({
    description: 'Prototype created successfully',
    type: PrototypeResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async create(
    @Body() createPrototypeDto: CreatePrototypeDto,
    @Request() req: any,
  ): Promise<PrototypeResponseDto> {
    return this.prototypesService.create(createPrototypeDto, req.user.id);
  }

  /**
   * Get all prototypes with filtering and pagination
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get all prototypes',
    description: 'Retrieves prototypes with filtering, searching, and pagination support'
  })
  @ApiOkResponse({
    description: 'Prototypes retrieved successfully',
    type: PaginatedResponseDto<PrototypeResponseDto>,
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (1-based)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (max 100)' })
  @ApiQuery({ name: 'projectId', required: false, description: 'Filter by project ID' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by prototype type' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by prototype status' })
  @ApiQuery({ name: 'search', required: false, description: 'Search in name and description' })
  @ApiQuery({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order (ASC/DESC)' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findAll(
    @Query() query: PrototypeQueryDto,
  ): Promise<PaginatedResponseDto<PrototypeResponseDto>> {
    return this.prototypesService.findAll(query);
  }

  /**
   * Get a specific prototype by ID
   */
  @Get(':id')
  @ApiOperation({ 
    summary: 'Get prototype by ID',
    description: 'Retrieves a specific prototype with all related information'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Prototype retrieved successfully',
    type: PrototypeResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Prototype not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<PrototypeResponseDto> {
    return this.prototypesService.findOne(id);
  }

  /**
   * Update a prototype
   */
  @Patch(':id')
  @ApiOperation({ 
    summary: 'Update prototype',
    description: 'Updates an existing prototype with new information'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Prototype updated successfully',
    type: PrototypeResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Prototype not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePrototypeDto: UpdatePrototypeDto,
    @Request() req: any,
  ): Promise<PrototypeResponseDto> {
    return this.prototypesService.update(id, updatePrototypeDto, req.user.id);
  }

  /**
   * Delete a prototype (soft delete)
   */
  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete prototype',
    description: 'Soft deletes a prototype (can be restored later)'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'Prototype UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({ description: 'Prototype deleted successfully' })
  @ApiNotFoundResponse({ description: 'Prototype not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Insufficient permissions' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.prototypesService.remove(id, req.user.id);
    return { message: 'Prototype deleted successfully' };
  }

  /**
   * Get prototypes by project ID
   */
  @Get('project/:projectId')
  @ApiOperation({ 
    summary: 'Get prototypes by project',
    description: 'Retrieves all prototypes for a specific project'
  })
  @ApiParam({ 
    name: 'projectId', 
    description: 'Project UUID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiOkResponse({
    description: 'Project prototypes retrieved successfully',
    type: PaginatedResponseDto<PrototypeResponseDto>,
  })
  @ApiNotFoundResponse({ description: 'Project not found' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async findByProject(
    @Param('projectId', ParseUUIDPipe) projectId: string,
    @Query() query: PrototypeQueryDto,
  ): Promise<PaginatedResponseDto<PrototypeResponseDto>> {
    // Override projectId in query with the one from URL
    const projectQuery = { ...query, projectId };
    return this.prototypesService.findAll(projectQuery);
  }

  /**
   * Get prototype statistics
   */
  @Get('stats/summary')
  @ApiOperation({ 
    summary: 'Get prototype statistics',
    description: 'Retrieves summary statistics for prototypes'
  })
  @ApiOkResponse({
    description: 'Statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: 'Total number of prototypes' },
        byStatus: { 
          type: 'object', 
          description: 'Count by status',
          additionalProperties: { type: 'number' }
        },
        byType: { 
          type: 'object', 
          description: 'Count by type',
          additionalProperties: { type: 'number' }
        },
        withFigmaIntegration: { type: 'number', description: 'Prototypes with Figma integration' },
        recentlyUpdated: { type: 'number', description: 'Updated in last 7 days' },
      }
    }
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  async getStatistics(): Promise<any> {
    // This would be implemented in the service
    // For now, return a placeholder
    return {
      total: 0,
      byStatus: {},
      byType: {},
      withFigmaIntegration: 0,
      recentlyUpdated: 0,
    };
  }
}
