import { Test, TestingModule } from '@nestjs/testing';
import { CommentDiscussionController } from '../comment-discussion.controller';
import { CommentDiscussionService } from '../../services/comment-discussion.service';
import { 
  CreateCommentDiscussionDto, 
  UpdateCommentDiscussionDto, 
  AddCommentDto,
  CommentDiscussionQueryDto 
} from '../../dto';
import { 
  DiscussionType, 
  DiscussionStatus, 
  DiscussionPriority,
  CommentType
} from '../../enums/comment-discussion.enum';

describe('CommentDiscussionController', () => {
  let controller: CommentDiscussionController;
  let service: CommentDiscussionService;

  const mockDiscussionService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
  };

  const mockRequest = {
    user: mockUser,
  };

  const mockDiscussionResponse = {
    id: 'discussion-123',
    title: 'Design feedback for mobile layout',
    description: 'Let\'s discuss the mobile layout design',
    type: DiscussionType.FEEDBACK,
    status: DiscussionStatus.OPEN,
    priority: DiscussionPriority.MEDIUM,
    commentCount: 1,
    participantCount: 1,
    isPrivate: false,
    allowAnonymous: false,
    requiresModeration: false,
    isPinned: false,
    isLocked: false,
    hasSolution: false,
    prototypeId: 'prototype-123',
    createdBy: 'user-123',
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommentDiscussionController],
      providers: [
        {
          provide: CommentDiscussionService,
          useValue: mockDiscussionService,
        },
      ],
    }).compile();

    controller = module.get<CommentDiscussionController>(CommentDiscussionController);
    service = module.get<CommentDiscussionService>(CommentDiscussionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new comment discussion', async () => {
      const createDto: CreateCommentDiscussionDto = {
        title: 'Design feedback for mobile layout',
        description: 'Let\'s discuss the mobile layout design',
        content: 'I think the mobile layout needs some adjustments',
        type: DiscussionType.FEEDBACK,
        prototypeId: 'prototype-123',
      };

      mockDiscussionService.create.mockResolvedValue(mockDiscussionResponse);

      const result = await controller.create(createDto, mockRequest);

      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(result).toEqual(mockDiscussionResponse);
    });

    it('should handle creation with mentions and participants', async () => {
      const createDto: CreateCommentDiscussionDto = {
        title: 'Team Discussion',
        content: 'Let\'s get everyone\'s input on this design',
        prototypeId: 'prototype-123',
        mentions: [
          {
            userId: 'user-456',
            startPosition: 10,
            length: 8,
            displayName: '@john.doe',
          },
        ],
        participants: ['user-456', 'user-789'],
        moderators: ['user-123'],
        tags: ['design', 'feedback', 'mobile'],
      };

      mockDiscussionService.create.mockResolvedValue(mockDiscussionResponse);

      const result = await controller.create(createDto, mockRequest);

      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(result).toEqual(mockDiscussionResponse);
    });
  });

  describe('findAll', () => {
    it('should return paginated discussions', async () => {
      const query: CommentDiscussionQueryDto = {
        page: 1,
        limit: 20,
        prototypeId: 'prototype-123',
      };

      const paginatedResponse = {
        data: [mockDiscussionResponse],
        meta: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      };

      mockDiscussionService.findAll.mockResolvedValue(paginatedResponse);

      const result = await controller.findAll(query, mockRequest);

      expect(service.findAll).toHaveBeenCalledWith(query, mockUser.id);
      expect(result).toEqual(paginatedResponse);
    });

    it('should handle filtering and search', async () => {
      const query: CommentDiscussionQueryDto = {
        search: 'mobile layout',
        type: DiscussionType.FEEDBACK,
        status: DiscussionStatus.OPEN,
        priority: DiscussionPriority.HIGH,
        tags: ['design', 'mobile'],
        unresolvedOnly: true,
        myParticipationOnly: true,
      };

      mockDiscussionService.findAll.mockResolvedValue({
        data: [],
        meta: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findAll(query, mockRequest);

      expect(service.findAll).toHaveBeenCalledWith(query, mockUser.id);
    });
  });

  describe('findOne', () => {
    it('should return a single discussion', async () => {
      const discussionId = 'discussion-123';

      mockDiscussionService.findOne.mockResolvedValue(mockDiscussionResponse);

      const result = await controller.findOne(discussionId);

      expect(service.findOne).toHaveBeenCalledWith(discussionId);
      expect(result).toEqual(mockDiscussionResponse);
    });
  });

  describe('update', () => {
    it('should update a discussion', async () => {
      const discussionId = 'discussion-123';
      const updateDto: UpdateCommentDiscussionDto = {
        title: 'Updated Discussion Title',
        priority: DiscussionPriority.HIGH,
        tags: ['design', 'urgent'],
      };

      const updatedResponse = {
        ...mockDiscussionResponse,
        title: 'Updated Discussion Title',
        priority: DiscussionPriority.HIGH,
      };

      mockDiscussionService.update.mockResolvedValue(updatedResponse);

      const result = await controller.update(discussionId, updateDto, mockRequest);

      expect(service.update).toHaveBeenCalledWith(discussionId, updateDto, mockUser.id);
      expect(result).toEqual(updatedResponse);
    });
  });

  describe('remove', () => {
    it('should delete a discussion', async () => {
      const discussionId = 'discussion-123';

      mockDiscussionService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(discussionId, mockRequest);

      expect(service.remove).toHaveBeenCalledWith(discussionId, mockUser.id);
      expect(result).toEqual({ message: 'Comment discussion deleted successfully' });
    });
  });

  describe('addComment', () => {
    it('should add a comment to discussion', async () => {
      const discussionId = 'discussion-123';
      const addCommentDto: AddCommentDto = {
        content: 'I agree with the previous feedback',
        type: CommentType.REPLY,
        discussionId: discussionId,
        parentCommentId: 'comment-456',
      };

      const result = await controller.addComment(discussionId, addCommentDto, mockRequest);

      expect(result).toEqual({
        id: 'comment-123',
        content: addCommentDto.content,
        type: addCommentDto.type,
        threadLevel: 0,
        createdAt: expect.any(Date),
      });
    });

    it('should handle comment with mentions and attachments', async () => {
      const discussionId = 'discussion-123';
      const addCommentDto: AddCommentDto = {
        content: 'Great point @john.doe! I\'ve attached some mockups',
        discussionId: discussionId,
        mentions: [
          {
            userId: 'user-456',
            startPosition: 12,
            length: 9,
            displayName: '@john.doe',
          },
        ],
        attachments: ['mockup1.png', 'mockup2.png'],
        tags: ['suggestion', 'mockup'],
        estimatedHours: 2.5,
        impactLevel: 'medium',
      };

      const result = await controller.addComment(discussionId, addCommentDto, mockRequest);

      expect(result).toBeDefined();
      expect(result.content).toBe(addCommentDto.content);
    });
  });

  describe('addReaction', () => {
    it('should add reaction to comment', async () => {
      const discussionId = 'discussion-123';
      const commentId = 'comment-456';
      const reactionDto = {
        type: 'like' as any,
        commentId: commentId,
        note: 'Great suggestion!',
      };

      const result = await controller.addReaction(discussionId, commentId, reactionDto, mockRequest);

      expect(result).toEqual({ message: 'Reaction added successfully' });
    });
  });

  describe('voteComment', () => {
    it('should vote on comment', async () => {
      const discussionId = 'discussion-123';
      const commentId = 'comment-456';
      const voteDto = {
        voteType: 'upvote' as any,
        commentId: commentId,
        reason: 'This addresses the core issue',
      };

      const result = await controller.voteComment(discussionId, commentId, voteDto, mockRequest);

      expect(result).toEqual({ message: 'Vote recorded successfully' });
    });
  });

  describe('markSolution', () => {
    it('should mark comment as solution', async () => {
      const discussionId = 'discussion-123';
      const commentId = 'comment-456';
      const solutionDto = {
        commentId: commentId,
        reason: 'This solution addresses all concerns',
        closeDiscussion: true,
      };

      const result = await controller.markSolution(discussionId, commentId, solutionDto, mockRequest);

      expect(result).toEqual({ message: 'Comment marked as solution successfully' });
    });
  });

  describe('flagComment', () => {
    it('should flag comment for moderation', async () => {
      const discussionId = 'discussion-123';
      const commentId = 'comment-456';
      const flagDto = {
        commentId: commentId,
        reason: 'inappropriate',
        details: 'This comment contains offensive language',
      };

      const result = await controller.flagComment(discussionId, commentId, flagDto, mockRequest);

      expect(result).toEqual({ message: 'Comment flagged successfully' });
    });
  });

  describe('findByPrototype', () => {
    it('should return discussions for a specific prototype', async () => {
      const prototypeId = 'prototype-123';
      const query: CommentDiscussionQueryDto = {
        page: 1,
        limit: 20,
      };

      const expectedQuery = { ...query, prototypeId };

      mockDiscussionService.findAll.mockResolvedValue({
        data: [mockDiscussionResponse],
        meta: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findByPrototype(prototypeId, query, mockRequest);

      expect(service.findAll).toHaveBeenCalledWith(expectedQuery, mockUser.id);
    });
  });

  describe('getStatistics', () => {
    it('should return discussion statistics', async () => {
      const result = await controller.getStatistics();

      expect(result).toEqual({
        total: 0,
        byStatus: {},
        byType: {},
        active: 0,
        withSolutions: 0,
        myParticipation: 0,
      });
    });
  });
});
