import { Test, TestingModule } from '@nestjs/testing';
import { VisualFeedbackController } from '../visual-feedback.controller';
import { VisualFeedbackService } from '../../services/visual-feedback.service';
import { CreateVisualFeedbackDto, UpdateVisualFeedbackDto, VisualFeedbackQueryDto } from '../../dto';
import { FeedbackType, FeedbackStatus, FeedbackPriority, AnnotationType } from '../../enums/visual-feedback.enum';

describe('VisualFeedbackController', () => {
  let controller: VisualFeedbackController;
  let service: VisualFeedbackService;

  const mockVisualFeedbackService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
  };

  const mockRequest = {
    user: mockUser,
  };

  const mockFeedbackResponse = {
    id: 'feedback-123',
    title: 'Button alignment issue',
    content: 'The submit button appears to be misaligned',
    type: FeedbackType.DESIGN_ISSUE,
    status: FeedbackStatus.OPEN,
    priority: FeedbackPriority.MEDIUM,
    annotationType: AnnotationType.RECTANGLE,
    positionX: 150.5,
    positionY: 200.75,
    positionZ: 1,
    width: 100,
    height: 50,
    color: '#FF6B6B',
    opacity: 0.8,
    prototypeId: 'prototype-123',
    createdBy: 'user-123',
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [VisualFeedbackController],
      providers: [
        {
          provide: VisualFeedbackService,
          useValue: mockVisualFeedbackService,
        },
      ],
    }).compile();

    controller = module.get<VisualFeedbackController>(VisualFeedbackController);
    service = module.get<VisualFeedbackService>(VisualFeedbackService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new visual feedback', async () => {
      const createDto: CreateVisualFeedbackDto = {
        title: 'Button alignment issue',
        content: 'The submit button appears to be misaligned',
        type: FeedbackType.DESIGN_ISSUE,
        annotationType: AnnotationType.RECTANGLE,
        positionX: 150.5,
        positionY: 200.75,
        width: 100,
        height: 50,
        prototypeId: 'prototype-123',
      };

      mockVisualFeedbackService.create.mockResolvedValue(mockFeedbackResponse);

      const result = await controller.create(createDto, mockRequest);

      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(result).toEqual(mockFeedbackResponse);
    });

    it('should handle creation with metadata', async () => {
      const createDto: CreateVisualFeedbackDto = {
        content: 'Test feedback',
        positionX: 100,
        positionY: 200,
        prototypeId: 'prototype-123',
        tags: ['ui', 'critical'],
        category: 'usability',
        severity: 'high',
        estimatedEffort: 2.5,
      };

      mockVisualFeedbackService.create.mockResolvedValue(mockFeedbackResponse);

      const result = await controller.create(createDto, mockRequest);

      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(result).toEqual(mockFeedbackResponse);
    });
  });

  describe('findAll', () => {
    it('should return paginated visual feedback', async () => {
      const query: VisualFeedbackQueryDto = {
        page: 1,
        limit: 20,
        prototypeId: 'prototype-123',
      };

      const paginatedResponse = {
        data: [mockFeedbackResponse],
        meta: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      };

      mockVisualFeedbackService.findAll.mockResolvedValue(paginatedResponse);

      const result = await controller.findAll(query);

      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual(paginatedResponse);
    });

    it('should handle filtering and search', async () => {
      const query: VisualFeedbackQueryDto = {
        search: 'button',
        type: FeedbackType.DESIGN_ISSUE,
        status: FeedbackStatus.OPEN,
        priority: FeedbackPriority.HIGH,
        tags: ['ui', 'critical'],
        unresolvedOnly: true,
      };

      mockVisualFeedbackService.findAll.mockResolvedValue({
        data: [],
        meta: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findAll(query);

      expect(service.findAll).toHaveBeenCalledWith(query);
    });

    it('should handle position-based filtering', async () => {
      const query: VisualFeedbackQueryDto = {
        minX: 100,
        maxX: 500,
        minY: 200,
        maxY: 800,
      };

      mockVisualFeedbackService.findAll.mockResolvedValue({
        data: [],
        meta: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findAll(query);

      expect(service.findAll).toHaveBeenCalledWith(query);
    });
  });

  describe('findOne', () => {
    it('should return a single visual feedback', async () => {
      const feedbackId = 'feedback-123';

      mockVisualFeedbackService.findOne.mockResolvedValue(mockFeedbackResponse);

      const result = await controller.findOne(feedbackId);

      expect(service.findOne).toHaveBeenCalledWith(feedbackId);
      expect(result).toEqual(mockFeedbackResponse);
    });
  });

  describe('update', () => {
    it('should update a visual feedback', async () => {
      const feedbackId = 'feedback-123';
      const updateDto: UpdateVisualFeedbackDto = {
        title: 'Updated feedback title',
        status: FeedbackStatus.IN_PROGRESS,
        priority: FeedbackPriority.HIGH,
      };

      const updatedResponse = {
        ...mockFeedbackResponse,
        title: 'Updated feedback title',
        status: FeedbackStatus.IN_PROGRESS,
        priority: FeedbackPriority.HIGH,
      };

      mockVisualFeedbackService.update.mockResolvedValue(updatedResponse);

      const result = await controller.update(feedbackId, updateDto, mockRequest);

      expect(service.update).toHaveBeenCalledWith(feedbackId, updateDto, mockUser.id);
      expect(result).toEqual(updatedResponse);
    });

    it('should handle position and styling updates', async () => {
      const feedbackId = 'feedback-123';
      const updateDto: UpdateVisualFeedbackDto = {
        positionX: 200,
        positionY: 300,
        color: '#00FF00',
        opacity: 0.9,
      };

      mockVisualFeedbackService.update.mockResolvedValue(mockFeedbackResponse);

      await controller.update(feedbackId, updateDto, mockRequest);

      expect(service.update).toHaveBeenCalledWith(feedbackId, updateDto, mockUser.id);
    });
  });

  describe('remove', () => {
    it('should delete a visual feedback', async () => {
      const feedbackId = 'feedback-123';

      mockVisualFeedbackService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(feedbackId, mockRequest);

      expect(service.remove).toHaveBeenCalledWith(feedbackId, mockUser.id);
      expect(result).toEqual({ message: 'Visual feedback deleted successfully' });
    });
  });

  describe('findByPrototype', () => {
    it('should return feedback for a specific prototype', async () => {
      const prototypeId = 'prototype-123';
      const query: VisualFeedbackQueryDto = {
        page: 1,
        limit: 20,
      };

      const expectedQuery = { ...query, prototypeId };

      mockVisualFeedbackService.findAll.mockResolvedValue({
        data: [mockFeedbackResponse],
        meta: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findByPrototype(prototypeId, query);

      expect(service.findAll).toHaveBeenCalledWith(expectedQuery);
    });
  });

  describe('findByVersion', () => {
    it('should return feedback for a specific version', async () => {
      const versionId = 'version-123';
      const query: VisualFeedbackQueryDto = {
        page: 1,
        limit: 20,
      };

      const expectedQuery = { ...query, versionId };

      mockVisualFeedbackService.findAll.mockResolvedValue({
        data: [mockFeedbackResponse],
        meta: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findByVersion(versionId, query);

      expect(service.findAll).toHaveBeenCalledWith(expectedQuery);
    });
  });

  describe('assign', () => {
    it('should assign feedback to a user', async () => {
      const feedbackId = 'feedback-123';
      const assignDto = { assignedTo: 'user-456' };

      mockVisualFeedbackService.update.mockResolvedValue(mockFeedbackResponse);

      const result = await controller.assign(feedbackId, assignDto, mockRequest);

      expect(service.update).toHaveBeenCalledWith(
        feedbackId,
        { assignedTo: 'user-456' },
        mockUser.id
      );
      expect(result).toEqual(mockFeedbackResponse);
    });
  });

  describe('resolve', () => {
    it('should resolve feedback', async () => {
      const feedbackId = 'feedback-123';

      const resolvedResponse = {
        ...mockFeedbackResponse,
        status: FeedbackStatus.RESOLVED,
      };

      mockVisualFeedbackService.update.mockResolvedValue(resolvedResponse);

      const result = await controller.resolve(feedbackId, mockRequest);

      expect(service.update).toHaveBeenCalledWith(
        feedbackId,
        { status: 'resolved' },
        mockUser.id
      );
      expect(result).toEqual(resolvedResponse);
    });
  });

  describe('getStatistics', () => {
    it('should return visual feedback statistics', async () => {
      const result = await controller.getStatistics();

      expect(result).toEqual({
        total: 0,
        byStatus: {},
        byType: {},
        byPriority: {},
        unresolved: 0,
        assignedToMe: 0,
      });
    });
  });
});
