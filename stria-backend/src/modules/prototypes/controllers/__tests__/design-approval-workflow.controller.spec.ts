import { Test, TestingModule } from '@nestjs/testing';
import { DesignApprovalWorkflowController } from '../design-approval-workflow.controller';
import { DesignApprovalWorkflowService } from '../../services/design-approval-workflow.service';
import { ApprovalActionService } from '../../services/approval-action.service';
import { 
  CreateDesignApprovalWorkflowDto, 
  UpdateDesignApprovalWorkflowDto, 
  CreateApprovalActionDto,
  DesignApprovalWorkflowQueryDto 
} from '../../dto';
import { 
  WorkflowType, 
  WorkflowStatus, 
  ApprovalStrategy,
  WorkflowPriority 
} from '../../enums/design-approval-workflow.enum';
import { ActionType, ApprovalDecision } from '../../enums/approval-action.enum';

describe('DesignApprovalWorkflowController', () => {
  let controller: DesignApprovalWorkflowController;
  let workflowService: DesignApprovalWorkflowService;
  let actionService: ApprovalActionService;

  const mockWorkflowService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    startWorkflow: jest.fn(),
  };

  const mockActionService = {
    createAction: jest.fn(),
    delegateApproval: jest.fn(),
    skipStep: jest.fn(),
    escalateStep: jest.fn(),
  };

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
  };

  const mockRequest = {
    user: mockUser,
  };

  const mockWorkflowResponse = {
    id: 'workflow-123',
    name: 'Mobile App Design Approval',
    description: 'Complete approval process for mobile app design',
    type: WorkflowType.SEQUENTIAL,
    status: WorkflowStatus.DRAFT,
    approvalStrategy: ApprovalStrategy.ALL_REQUIRED,
    requiredApprovals: 3,
    allowParallelApproval: false,
    currentStep: 1,
    totalSteps: 3,
    completedSteps: 0,
    approvalPercentage: 0,
    prototypeId: 'prototype-123',
    createdBy: 'user-123',
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DesignApprovalWorkflowController],
      providers: [
        {
          provide: DesignApprovalWorkflowService,
          useValue: mockWorkflowService,
        },
        {
          provide: ApprovalActionService,
          useValue: mockActionService,
        },
      ],
    }).compile();

    controller = module.get<DesignApprovalWorkflowController>(DesignApprovalWorkflowController);
    workflowService = module.get<DesignApprovalWorkflowService>(DesignApprovalWorkflowService);
    actionService = module.get<ApprovalActionService>(ApprovalActionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new design approval workflow', async () => {
      const createDto: CreateDesignApprovalWorkflowDto = {
        name: 'Mobile App Design Approval',
        description: 'Complete approval process for mobile app design',
        type: WorkflowType.SEQUENTIAL,
        approvalStrategy: ApprovalStrategy.ALL_REQUIRED,
        prototypeId: 'prototype-123',
        steps: [
          {
            stepName: 'Design Review',
            requiredApprovers: 1,
            approverIds: ['user-456'],
          },
          {
            stepName: 'Stakeholder Approval',
            requiredApprovers: 2,
            approverIds: ['user-789', 'user-012'],
          },
        ],
      };

      mockWorkflowService.create.mockResolvedValue(mockWorkflowResponse);

      const result = await controller.create(createDto, mockRequest);

      expect(workflowService.create).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(result).toEqual(mockWorkflowResponse);
    });

    it('should handle creation with advanced configuration', async () => {
      const createDto: CreateDesignApprovalWorkflowDto = {
        name: 'Advanced Workflow',
        prototypeId: 'prototype-123',
        type: WorkflowType.PARALLEL,
        approvalStrategy: ApprovalStrategy.MAJORITY_REQUIRED,
        allowParallelApproval: true,
        autoApproveOnThreshold: true,
        approvalThresholdPercentage: 75.0,
        dueDate: '2024-02-15T17:00:00Z',
        priority: WorkflowPriority.HIGH,
        enableEmailNotifications: true,
        steps: [
          {
            stepName: 'Quick Review',
            requiredApprovers: 1,
          },
        ],
      };

      mockWorkflowService.create.mockResolvedValue(mockWorkflowResponse);

      const result = await controller.create(createDto, mockRequest);

      expect(workflowService.create).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(result).toEqual(mockWorkflowResponse);
    });
  });

  describe('findAll', () => {
    it('should return paginated workflows', async () => {
      const query: DesignApprovalWorkflowQueryDto = {
        page: 1,
        limit: 20,
        prototypeId: 'prototype-123',
      };

      const paginatedResponse = {
        data: [mockWorkflowResponse],
        meta: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      };

      mockWorkflowService.findAll.mockResolvedValue(paginatedResponse);

      const result = await controller.findAll(query);

      expect(workflowService.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual(paginatedResponse);
    });

    it('should handle filtering and search', async () => {
      const query: DesignApprovalWorkflowQueryDto = {
        search: 'mobile',
        status: WorkflowStatus.ACTIVE,
        priority: WorkflowPriority.HIGH,
        activeOnly: true,
        overdueOnly: false,
      };

      mockWorkflowService.findAll.mockResolvedValue({
        data: [],
        meta: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findAll(query);

      expect(workflowService.findAll).toHaveBeenCalledWith(query);
    });
  });

  describe('findOne', () => {
    it('should return a single workflow', async () => {
      const workflowId = 'workflow-123';

      mockWorkflowService.findOne.mockResolvedValue(mockWorkflowResponse);

      const result = await controller.findOne(workflowId);

      expect(workflowService.findOne).toHaveBeenCalledWith(workflowId);
      expect(result).toEqual(mockWorkflowResponse);
    });
  });

  describe('update', () => {
    it('should update a workflow', async () => {
      const workflowId = 'workflow-123';
      const updateDto: UpdateDesignApprovalWorkflowDto = {
        name: 'Updated Workflow Name',
        priority: WorkflowPriority.HIGH,
        dueDate: '2024-03-01T17:00:00Z',
      };

      const updatedResponse = {
        ...mockWorkflowResponse,
        name: 'Updated Workflow Name',
        priority: WorkflowPriority.HIGH,
      };

      mockWorkflowService.update.mockResolvedValue(updatedResponse);

      const result = await controller.update(workflowId, updateDto, mockRequest);

      expect(workflowService.update).toHaveBeenCalledWith(workflowId, updateDto, mockUser.id);
      expect(result).toEqual(updatedResponse);
    });
  });

  describe('remove', () => {
    it('should delete a workflow', async () => {
      const workflowId = 'workflow-123';

      mockWorkflowService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(workflowId, mockRequest);

      expect(workflowService.remove).toHaveBeenCalledWith(workflowId, mockUser.id);
      expect(result).toEqual({ message: 'Design approval workflow deleted successfully' });
    });
  });

  describe('startWorkflow', () => {
    it('should start a workflow', async () => {
      const workflowId = 'workflow-123';

      const startedResponse = {
        ...mockWorkflowResponse,
        status: WorkflowStatus.ACTIVE,
        startedAt: new Date(),
      };

      mockWorkflowService.startWorkflow.mockResolvedValue(startedResponse);

      const result = await controller.startWorkflow(workflowId, mockRequest);

      expect(workflowService.startWorkflow).toHaveBeenCalledWith(workflowId, mockUser.id);
      expect(result).toEqual(startedResponse);
    });
  });

  describe('submitAction', () => {
    it('should submit an approval action', async () => {
      const workflowId = 'workflow-123';
      const actionDto: CreateApprovalActionDto = {
        type: ActionType.APPROVE,
        decision: ApprovalDecision.APPROVED,
        comment: 'Design looks great!',
        stepId: 'step-123',
      };

      const mockAction = {
        id: 'action-123',
        type: ActionType.APPROVE,
        decision: ApprovalDecision.APPROVED,
        comment: 'Design looks great!',
        createdAt: new Date(),
      };

      mockActionService.createAction.mockResolvedValue(mockAction);

      const result = await controller.submitAction(workflowId, actionDto, mockRequest);

      expect(actionService.createAction).toHaveBeenCalledWith(actionDto, mockUser.id);
      expect(result).toEqual({
        id: mockAction.id,
        type: mockAction.type,
        decision: mockAction.decision,
        comment: mockAction.comment,
        createdAt: mockAction.createdAt,
      });
    });
  });

  describe('delegateStep', () => {
    it('should delegate an approval step', async () => {
      const workflowId = 'workflow-123';
      const stepId = 'step-123';
      const delegateDto = {
        delegateToUserId: 'user-456',
        reason: 'I am not available this week',
      };

      mockActionService.delegateApproval.mockResolvedValue(undefined);

      const result = await controller.delegateStep(workflowId, stepId, delegateDto, mockRequest);

      expect(actionService.delegateApproval).toHaveBeenCalledWith(
        stepId,
        delegateDto.delegateToUserId,
        delegateDto.reason,
        mockUser.id
      );
      expect(result).toEqual({ message: 'Approval step delegated successfully' });
    });
  });

  describe('skipStep', () => {
    it('should skip an approval step', async () => {
      const workflowId = 'workflow-123';
      const stepId = 'step-123';
      const skipDto = { reason: 'Step not applicable for this design' };

      mockActionService.skipStep.mockResolvedValue(undefined);

      const result = await controller.skipStep(workflowId, stepId, skipDto, mockRequest);

      expect(actionService.skipStep).toHaveBeenCalledWith(stepId, skipDto.reason, mockUser.id);
      expect(result).toEqual({ message: 'Approval step skipped successfully' });
    });
  });

  describe('escalateStep', () => {
    it('should escalate an approval step', async () => {
      const workflowId = 'workflow-123';
      const stepId = 'step-123';
      const escalateDto = {
        escalateToUserId: 'manager-456',
        reason: 'Need higher authority approval',
      };

      mockActionService.escalateStep.mockResolvedValue(undefined);

      const result = await controller.escalateStep(workflowId, stepId, escalateDto, mockRequest);

      expect(actionService.escalateStep).toHaveBeenCalledWith(
        stepId,
        escalateDto.escalateToUserId,
        escalateDto.reason,
        mockUser.id
      );
      expect(result).toEqual({ message: 'Approval step escalated successfully' });
    });
  });

  describe('findByPrototype', () => {
    it('should return workflows for a specific prototype', async () => {
      const prototypeId = 'prototype-123';
      const query: DesignApprovalWorkflowQueryDto = {
        page: 1,
        limit: 20,
      };

      const expectedQuery = { ...query, prototypeId };

      mockWorkflowService.findAll.mockResolvedValue({
        data: [mockWorkflowResponse],
        meta: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findByPrototype(prototypeId, query);

      expect(workflowService.findAll).toHaveBeenCalledWith(expectedQuery);
    });
  });

  describe('getStatistics', () => {
    it('should return workflow statistics', async () => {
      const result = await controller.getStatistics();

      expect(result).toEqual({
        total: 0,
        byStatus: {},
        byPriority: {},
        active: 0,
        overdue: 0,
        pendingMyApproval: 0,
      });
    });
  });
});
