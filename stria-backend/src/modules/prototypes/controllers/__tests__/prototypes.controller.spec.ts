import { Test, TestingModule } from '@nestjs/testing';
import { PrototypesController } from '../prototypes.controller';
import { PrototypesService } from '../../services/prototypes.service';
import { CreatePrototypeDto, UpdatePrototypeDto, PrototypeQueryDto } from '../../dto';
import { PrototypeType, PrototypeStatus } from '../../enums/prototype.enum';

describe('PrototypesController', () => {
  let controller: PrototypesController;
  let service: PrototypesService;

  const mockPrototypesService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
  };

  const mockRequest = {
    user: mockUser,
  };

  const mockPrototypeResponse = {
    id: 'prototype-123',
    name: 'Test Prototype',
    description: 'Test description',
    type: PrototypeType.HIGH_FIDELITY,
    status: PrototypeStatus.DRAFT,
    projectId: 'project-123',
    createdBy: 'user-123',
    figmaIntegrationStatus: 'not_connected',
    currentVersion: '1.0.0',
    versionCount: 1,
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PrototypesController],
      providers: [
        {
          provide: PrototypesService,
          useValue: mockPrototypesService,
        },
      ],
    }).compile();

    controller = module.get<PrototypesController>(PrototypesController);
    service = module.get<PrototypesService>(PrototypesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new prototype', async () => {
      const createDto: CreatePrototypeDto = {
        name: 'Test Prototype',
        description: 'Test description',
        type: PrototypeType.HIGH_FIDELITY,
        projectId: 'project-123',
      };

      mockPrototypesService.create.mockResolvedValue(mockPrototypeResponse);

      const result = await controller.create(createDto, mockRequest);

      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(result).toEqual(mockPrototypeResponse);
    });

    it('should handle creation with Figma integration', async () => {
      const createDto: CreatePrototypeDto = {
        name: 'Figma Prototype',
        projectId: 'project-123',
        figmaFileId: 'figma-123',
        figmaFileUrl: 'https://figma.com/file/123',
        figmaNodeIds: ['node1', 'node2'],
      };

      const figmaPrototypeResponse = {
        ...mockPrototypeResponse,
        figmaFileId: 'figma-123',
        figmaIntegrationStatus: 'connected',
      };

      mockPrototypesService.create.mockResolvedValue(figmaPrototypeResponse);

      const result = await controller.create(createDto, mockRequest);

      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(result).toEqual(figmaPrototypeResponse);
    });
  });

  describe('findAll', () => {
    it('should return paginated prototypes', async () => {
      const query: PrototypeQueryDto = {
        page: 1,
        limit: 20,
        projectId: 'project-123',
      };

      const paginatedResponse = {
        data: [mockPrototypeResponse],
        meta: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      };

      mockPrototypesService.findAll.mockResolvedValue(paginatedResponse);

      const result = await controller.findAll(query);

      expect(service.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual(paginatedResponse);
    });

    it('should handle search and filtering', async () => {
      const query: PrototypeQueryDto = {
        search: 'mobile',
        type: PrototypeType.HIGH_FIDELITY,
        status: PrototypeStatus.IN_REVIEW,
        tags: ['mobile', 'responsive'],
      };

      mockPrototypesService.findAll.mockResolvedValue({
        data: [],
        meta: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findAll(query);

      expect(service.findAll).toHaveBeenCalledWith(query);
    });
  });

  describe('findOne', () => {
    it('should return a single prototype', async () => {
      const prototypeId = 'prototype-123';

      mockPrototypesService.findOne.mockResolvedValue(mockPrototypeResponse);

      const result = await controller.findOne(prototypeId);

      expect(service.findOne).toHaveBeenCalledWith(prototypeId);
      expect(result).toEqual(mockPrototypeResponse);
    });
  });

  describe('update', () => {
    it('should update a prototype', async () => {
      const prototypeId = 'prototype-123';
      const updateDto: UpdatePrototypeDto = {
        name: 'Updated Prototype',
        status: PrototypeStatus.IN_REVIEW,
      };

      const updatedResponse = {
        ...mockPrototypeResponse,
        name: 'Updated Prototype',
        status: PrototypeStatus.IN_REVIEW,
      };

      mockPrototypesService.update.mockResolvedValue(updatedResponse);

      const result = await controller.update(prototypeId, updateDto, mockRequest);

      expect(service.update).toHaveBeenCalledWith(prototypeId, updateDto, mockUser.id);
      expect(result).toEqual(updatedResponse);
    });

    it('should handle metadata updates', async () => {
      const prototypeId = 'prototype-123';
      const updateDto: UpdatePrototypeDto = {
        tags: ['updated', 'tags'],
        customProperties: { priority: 'high' },
      };

      mockPrototypesService.update.mockResolvedValue(mockPrototypeResponse);

      await controller.update(prototypeId, updateDto, mockRequest);

      expect(service.update).toHaveBeenCalledWith(prototypeId, updateDto, mockUser.id);
    });
  });

  describe('remove', () => {
    it('should delete a prototype', async () => {
      const prototypeId = 'prototype-123';

      mockPrototypesService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(prototypeId, mockRequest);

      expect(service.remove).toHaveBeenCalledWith(prototypeId, mockUser.id);
      expect(result).toEqual({ message: 'Prototype deleted successfully' });
    });
  });

  describe('findByProject', () => {
    it('should return prototypes for a specific project', async () => {
      const projectId = 'project-123';
      const query: PrototypeQueryDto = {
        page: 1,
        limit: 20,
      };

      const expectedQuery = { ...query, projectId };

      mockPrototypesService.findAll.mockResolvedValue({
        data: [mockPrototypeResponse],
        meta: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      await controller.findByProject(projectId, query);

      expect(service.findAll).toHaveBeenCalledWith(expectedQuery);
    });
  });

  describe('getStatistics', () => {
    it('should return prototype statistics', async () => {
      const result = await controller.getStatistics();

      expect(result).toEqual({
        total: 0,
        byStatus: {},
        byType: {},
        withFigmaIntegration: 0,
        recentlyUpdated: 0,
      });
    });
  });
});
