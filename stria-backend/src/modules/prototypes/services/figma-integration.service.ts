import { Injectable, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { Prototype } from '../entities/prototype.entity';
import { 
  FigmaSyncDto, 
  FigmaSyncResponseDto, 
  FigmaConnectionTestDto,
  FigmaConnectionTestResponseDto 
} from '../dto/figma-sync.dto';
import { FigmaIntegrationStatus } from '../enums/prototype.enum';

/**
 * FigmaIntegrationService
 * 
 * Service for integrating with Figma API
 * Handles file synchronization, connection testing, and data extraction
 */
@Injectable()
export class FigmaIntegrationService {
  private readonly figmaApiUrl = 'https://api.figma.com/v1';
  private readonly figmaToken: string;

  constructor(
    @InjectRepository(Prototype)
    private readonly prototypeRepository: Repository<Prototype>,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.figmaToken = this.configService.get<string>('FIGMA_ACCESS_TOKEN');
  }

  /**
   * Sync prototype with Figma file
   */
  async syncWithFigma(
    prototypeId: string, 
    syncDto: FigmaSyncDto, 
    userId: string
  ): Promise<FigmaSyncResponseDto> {
    try {
      const prototype = await this.prototypeRepository.findOne({ 
        where: { id: prototypeId } 
      });

      if (!prototype) {
        throw new BadRequestException('Prototype not found');
      }

      // Update prototype with Figma information
      prototype.figmaFileId = syncDto.figmaFileId;
      prototype.figmaFileUrl = syncDto.figmaFileUrl;
      prototype.figmaEmbedUrl = syncDto.figmaEmbedUrl;
      prototype.figmaIntegrationStatus = FigmaIntegrationStatus.SYNCING;
      prototype.updatedBy = userId;

      await this.prototypeRepository.save(prototype);

      // Test connection first
      const connectionTest = await this.testConnection({ figmaFileId: syncDto.figmaFileId });
      
      if (!connectionTest.connected) {
        prototype.figmaIntegrationStatus = FigmaIntegrationStatus.SYNC_ERROR;
        await this.prototypeRepository.save(prototype);
        
        return {
          success: false,
          message: 'Failed to connect to Figma file',
          error: connectionTest.error,
        };
      }

      // Fetch file data from Figma
      const fileData = await this.fetchFigmaFile(syncDto.figmaFileId, syncDto.nodeIds);
      
      if (!fileData) {
        prototype.figmaIntegrationStatus = FigmaIntegrationStatus.SYNC_ERROR;
        await this.prototypeRepository.save(prototype);
        
        return {
          success: false,
          message: 'Failed to fetch data from Figma',
          error: 'Unable to retrieve file data',
        };
      }

      // Process and update metadata
      const syncDetails = await this.processFigmaData(prototype, fileData, syncDto.nodeIds);
      
      // Update prototype with sync results
      prototype.figmaIntegrationStatus = FigmaIntegrationStatus.SYNCED;
      prototype.figmaLastSync = new Date();
      
      // Update metadata with Figma data
      const updatedMetadata = {
        ...prototype.metadata,
        figmaNodeIds: syncDetails.syncedNodes?.map(node => node.id) || [],
        figmaFileInfo: {
          name: fileData.name,
          lastModified: fileData.lastModified,
          version: fileData.version,
        },
        syncHistory: [
          ...(prototype.metadata.syncHistory || []),
          {
            timestamp: new Date().toISOString(),
            changesCount: syncDetails.changesCount,
            syncedBy: userId,
          }
        ].slice(-10), // Keep last 10 sync records
      };
      
      prototype.metadata = updatedMetadata;
      await this.prototypeRepository.save(prototype);

      return {
        success: true,
        message: 'Successfully synced with Figma file',
        syncedAt: prototype.figmaLastSync,
        syncedNodes: syncDetails.syncedNodes,
        changesCount: syncDetails.changesCount,
        syncDetails: syncDetails.details,
      };

    } catch (error) {
      // Update prototype status on error
      const prototype = await this.prototypeRepository.findOne({ 
        where: { id: prototypeId } 
      });
      
      if (prototype) {
        prototype.figmaIntegrationStatus = FigmaIntegrationStatus.SYNC_ERROR;
        await this.prototypeRepository.save(prototype);
      }

      return {
        success: false,
        message: 'Sync operation failed',
        error: error.message || 'Unknown error occurred',
      };
    }
  }

  /**
   * Test connection to Figma file
   */
  async testConnection(testDto: FigmaConnectionTestDto): Promise<FigmaConnectionTestResponseDto> {
    try {
      if (!this.figmaToken) {
        return {
          connected: false,
          message: 'Figma API token not configured',
          error: 'Missing FIGMA_ACCESS_TOKEN configuration',
        };
      }

      const response = await firstValueFrom(
        this.httpService.get(`${this.figmaApiUrl}/files/${testDto.figmaFileId}`, {
          headers: {
            'X-Figma-Token': this.figmaToken,
          },
          timeout: 10000, // 10 second timeout
        })
      );

      if (response.status === 200 && response.data) {
        return {
          connected: true,
          message: 'Successfully connected to Figma file',
          fileInfo: {
            name: response.data.name,
            lastModified: response.data.lastModified,
            version: response.data.version,
          },
        };
      }

      return {
        connected: false,
        message: 'Failed to retrieve file information',
        error: 'Invalid response from Figma API',
      };

    } catch (error) {
      let errorMessage = 'Connection test failed';
      
      if (error.response?.status === 403) {
        errorMessage = 'Access denied - check file permissions and API token';
      } else if (error.response?.status === 404) {
        errorMessage = 'Figma file not found - check file ID';
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = 'Connection timeout - Figma API not responding';
      }

      return {
        connected: false,
        message: errorMessage,
        error: error.message,
      };
    }
  }

  /**
   * Fetch file data from Figma API
   */
  private async fetchFigmaFile(fileId: string, nodeIds?: string[]): Promise<any> {
    try {
      let url = `${this.figmaApiUrl}/files/${fileId}`;
      
      // Add node IDs if specified
      if (nodeIds && nodeIds.length > 0) {
        url += `?ids=${nodeIds.join(',')}`;
      }

      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: {
            'X-Figma-Token': this.figmaToken,
          },
          timeout: 30000, // 30 second timeout for file data
        })
      );

      return response.data;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch Figma file: ${error.message}`);
    }
  }

  /**
   * Process Figma data and extract relevant information
   */
  private async processFigmaData(
    prototype: Prototype, 
    figmaData: any, 
    requestedNodeIds?: string[]
  ): Promise<{
    syncedNodes: any[];
    changesCount: number;
    details: any;
  }> {
    const syncedNodes = [];
    const details = {
      addedNodes: [],
      updatedNodes: [],
      removedNodes: [],
      errors: [],
    };

    try {
      // Extract nodes from Figma data
      const nodes = this.extractNodesFromFigmaData(figmaData, requestedNodeIds);
      
      // Compare with existing metadata
      const existingNodeIds = prototype.metadata.figmaNodeIds || [];
      const newNodeIds = nodes.map(node => node.id);

      // Identify changes
      details.addedNodes = newNodeIds.filter(id => !existingNodeIds.includes(id));
      details.updatedNodes = newNodeIds.filter(id => existingNodeIds.includes(id));
      details.removedNodes = existingNodeIds.filter(id => !newNodeIds.includes(id));

      // Process each node
      for (const node of nodes) {
        try {
          const processedNode = {
            id: node.id,
            name: node.name,
            type: node.type,
            properties: this.extractNodeProperties(node),
          };
          
          syncedNodes.push(processedNode);
        } catch (nodeError) {
          details.errors.push(`Failed to process node ${node.id}: ${nodeError.message}`);
        }
      }

      const changesCount = details.addedNodes.length + details.updatedNodes.length + details.removedNodes.length;

      return {
        syncedNodes,
        changesCount,
        details,
      };

    } catch (error) {
      throw new InternalServerErrorException(`Failed to process Figma data: ${error.message}`);
    }
  }

  /**
   * Extract nodes from Figma data structure
   */
  private extractNodesFromFigmaData(figmaData: any, requestedNodeIds?: string[]): any[] {
    const nodes = [];
    
    if (figmaData.document && figmaData.document.children) {
      this.traverseNodes(figmaData.document.children, nodes, requestedNodeIds);
    }

    return nodes;
  }

  /**
   * Recursively traverse Figma nodes
   */
  private traverseNodes(children: any[], nodes: any[], requestedNodeIds?: string[]): void {
    for (const child of children) {
      // Include node if no specific IDs requested or if this node is requested
      if (!requestedNodeIds || requestedNodeIds.includes(child.id)) {
        nodes.push(child);
      }

      // Recursively traverse children
      if (child.children && child.children.length > 0) {
        this.traverseNodes(child.children, nodes, requestedNodeIds);
      }
    }
  }

  /**
   * Extract relevant properties from a Figma node
   */
  private extractNodeProperties(node: any): Record<string, any> {
    const properties: Record<string, any> = {};

    // Basic properties
    if (node.absoluteBoundingBox) {
      properties.boundingBox = node.absoluteBoundingBox;
    }

    if (node.fills) {
      properties.fills = node.fills;
    }

    if (node.strokes) {
      properties.strokes = node.strokes;
    }

    if (node.effects) {
      properties.effects = node.effects;
    }

    // Text properties
    if (node.characters) {
      properties.text = node.characters;
    }

    if (node.style) {
      properties.textStyle = node.style;
    }

    // Component properties
    if (node.componentId) {
      properties.componentId = node.componentId;
    }

    return properties;
  }
}
