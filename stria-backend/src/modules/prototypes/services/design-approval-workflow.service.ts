import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DesignApprovalWorkflow } from '../entities/design-approval-workflow.entity';
import { ApprovalStep } from '../entities/approval-step.entity';
import { ApprovalAction } from '../entities/approval-action.entity';
import { 
  CreateDesignApprovalWorkflowDto, 
  UpdateDesignApprovalWorkflowDto, 
  DesignApprovalWorkflowQueryDto,
  CreateApprovalActionDto,
  PaginatedResponseDto,
  DesignApprovalWorkflowResponseDto
} from '../dto';
import { WorkflowStatus } from '../enums/design-approval-workflow.enum';
import { StepStatus, StepType } from '../enums/approval-step.enum';
import { ApprovalDecision } from '../enums/approval-action.enum';
import { plainToClass } from 'class-transformer';

/**
 * DesignApprovalWorkflowService
 * 
 * Service for managing design approval workflows
 * Handles workflow creation, step management, and approval processing
 */
@Injectable()
export class DesignApprovalWorkflowService {
  constructor(
    @InjectRepository(DesignApprovalWorkflow)
    private readonly workflowRepository: Repository<DesignApprovalWorkflow>,
    @InjectRepository(ApprovalStep)
    private readonly stepRepository: Repository<ApprovalStep>,
    @InjectRepository(ApprovalAction)
    private readonly actionRepository: Repository<ApprovalAction>,
  ) {}

  /**
   * Create a new design approval workflow
   */
  async create(
    createWorkflowDto: CreateDesignApprovalWorkflowDto, 
    userId: string
  ): Promise<DesignApprovalWorkflowResponseDto> {
    // Build metadata object
    const metadata: any = {};
    
    if (createWorkflowDto.enableEmailNotifications !== undefined) {
      metadata.enableEmailNotifications = createWorkflowDto.enableEmailNotifications;
    }
    
    if (createWorkflowDto.enableSlackNotifications !== undefined) {
      metadata.enableSlackNotifications = createWorkflowDto.enableSlackNotifications;
    }
    
    if (createWorkflowDto.webhookUrl) {
      metadata.webhookUrl = createWorkflowDto.webhookUrl;
    }
    
    if (createWorkflowDto.template) {
      metadata.template = createWorkflowDto.template;
    }
    
    if (createWorkflowDto.customProperties) {
      metadata.customProperties = createWorkflowDto.customProperties;
    }

    // Create workflow entity
    const workflow = this.workflowRepository.create({
      name: createWorkflowDto.name,
      description: createWorkflowDto.description,
      type: createWorkflowDto.type,
      status: createWorkflowDto.status || WorkflowStatus.DRAFT,
      approvalStrategy: createWorkflowDto.approvalStrategy,
      requiredApprovals: createWorkflowDto.requiredApprovals || 1,
      allowParallelApproval: createWorkflowDto.allowParallelApproval ?? true,
      autoApproveOnThreshold: createWorkflowDto.autoApproveOnThreshold ?? false,
      approvalThresholdPercentage: createWorkflowDto.approvalThresholdPercentage || 100.0,
      dueDate: createWorkflowDto.dueDate ? new Date(createWorkflowDto.dueDate) : null,
      reminderIntervalHours: createWorkflowDto.reminderIntervalHours || 24,
      escalationHours: createWorkflowDto.escalationHours,
      totalSteps: createWorkflowDto.steps.length,
      prototypeId: createWorkflowDto.prototypeId,
      createdBy: userId,
      assignedTo: createWorkflowDto.assignedTo,
      metadata,
    } as any);

    const savedWorkflow = await this.workflowRepository.save(workflow) as any;

    // Create approval steps
    const steps = [];
    for (let i = 0; i < createWorkflowDto.steps.length; i++) {
      const stepConfig = createWorkflowDto.steps[i];

      const step = this.stepRepository.create({
        stepNumber: i + 1,
        stepName: stepConfig.stepName,
        description: stepConfig.description,
        type: StepType.REVIEW, // Default type
        status: i === 0 ? StepStatus.PENDING : StepStatus.PENDING,
        isRequired: stepConfig.isRequired ?? true,
        allowSkip: stepConfig.allowSkip ?? false,
        requiredApprovers: stepConfig.requiredApprovers,
        dueDate: stepConfig.dueDate ? new Date(stepConfig.dueDate) : null,
        estimatedHours: stepConfig.estimatedHours,
        dependsOnSteps: (stepConfig.dependsOnSteps || []).map(String),
        workflowId: savedWorkflow.id,
        createdBy: userId,
      } as any);

      steps.push(await this.stepRepository.save(step));
    }

    // Load with relations for response
    const workflowWithRelations = await this.findOneWithRelations(savedWorkflow.id);
    
    return this.toResponseDto(workflowWithRelations);
  }

  /**
   * Find all workflows with filtering and pagination
   */
  async findAll(query: DesignApprovalWorkflowQueryDto): Promise<PaginatedResponseDto<DesignApprovalWorkflowResponseDto>> {
    const {
      page = 1,
      limit = 20,
      prototypeId,
      createdBy,
      assignedTo,
      completedBy,
      type,
      status,
      approvalStrategy,
      priority,
      search,
      template,
      minCompletionPercentage,
      maxCompletionPercentage,
      currentStep,
      createdAfter,
      createdBefore,
      dueAfter,
      dueBefore,
      completedAfter,
      completedBefore,
      overdueOnly,
      activeOnly,
      completedOnly,
      pendingMyApproval,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      includePrototype = true,
      includeCreator = true,
      includeAssignee = true,
      includeSteps = false,
      includeActions = false,
      includeDeleted = false,
    } = query;

    const queryBuilder = this.workflowRepository.createQueryBuilder('workflow');

    // Include relations based on query parameters
    if (includePrototype) {
      queryBuilder.leftJoinAndSelect('workflow.prototype', 'prototype');
    }
    
    if (includeCreator) {
      queryBuilder.leftJoinAndSelect('workflow.creator', 'creator');
    }
    
    if (includeAssignee) {
      queryBuilder.leftJoinAndSelect('workflow.assignee', 'assignee');
      queryBuilder.leftJoinAndSelect('workflow.completedBy', 'completedBy');
      queryBuilder.leftJoinAndSelect('workflow.escalatedTo', 'escalatedTo');
    }

    if (includeSteps) {
      queryBuilder.leftJoinAndSelect('workflow.steps', 'steps');
      
      if (includeActions) {
        queryBuilder.leftJoinAndSelect('steps.actions', 'actions');
        queryBuilder.leftJoinAndSelect('actions.user', 'actionUser');
      }
    }

    // Apply filters
    if (prototypeId) {
      queryBuilder.andWhere('workflow.prototypeId = :prototypeId', { prototypeId });
    }

    if (createdBy) {
      queryBuilder.andWhere('workflow.createdBy = :createdBy', { createdBy });
    }

    if (assignedTo) {
      queryBuilder.andWhere('workflow.assignedTo = :assignedTo', { assignedTo });
    }

    if (completedBy) {
      queryBuilder.andWhere('workflow.completedBy = :completedBy', { completedBy });
    }

    if (type) {
      queryBuilder.andWhere('workflow.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('workflow.status = :status', { status });
    }

    if (approvalStrategy) {
      queryBuilder.andWhere('workflow.approvalStrategy = :approvalStrategy', { approvalStrategy });
    }

    if (priority) {
      queryBuilder.andWhere('workflow.priority = :priority', { priority });
    }

    if (search) {
      queryBuilder.andWhere(
        '(workflow.name ILIKE :search OR workflow.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (template) {
      queryBuilder.andWhere(
        'workflow.metadata->>\'template\' = :template',
        { template }
      );
    }

    // Progress-based filtering
    if (minCompletionPercentage !== undefined) {
      queryBuilder.andWhere('workflow.approvalPercentage >= :minCompletion', { 
        minCompletion: minCompletionPercentage 
      });
    }

    if (maxCompletionPercentage !== undefined) {
      queryBuilder.andWhere('workflow.approvalPercentage <= :maxCompletion', { 
        maxCompletion: maxCompletionPercentage 
      });
    }

    if (currentStep) {
      queryBuilder.andWhere('workflow.currentStep = :currentStep', { currentStep });
    }

    // Date filters
    if (createdAfter) {
      queryBuilder.andWhere('workflow.createdAt >= :createdAfter', { 
        createdAfter: new Date(createdAfter) 
      });
    }

    if (createdBefore) {
      queryBuilder.andWhere('workflow.createdAt <= :createdBefore', { 
        createdBefore: new Date(createdBefore) 
      });
    }

    if (dueAfter) {
      queryBuilder.andWhere('workflow.dueDate >= :dueAfter', { 
        dueAfter: new Date(dueAfter) 
      });
    }

    if (dueBefore) {
      queryBuilder.andWhere('workflow.dueDate <= :dueBefore', { 
        dueBefore: new Date(dueBefore) 
      });
    }

    if (completedAfter) {
      queryBuilder.andWhere('workflow.completedAt >= :completedAfter', { 
        completedAfter: new Date(completedAfter) 
      });
    }

    if (completedBefore) {
      queryBuilder.andWhere('workflow.completedAt <= :completedBefore', { 
        completedBefore: new Date(completedBefore) 
      });
    }

    // Status-based filters
    if (overdueOnly) {
      queryBuilder.andWhere('workflow.dueDate < :now AND workflow.status != :completed', { 
        now: new Date(),
        completed: WorkflowStatus.COMPLETED
      });
    }

    if (activeOnly) {
      queryBuilder.andWhere('workflow.status IN (:...activeStatuses)', { 
        activeStatuses: [WorkflowStatus.ACTIVE, WorkflowStatus.IN_PROGRESS, WorkflowStatus.PENDING]
      });
    }

    if (completedOnly) {
      queryBuilder.andWhere('workflow.status = :completed', { 
        completed: WorkflowStatus.COMPLETED 
      });
    }

    // Soft delete filter
    if (!includeDeleted) {
      queryBuilder.andWhere('workflow.deletedAt IS NULL');
    }

    // Sorting
    const validSortFields = ['name', 'createdAt', 'updatedAt', 'dueDate', 'priority', 'status', 'approvalPercentage'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    queryBuilder.orderBy(`workflow.${sortField}`, sortOrder);

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [workflows, total] = await queryBuilder.getManyAndCount();

    // Convert to response DTOs
    const responseData = workflows.map(workflow => this.toResponseDto(workflow));

    return PaginatedResponseDto.create(responseData, total, page, limit);
  }

  /**
   * Find one workflow by ID
   */
  async findOne(id: string): Promise<DesignApprovalWorkflowResponseDto> {
    const workflow = await this.findOneWithRelations(id);
    
    if (!workflow) {
      throw new NotFoundException(`Design approval workflow with ID ${id} not found`);
    }

    return this.toResponseDto(workflow);
  }

  /**
   * Update a workflow
   */
  async update(
    id: string, 
    updateWorkflowDto: UpdateDesignApprovalWorkflowDto, 
    userId: string
  ): Promise<DesignApprovalWorkflowResponseDto> {
    const workflow = await this.workflowRepository.findOne({ 
      where: { id },
      relations: ['prototype', 'creator']
    });

    if (!workflow) {
      throw new NotFoundException(`Design approval workflow with ID ${id} not found`);
    }

    // Update metadata if provided
    if (updateWorkflowDto.enableEmailNotifications !== undefined ||
        updateWorkflowDto.enableSlackNotifications !== undefined ||
        updateWorkflowDto.webhookUrl !== undefined ||
        updateWorkflowDto.template !== undefined ||
        updateWorkflowDto.customProperties !== undefined) {
      
      const updatedMetadata = { ...workflow.metadata };
      
      if (updateWorkflowDto.enableEmailNotifications !== undefined) {
        updatedMetadata.enableEmailNotifications = updateWorkflowDto.enableEmailNotifications;
      }
      
      if (updateWorkflowDto.enableSlackNotifications !== undefined) {
        updatedMetadata.enableSlackNotifications = updateWorkflowDto.enableSlackNotifications;
      }
      
      if (updateWorkflowDto.webhookUrl !== undefined) {
        updatedMetadata.webhookUrl = updateWorkflowDto.webhookUrl;
      }
      
      if (updateWorkflowDto.template !== undefined) {
        updatedMetadata.template = updateWorkflowDto.template;
      }
      
      if (updateWorkflowDto.customProperties !== undefined) {
        updatedMetadata.customProperties = updateWorkflowDto.customProperties;
      }

      workflow.metadata = updatedMetadata;
    }

    // Update other fields
    Object.assign(workflow, {
      ...updateWorkflowDto,
      dueDate: updateWorkflowDto.dueDate ? new Date(updateWorkflowDto.dueDate) : workflow.dueDate,
    });

    const savedWorkflow = await this.workflowRepository.save(workflow);
    const workflowWithRelations = await this.findOneWithRelations(savedWorkflow.id);
    
    return this.toResponseDto(workflowWithRelations);
  }

  /**
   * Soft delete a workflow
   */
  async remove(id: string, userId: string): Promise<void> {
    const workflow = await this.workflowRepository.findOne({ where: { id } });

    if (!workflow) {
      throw new NotFoundException(`Design approval workflow with ID ${id} not found`);
    }

    workflow.deletedAt = new Date();
    workflow.deletedBy = userId;

    await this.workflowRepository.save(workflow);
  }

  /**
   * Start a workflow
   */
  async startWorkflow(id: string, userId: string): Promise<DesignApprovalWorkflowResponseDto> {
    const workflow = await this.workflowRepository.findOne({ 
      where: { id },
      relations: ['steps']
    });

    if (!workflow) {
      throw new NotFoundException(`Design approval workflow with ID ${id} not found`);
    }

    if (workflow.status !== WorkflowStatus.DRAFT) {
      throw new BadRequestException('Workflow can only be started from draft status');
    }

    // Update workflow status
    workflow.status = WorkflowStatus.ACTIVE;
    workflow.startedAt = new Date();

    // Update first step status
    const steps = await workflow.steps;
    if (steps && steps.length > 0) {
      const firstStep = steps.find(step => step.stepNumber === 1);
      if (firstStep) {
        firstStep.status = StepStatus.IN_PROGRESS;
        await this.stepRepository.save(firstStep);
      }
    }

    await this.workflowRepository.save(workflow);

    const workflowWithRelations = await this.findOneWithRelations(id);
    return this.toResponseDto(workflowWithRelations);
  }

  /**
   * Find one workflow with all relations
   */
  private async findOneWithRelations(id: string): Promise<DesignApprovalWorkflow | null> {
    return this.workflowRepository.findOne({
      where: { id },
      relations: [
        'prototype', 
        'creator', 
        'assignee', 
        'completedBy', 
        'escalatedTo',
        'steps',
        'steps.actions',
        'steps.actions.user',
        'steps.assignee',
        'steps.completedBy'
      ],
    });
  }

  /**
   * Convert entity to response DTO
   */
  private toResponseDto(workflow: DesignApprovalWorkflow): DesignApprovalWorkflowResponseDto {
    return plainToClass(DesignApprovalWorkflowResponseDto, workflow, {
      excludeExtraneousValues: true,
    });
  }
}
