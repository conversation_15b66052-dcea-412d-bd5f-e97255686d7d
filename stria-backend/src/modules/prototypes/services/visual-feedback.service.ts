import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { VisualFeedback } from '../entities/visual-feedback.entity';
import { 
  CreateVisualFeedbackDto, 
  UpdateVisualFeedbackDto, 
  VisualFeedbackQueryDto,
  PaginatedResponseDto,
  VisualFeedbackResponseDto
} from '../dto';
import { FeedbackStatus } from '../enums/visual-feedback.enum';
import { plainToClass } from 'class-transformer';

/**
 * VisualFeedbackService
 * 
 * Service for managing visual feedback annotations on prototypes
 * Handles coordinate-based feedback, status management, and assignments
 */
@Injectable()
export class VisualFeedbackService {
  constructor(
    @InjectRepository(VisualFeedback)
    private readonly visualFeedbackRepository: Repository<VisualFeedback>,
  ) {}

  /**
   * Create a new visual feedback
   */
  async create(
    createVisualFeedbackDto: CreateVisualFeedbackDto, 
    userId: string
  ): Promise<VisualFeedbackResponseDto> {
    // Build metadata object
    const metadata: any = {};
    
    if (createVisualFeedbackDto.screenshot) {
      metadata.screenshot = createVisualFeedbackDto.screenshot;
    }
    
    if (createVisualFeedbackDto.attachments) {
      metadata.attachments = createVisualFeedbackDto.attachments;
    }
    
    if (createVisualFeedbackDto.tags) {
      metadata.tags = createVisualFeedbackDto.tags;
    }
    
    if (createVisualFeedbackDto.category) {
      metadata.category = createVisualFeedbackDto.category;
    }
    
    if (createVisualFeedbackDto.severity) {
      metadata.severity = createVisualFeedbackDto.severity;
    }
    
    if (createVisualFeedbackDto.estimatedEffort) {
      metadata.estimatedEffort = createVisualFeedbackDto.estimatedEffort;
    }
    
    if (createVisualFeedbackDto.relatedFeedbacks) {
      metadata.relatedFeedbacks = createVisualFeedbackDto.relatedFeedbacks;
    }
    
    if (createVisualFeedbackDto.customFields) {
      metadata.customFields = createVisualFeedbackDto.customFields;
    }

    // Create visual feedback entity
    const visualFeedback = this.visualFeedbackRepository.create({
      title: createVisualFeedbackDto.title,
      content: createVisualFeedbackDto.content,
      type: createVisualFeedbackDto.type,
      status: createVisualFeedbackDto.status,
      priority: createVisualFeedbackDto.priority,
      annotationType: createVisualFeedbackDto.annotationType,
      positionX: createVisualFeedbackDto.positionX,
      positionY: createVisualFeedbackDto.positionY,
      positionZ: createVisualFeedbackDto.positionZ || 0,
      width: createVisualFeedbackDto.width,
      height: createVisualFeedbackDto.height,
      color: createVisualFeedbackDto.color || '#FF6B6B',
      borderColor: createVisualFeedbackDto.borderColor,
      backgroundColor: createVisualFeedbackDto.backgroundColor,
      opacity: createVisualFeedbackDto.opacity || 1.0,
      targetElementId: createVisualFeedbackDto.targetElementId,
      targetElementType: createVisualFeedbackDto.targetElementType,
      targetElementName: createVisualFeedbackDto.targetElementName,
      prototypeId: createVisualFeedbackDto.prototypeId,
      versionId: createVisualFeedbackDto.versionId,
      createdBy: userId,
      assignedTo: createVisualFeedbackDto.assignedTo,
      metadata,
    });

    const savedFeedback = await this.visualFeedbackRepository.save(visualFeedback);
    
    // Load with relations for response
    const feedbackWithRelations = await this.findOneWithRelations(savedFeedback.id);
    
    return this.toResponseDto(feedbackWithRelations);
  }

  /**
   * Find all visual feedback with filtering and pagination
   */
  async findAll(query: VisualFeedbackQueryDto): Promise<PaginatedResponseDto<VisualFeedbackResponseDto>> {
    const {
      page = 1,
      limit = 20,
      prototypeId,
      versionId,
      createdBy,
      assignedTo,
      resolvedBy,
      type,
      status,
      priority,
      annotationType,
      search,
      tags,
      category,
      severity,
      targetElementId,
      minX,
      maxX,
      minY,
      maxY,
      createdAfter,
      createdBefore,
      resolvedAfter,
      resolvedBefore,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      includePrototype = true,
      includeVersion = true,
      includeCreator = true,
      includeAssignee = true,
      includeDeleted = false,
      unresolvedOnly = false,
    } = query;

    const queryBuilder = this.visualFeedbackRepository.createQueryBuilder('feedback');

    // Include relations based on query parameters
    if (includePrototype) {
      queryBuilder.leftJoinAndSelect('feedback.prototype', 'prototype');
    }
    
    if (includeVersion) {
      queryBuilder.leftJoinAndSelect('feedback.version', 'version');
    }
    
    if (includeCreator) {
      queryBuilder.leftJoinAndSelect('feedback.creator', 'creator');
    }
    
    if (includeAssignee) {
      queryBuilder.leftJoinAndSelect('feedback.assignee', 'assignee');
      queryBuilder.leftJoinAndSelect('feedback.resolver', 'resolver');
    }

    // Apply filters
    if (prototypeId) {
      queryBuilder.andWhere('feedback.prototypeId = :prototypeId', { prototypeId });
    }

    if (versionId) {
      queryBuilder.andWhere('feedback.versionId = :versionId', { versionId });
    }

    if (createdBy) {
      queryBuilder.andWhere('feedback.createdBy = :createdBy', { createdBy });
    }

    if (assignedTo) {
      queryBuilder.andWhere('feedback.assignedTo = :assignedTo', { assignedTo });
    }

    if (resolvedBy) {
      queryBuilder.andWhere('feedback.resolvedBy = :resolvedBy', { resolvedBy });
    }

    if (type) {
      queryBuilder.andWhere('feedback.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('feedback.status = :status', { status });
    }

    if (priority) {
      queryBuilder.andWhere('feedback.priority = :priority', { priority });
    }

    if (annotationType) {
      queryBuilder.andWhere('feedback.annotationType = :annotationType', { annotationType });
    }

    if (search) {
      queryBuilder.andWhere(
        '(feedback.title ILIKE :search OR feedback.content ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere(
        'feedback.metadata->>\'tags\' ?| array[:tags]',
        { tags }
      );
    }

    if (category) {
      queryBuilder.andWhere(
        'feedback.metadata->>\'category\' = :category',
        { category }
      );
    }

    if (severity) {
      queryBuilder.andWhere(
        'feedback.metadata->>\'severity\' = :severity',
        { severity }
      );
    }

    if (targetElementId) {
      queryBuilder.andWhere('feedback.targetElementId = :targetElementId', { targetElementId });
    }

    // Position-based filtering
    if (minX !== undefined) {
      queryBuilder.andWhere('feedback.positionX >= :minX', { minX });
    }

    if (maxX !== undefined) {
      queryBuilder.andWhere('feedback.positionX <= :maxX', { maxX });
    }

    if (minY !== undefined) {
      queryBuilder.andWhere('feedback.positionY >= :minY', { minY });
    }

    if (maxY !== undefined) {
      queryBuilder.andWhere('feedback.positionY <= :maxY', { maxY });
    }

    // Date filters
    if (createdAfter) {
      queryBuilder.andWhere('feedback.createdAt >= :createdAfter', { 
        createdAfter: new Date(createdAfter) 
      });
    }

    if (createdBefore) {
      queryBuilder.andWhere('feedback.createdAt <= :createdBefore', { 
        createdBefore: new Date(createdBefore) 
      });
    }

    if (resolvedAfter) {
      queryBuilder.andWhere('feedback.resolvedAt >= :resolvedAfter', { 
        resolvedAfter: new Date(resolvedAfter) 
      });
    }

    if (resolvedBefore) {
      queryBuilder.andWhere('feedback.resolvedAt <= :resolvedBefore', { 
        resolvedBefore: new Date(resolvedBefore) 
      });
    }

    // Unresolved only filter
    if (unresolvedOnly) {
      queryBuilder.andWhere('feedback.resolvedAt IS NULL');
    }

    // Soft delete filter
    if (!includeDeleted) {
      queryBuilder.andWhere('feedback.deletedAt IS NULL');
    }

    // Sorting
    const validSortFields = ['createdAt', 'updatedAt', 'priority', 'status', 'type', 'positionX', 'positionY'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    queryBuilder.orderBy(`feedback.${sortField}`, sortOrder);

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [feedbacks, total] = await queryBuilder.getManyAndCount();

    // Convert to response DTOs
    const responseData = feedbacks.map(feedback => this.toResponseDto(feedback));

    return PaginatedResponseDto.create(responseData, total, page, limit);
  }

  /**
   * Find one visual feedback by ID
   */
  async findOne(id: string): Promise<VisualFeedbackResponseDto> {
    const feedback = await this.findOneWithRelations(id);
    
    if (!feedback) {
      throw new NotFoundException(`Visual feedback with ID ${id} not found`);
    }

    return this.toResponseDto(feedback);
  }

  /**
   * Update a visual feedback
   */
  async update(
    id: string, 
    updateVisualFeedbackDto: UpdateVisualFeedbackDto, 
    userId: string
  ): Promise<VisualFeedbackResponseDto> {
    const feedback = await this.visualFeedbackRepository.findOne({ 
      where: { id },
      relations: ['prototype', 'creator']
    });

    if (!feedback) {
      throw new NotFoundException(`Visual feedback with ID ${id} not found`);
    }

    // Update metadata if provided
    if (updateVisualFeedbackDto.screenshot !== undefined ||
        updateVisualFeedbackDto.attachments !== undefined ||
        updateVisualFeedbackDto.tags !== undefined ||
        updateVisualFeedbackDto.category !== undefined ||
        updateVisualFeedbackDto.severity !== undefined ||
        updateVisualFeedbackDto.estimatedEffort !== undefined ||
        updateVisualFeedbackDto.relatedFeedbacks !== undefined ||
        updateVisualFeedbackDto.customFields !== undefined) {
      
      const updatedMetadata = { ...feedback.metadata };
      
      if (updateVisualFeedbackDto.screenshot !== undefined) {
        updatedMetadata.screenshot = updateVisualFeedbackDto.screenshot;
      }
      
      if (updateVisualFeedbackDto.attachments !== undefined) {
        updatedMetadata.attachments = updateVisualFeedbackDto.attachments;
      }
      
      if (updateVisualFeedbackDto.tags !== undefined) {
        updatedMetadata.tags = updateVisualFeedbackDto.tags;
      }
      
      if (updateVisualFeedbackDto.category !== undefined) {
        updatedMetadata.category = updateVisualFeedbackDto.category;
      }
      
      if (updateVisualFeedbackDto.severity !== undefined) {
        updatedMetadata.severity = updateVisualFeedbackDto.severity;
      }
      
      if (updateVisualFeedbackDto.estimatedEffort !== undefined) {
        updatedMetadata.estimatedEffort = updateVisualFeedbackDto.estimatedEffort;
      }
      
      if (updateVisualFeedbackDto.relatedFeedbacks !== undefined) {
        updatedMetadata.relatedFeedbacks = updateVisualFeedbackDto.relatedFeedbacks;
      }
      
      if (updateVisualFeedbackDto.customFields !== undefined) {
        updatedMetadata.customFields = updateVisualFeedbackDto.customFields;
      }

      feedback.metadata = updatedMetadata;
    }

    // Update other fields
    Object.assign(feedback, updateVisualFeedbackDto);

    // Handle status changes
    if (updateVisualFeedbackDto.status === FeedbackStatus.RESOLVED && !feedback.resolvedAt) {
      feedback.resolvedAt = new Date();
      feedback.resolvedBy = userId;
    } else if (updateVisualFeedbackDto.status !== FeedbackStatus.RESOLVED && feedback.resolvedAt) {
      feedback.resolvedAt = null;
      feedback.resolvedBy = null;
    }

    const savedFeedback = await this.visualFeedbackRepository.save(feedback);
    const feedbackWithRelations = await this.findOneWithRelations(savedFeedback.id);
    
    return this.toResponseDto(feedbackWithRelations);
  }

  /**
   * Soft delete a visual feedback
   */
  async remove(id: string, userId: string): Promise<void> {
    const feedback = await this.visualFeedbackRepository.findOne({ where: { id } });

    if (!feedback) {
      throw new NotFoundException(`Visual feedback with ID ${id} not found`);
    }

    feedback.deletedAt = new Date();
    feedback.deletedBy = userId;

    await this.visualFeedbackRepository.save(feedback);
  }

  /**
   * Find one visual feedback with all relations
   */
  private async findOneWithRelations(id: string): Promise<VisualFeedback | null> {
    return this.visualFeedbackRepository.findOne({
      where: { id },
      relations: ['prototype', 'version', 'creator', 'assignee', 'resolver'],
    });
  }

  /**
   * Convert entity to response DTO
   */
  private toResponseDto(feedback: VisualFeedback): VisualFeedbackResponseDto {
    return plainToClass(VisualFeedbackResponseDto, feedback, {
      excludeExtraneousValues: true,
    });
  }
}
