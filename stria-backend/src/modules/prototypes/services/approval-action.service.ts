import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApprovalAction } from '../entities/approval-action.entity';
import { ApprovalStep } from '../entities/approval-step.entity';
import { DesignApprovalWorkflow } from '../entities/design-approval-workflow.entity';
import { CreateApprovalActionDto } from '../dto';
import { ApprovalDecision, ActionType } from '../enums/approval-action.enum';
import { StepStatus } from '../enums/approval-step.enum';
import { WorkflowStatus } from '../enums/design-approval-workflow.enum';

/**
 * ApprovalActionService
 * 
 * Service for managing approval actions within workflows
 * Handles action creation, workflow progression, and decision processing
 */
@Injectable()
export class ApprovalActionService {
  constructor(
    @InjectRepository(ApprovalAction)
    private readonly actionRepository: Repository<ApprovalAction>,
    @InjectRepository(ApprovalStep)
    private readonly stepRepository: Repository<ApprovalStep>,
    @InjectRepository(DesignApprovalWorkflow)
    private readonly workflowRepository: Repository<DesignApprovalWorkflow>,
  ) {}

  /**
   * Create a new approval action
   */
  async createAction(
    createActionDto: CreateApprovalActionDto, 
    userId: string
  ): Promise<ApprovalAction> {
    // Find the step
    const step = await this.stepRepository.findOne({
      where: { id: createActionDto.stepId },
      relations: ['workflow', 'actions']
    });

    if (!step) {
      throw new NotFoundException(`Approval step with ID ${createActionDto.stepId} not found`);
    }

    // Validate step is in progress
    if (step.status !== StepStatus.IN_PROGRESS && step.status !== StepStatus.PENDING) {
      throw new BadRequestException('Cannot add action to step that is not in progress or pending');
    }

    // Build metadata object
    const metadata: any = {};
    
    if (createActionDto.attachments) {
      metadata.attachments = createActionDto.attachments;
    }
    
    if (createActionDto.referencedFeedback) {
      metadata.referencedFeedback = createActionDto.referencedFeedback;
    }
    
    if (createActionDto.tags) {
      metadata.tags = createActionDto.tags;
    }
    
    if (createActionDto.qualityScore !== undefined) {
      metadata.qualityScore = createActionDto.qualityScore;
    }
    
    if (createActionDto.usabilityScore !== undefined) {
      metadata.usabilityScore = createActionDto.usabilityScore;
    }
    
    if (createActionDto.brandComplianceScore !== undefined) {
      metadata.brandComplianceScore = createActionDto.brandComplianceScore;
    }
    
    if (createActionDto.customProperties) {
      metadata.customProperties = createActionDto.customProperties;
    }

    // Create action entity
    const action = this.actionRepository.create({
      type: createActionDto.type,
      decision: createActionDto.decision,
      comment: createActionDto.comment,
      reason: createActionDto.reason,
      isConditional: createActionDto.isConditional || false,
      conditions: createActionDto.conditions || [],
      stepId: createActionDto.stepId,
      userId: userId,
      delegatedBy: createActionDto.delegatedBy,
      delegationReason: createActionDto.delegationReason,
      metadata,
    } as any);

    const savedAction = await this.actionRepository.save(action);

    // Update step counts and status
    await this.updateStepAfterAction(step, createActionDto.decision);

    // Check if workflow should progress
    await this.checkWorkflowProgression(step.workflow);

    return savedAction as unknown as ApprovalAction;
  }

  /**
   * Update step status and counts after an action
   */
  private async updateStepAfterAction(step: ApprovalStep, decision: ApprovalDecision): Promise<void> {
    // Update approval/rejection counts
    if (decision === ApprovalDecision.APPROVED) {
      step.approvalCount += 1;
    } else if (decision === ApprovalDecision.REJECTED) {
      step.rejectionCount += 1;
    }

    // Check if step is complete
    const totalActions = step.approvalCount + step.rejectionCount;
    
    if (step.approvalCount >= step.requiredApprovers) {
      // Step approved
      step.status = StepStatus.COMPLETED;
      (step as any).finalDecision = ApprovalDecision.APPROVED;
      step.completedAt = new Date();
    } else if (step.rejectionCount > 0 && !step.workflow.allowParallelApproval) {
      // Step rejected (in sequential mode, one rejection fails the step)
      step.status = StepStatus.COMPLETED;
      (step as any).finalDecision = ApprovalDecision.REJECTED;
      step.completedAt = new Date();
    } else if (totalActions >= step.requiredApprovers) {
      // All required approvers have responded, determine final decision
      if (step.approvalCount > step.rejectionCount) {
        step.status = StepStatus.COMPLETED;
        (step as any).finalDecision = ApprovalDecision.APPROVED;
        step.completedAt = new Date();
      } else {
        step.status = StepStatus.COMPLETED;
        (step as any).finalDecision = ApprovalDecision.REJECTED;
        step.completedAt = new Date();
      }
    }

    await this.stepRepository.save(step);
  }

  /**
   * Check if workflow should progress to next step or complete
   */
  private async checkWorkflowProgression(workflow: DesignApprovalWorkflow): Promise<void> {
    // Reload workflow with all steps
    const fullWorkflow = await this.workflowRepository.findOne({
      where: { id: workflow.id },
      relations: ['steps']
    });

    if (!fullWorkflow) return;

    const steps = await fullWorkflow.steps;
    const currentStepEntity = steps.find(s => s.stepNumber === fullWorkflow.currentStep);
    
    if (!currentStepEntity || currentStepEntity.status !== StepStatus.COMPLETED) {
      return; // Current step not completed yet
    }

    // If current step was rejected, fail the workflow
    if ((currentStepEntity as any).finalDecision === ApprovalDecision.REJECTED) {
      fullWorkflow.status = WorkflowStatus.REJECTED;
      fullWorkflow.completedAt = new Date();
      await this.workflowRepository.save(fullWorkflow);
      return;
    }

    // Move to next step or complete workflow
    const nextStepNumber = fullWorkflow.currentStep + 1;
    const nextStep = steps.find(s => s.stepNumber === nextStepNumber);

    if (nextStep) {
      // Move to next step
      fullWorkflow.currentStep = nextStepNumber;
      nextStep.status = StepStatus.IN_PROGRESS;
      
      await this.stepRepository.save(nextStep);
      await this.workflowRepository.save(fullWorkflow);
    } else {
      // Workflow completed
      fullWorkflow.status = WorkflowStatus.COMPLETED;
      fullWorkflow.completedAt = new Date();
      fullWorkflow.completedSteps = fullWorkflow.totalSteps;
      fullWorkflow.approvalPercentage = 100.0;
      
      await this.workflowRepository.save(fullWorkflow);
    }
  }

  /**
   * Delegate approval to another user
   */
  async delegateApproval(
    stepId: string, 
    delegateToUserId: string, 
    delegationReason: string,
    currentUserId: string
  ): Promise<void> {
    const step = await this.stepRepository.findOne({
      where: { id: stepId }
    });

    if (!step) {
      throw new NotFoundException(`Approval step with ID ${stepId} not found`);
    }

    if (step.status !== StepStatus.IN_PROGRESS && step.status !== StepStatus.PENDING) {
      throw new BadRequestException('Cannot delegate step that is not in progress or pending');
    }

    // Create delegation action
    const delegationAction = this.actionRepository.create({
      type: ActionType.DELEGATE,
      decision: ApprovalDecision.DELEGATED,
      comment: `Delegated to another user: ${delegationReason}`,
      reason: delegationReason,
      stepId: stepId,
      userId: currentUserId,
      metadata: {
        delegatedTo: delegateToUserId,
        delegationReason: delegationReason,
      },
    });

    await this.actionRepository.save(delegationAction);

    // Update step assignee
    step.assignedTo = delegateToUserId;
    await this.stepRepository.save(step);
  }

  /**
   * Skip a step (if allowed)
   */
  async skipStep(stepId: string, reason: string, userId: string): Promise<void> {
    const step = await this.stepRepository.findOne({
      where: { id: stepId },
      relations: ['workflow']
    });

    if (!step) {
      throw new NotFoundException(`Approval step with ID ${stepId} not found`);
    }

    if (!step.allowSkip) {
      throw new BadRequestException('This step cannot be skipped');
    }

    if (step.status !== StepStatus.PENDING && step.status !== StepStatus.IN_PROGRESS) {
      throw new BadRequestException('Cannot skip step that is not pending or in progress');
    }

    // Create skip action
    const skipAction = this.actionRepository.create({
      type: ActionType.SKIP,
      decision: 'skipped' as any,
      comment: `Step skipped: ${reason}`,
      reason: reason,
      stepId: stepId,
      userId: userId,
      metadata: {
        skipReason: reason,
      },
    });

    await this.actionRepository.save(skipAction);

    // Update step status
    step.status = StepStatus.SKIPPED;
    step.completedAt = new Date();
    step.completedBy = userId;

    await this.stepRepository.save(step);

    // Check workflow progression
    await this.checkWorkflowProgression(step.workflow);
  }

  /**
   * Escalate a step
   */
  async escalateStep(stepId: string, escalateToUserId: string, reason: string, userId: string): Promise<void> {
    const step = await this.stepRepository.findOne({
      where: { id: stepId },
      relations: ['workflow']
    });

    if (!step) {
      throw new NotFoundException(`Approval step with ID ${stepId} not found`);
    }

    // Create escalation action
    const escalationAction = this.actionRepository.create({
      type: ActionType.ESCALATE,
      decision: ApprovalDecision.ESCALATED,
      comment: `Step escalated: ${reason}`,
      reason: reason,
      stepId: stepId,
      userId: userId,
      metadata: {
        escalatedTo: escalateToUserId,
        escalationReason: reason,
      },
    });

    await this.actionRepository.save(escalationAction);

    // Update workflow escalation info
    const workflow = step.workflow;
    workflow.escalatedTo = escalateToUserId as any;
    workflow.escalatedAt = new Date();

    await this.workflowRepository.save(workflow);

    // Update step assignee
    step.assignedTo = escalateToUserId;
    await this.stepRepository.save(step);
  }

  /**
   * Get actions for a step
   */
  async getStepActions(stepId: string): Promise<ApprovalAction[]> {
    return this.actionRepository.find({
      where: { stepId },
      relations: ['user', 'delegatedByUser'],
      order: { createdAt: 'ASC' }
    });
  }

  /**
   * Get actions by user
   */
  async getUserActions(userId: string): Promise<ApprovalAction[]> {
    return this.actionRepository.find({
      where: { userId },
      relations: ['step', 'step.workflow', 'user'],
      order: { createdAt: 'DESC' }
    });
  }
}
