import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { DesignApprovalWorkflowService } from '../design-approval-workflow.service';
import { DesignApprovalWorkflow } from '../../entities/design-approval-workflow.entity';
import { ApprovalStep } from '../../entities/approval-step.entity';
import { ApprovalAction } from '../../entities/approval-action.entity';
import { 
  CreateDesignApprovalWorkflowDto, 
  UpdateDesignApprovalWorkflowDto, 
  DesignApprovalWorkflowQueryDto 
} from '../../dto';
import { 
  WorkflowType, 
  WorkflowStatus, 
  ApprovalStrategy,
  WorkflowPriority 
} from '../../enums/design-approval-workflow.enum';
import { StepStatus, StepType } from '../../enums/approval-step.enum';

describe('DesignApprovalWorkflowService', () => {
  let service: DesignApprovalWorkflowService;
  let workflowRepository: Repository<DesignApprovalWorkflow>;
  let stepRepository: Repository<ApprovalStep>;
  let actionRepository: Repository<ApprovalAction>;

  const mockWorkflowRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockStepRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
  };

  const mockActionRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
  };

  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
  };

  const mockWorkflow = {
    id: 'workflow-123',
    name: 'Mobile App Design Approval',
    description: 'Complete approval process for mobile app design',
    type: WorkflowType.SEQUENTIAL,
    status: WorkflowStatus.DRAFT,
    approvalStrategy: ApprovalStrategy.ALL_REQUIRED,
    requiredApprovals: 3,
    allowParallelApproval: false,
    currentStep: 1,
    totalSteps: 2,
    completedSteps: 0,
    approvalPercentage: 0,
    prototypeId: 'prototype-123',
    createdBy: 'user-123',
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    prototype: {
      id: 'prototype-123',
      name: 'Test Prototype',
      currentVersion: '1.0.0',
    },
    creator: {
      id: 'user-123',
      name: 'Test User',
      email: '<EMAIL>',
    },
    steps: [],
  };

  const mockStep = {
    id: 'step-123',
    stepNumber: 1,
    stepName: 'Design Review',
    type: StepType.REVIEW,
    status: StepStatus.PENDING,
    isRequired: true,
    allowSkip: false,
    requiredApprovers: 1,
    approvalCount: 0,
    rejectionCount: 0,
    workflowId: 'workflow-123',
    createdBy: 'user-123',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DesignApprovalWorkflowService,
        {
          provide: getRepositoryToken(DesignApprovalWorkflow),
          useValue: mockWorkflowRepository,
        },
        {
          provide: getRepositoryToken(ApprovalStep),
          useValue: mockStepRepository,
        },
        {
          provide: getRepositoryToken(ApprovalAction),
          useValue: mockActionRepository,
        },
      ],
    }).compile();

    service = module.get<DesignApprovalWorkflowService>(DesignApprovalWorkflowService);
    workflowRepository = module.get<Repository<DesignApprovalWorkflow>>(getRepositoryToken(DesignApprovalWorkflow));
    stepRepository = module.get<Repository<ApprovalStep>>(getRepositoryToken(ApprovalStep));
    actionRepository = module.get<Repository<ApprovalAction>>(getRepositoryToken(ApprovalAction));

    // Setup query builder mock
    mockWorkflowRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new design approval workflow', async () => {
      const createDto: CreateDesignApprovalWorkflowDto = {
        name: 'Mobile App Design Approval',
        description: 'Complete approval process for mobile app design',
        type: WorkflowType.SEQUENTIAL,
        approvalStrategy: ApprovalStrategy.ALL_REQUIRED,
        prototypeId: 'prototype-123',
        steps: [
          {
            stepName: 'Design Review',
            requiredApprovers: 1,
            approverIds: ['user-456'],
          },
          {
            stepName: 'Stakeholder Approval',
            requiredApprovers: 2,
            approverIds: ['user-789', 'user-012'],
          },
        ],
      };

      const userId = 'user-123';

      mockWorkflowRepository.create.mockReturnValue(mockWorkflow);
      mockWorkflowRepository.save.mockResolvedValue(mockWorkflow);
      mockStepRepository.create.mockReturnValue(mockStep);
      mockStepRepository.save.mockResolvedValue(mockStep);
      mockWorkflowRepository.findOne.mockResolvedValue(mockWorkflow);

      const result = await service.create(createDto, userId);

      expect(workflowRepository.create).toHaveBeenCalled();
      expect(workflowRepository.save).toHaveBeenCalledWith(mockWorkflow);
      expect(stepRepository.create).toHaveBeenCalledTimes(2);
      expect(stepRepository.save).toHaveBeenCalledTimes(2);
      expect(result).toBeDefined();
      expect(result.id).toBe(mockWorkflow.id);
    });

    it('should create workflow with advanced configuration', async () => {
      const createDto: CreateDesignApprovalWorkflowDto = {
        name: 'Advanced Workflow',
        prototypeId: 'prototype-123',
        type: WorkflowType.PARALLEL,
        approvalStrategy: ApprovalStrategy.MAJORITY_REQUIRED,
        allowParallelApproval: true,
        autoApproveOnThreshold: true,
        approvalThresholdPercentage: 75.0,
        dueDate: '2024-02-15T17:00:00Z',
        enableEmailNotifications: true,
        enableSlackNotifications: false,
        customProperties: { department: 'Design' },
        steps: [
          {
            stepName: 'Quick Review',
            requiredApprovers: 1,
          },
        ],
      };

      const userId = 'user-123';

      mockWorkflowRepository.create.mockReturnValue(mockWorkflow);
      mockWorkflowRepository.save.mockResolvedValue(mockWorkflow);
      mockStepRepository.create.mockReturnValue(mockStep);
      mockStepRepository.save.mockResolvedValue(mockStep);
      mockWorkflowRepository.findOne.mockResolvedValue(mockWorkflow);

      await service.create(createDto, userId);

      expect(workflowRepository.create).toHaveBeenCalled();
      expect(workflowRepository.save).toHaveBeenCalled();
    });
  });

  describe('findAll', () => {
    it('should return paginated workflows', async () => {
      const query: DesignApprovalWorkflowQueryDto = {
        page: 1,
        limit: 20,
      };

      const workflows = [mockWorkflow];
      const total = 1;

      mockQueryBuilder.getManyAndCount.mockResolvedValue([workflows, total]);

      const result = await service.findAll(query);

      expect(result.data).toHaveLength(1);
      expect(result.meta.total).toBe(1);
      expect(result.meta.page).toBe(1);
      expect(result.meta.limit).toBe(20);
    });

    it('should apply filters correctly', async () => {
      const query: DesignApprovalWorkflowQueryDto = {
        prototypeId: 'prototype-123',
        status: WorkflowStatus.ACTIVE,
        priority: WorkflowPriority.HIGH,
        search: 'mobile',
        activeOnly: true,
      };

      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll(query);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'workflow.prototypeId = :prototypeId',
        { prototypeId: 'prototype-123' }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'workflow.status = :status',
        { status: WorkflowStatus.ACTIVE }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'workflow.priority = :priority',
        { priority: WorkflowPriority.HIGH }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(workflow.name ILIKE :search OR workflow.description ILIKE :search)',
        { search: '%mobile%' }
      );
    });

    it('should handle overdue filter', async () => {
      const query: DesignApprovalWorkflowQueryDto = {
        overdueOnly: true,
      };

      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll(query);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'workflow.dueDate < :now AND workflow.status != :completed',
        expect.objectContaining({
          now: expect.any(Date),
          completed: WorkflowStatus.COMPLETED,
        })
      );
    });
  });

  describe('findOne', () => {
    it('should return a workflow by ID', async () => {
      const workflowId = 'workflow-123';

      mockWorkflowRepository.findOne.mockResolvedValue(mockWorkflow);

      const result = await service.findOne(workflowId);

      expect(workflowRepository.findOne).toHaveBeenCalledWith({
        where: { id: workflowId },
        relations: [
          'prototype', 
          'creator', 
          'assignee', 
          'completedBy', 
          'escalatedTo',
          'steps',
          'steps.actions',
          'steps.actions.user',
          'steps.assignee',
          'steps.completedBy'
        ],
      });
      expect(result).toBeDefined();
      expect(result.id).toBe(mockWorkflow.id);
    });

    it('should throw NotFoundException when workflow not found', async () => {
      const workflowId = 'nonexistent-id';

      mockWorkflowRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(workflowId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a workflow', async () => {
      const workflowId = 'workflow-123';
      const updateDto: UpdateDesignApprovalWorkflowDto = {
        name: 'Updated Workflow Name',
        priority: WorkflowPriority.HIGH,
        dueDate: '2024-03-01T17:00:00Z',
      };
      const userId = 'user-123';

      mockWorkflowRepository.findOne
        .mockResolvedValueOnce(mockWorkflow) // First call for finding workflow
        .mockResolvedValueOnce(mockWorkflow); // Second call for finding with relations

      mockWorkflowRepository.save.mockResolvedValue({
        ...mockWorkflow,
        ...updateDto,
        dueDate: new Date('2024-03-01T17:00:00Z'),
      });

      const result = await service.update(workflowId, updateDto, userId);

      expect(workflowRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          ...updateDto,
          dueDate: new Date('2024-03-01T17:00:00Z'),
        })
      );
      expect(result).toBeDefined();
    });

    it('should throw NotFoundException when workflow not found', async () => {
      const workflowId = 'nonexistent-id';
      const updateDto: UpdateDesignApprovalWorkflowDto = { name: 'Updated' };
      const userId = 'user-123';

      mockWorkflowRepository.findOne.mockResolvedValue(null);

      await expect(service.update(workflowId, updateDto, userId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should soft delete a workflow', async () => {
      const workflowId = 'workflow-123';
      const userId = 'user-123';

      mockWorkflowRepository.findOne.mockResolvedValue(mockWorkflow);
      mockWorkflowRepository.save.mockResolvedValue({
        ...mockWorkflow,
        deletedAt: new Date(),
        deletedBy: userId,
      });

      await service.remove(workflowId, userId);

      expect(workflowRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          deletedAt: expect.any(Date),
          deletedBy: userId,
        })
      );
    });

    it('should throw NotFoundException when workflow not found', async () => {
      const workflowId = 'nonexistent-id';
      const userId = 'user-123';

      mockWorkflowRepository.findOne.mockResolvedValue(null);

      await expect(service.remove(workflowId, userId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('startWorkflow', () => {
    it('should start a draft workflow', async () => {
      const workflowId = 'workflow-123';
      const userId = 'user-123';

      const draftWorkflow = {
        ...mockWorkflow,
        status: WorkflowStatus.DRAFT,
        steps: Promise.resolve([{ ...mockStep, stepNumber: 1 }]),
      };

      mockWorkflowRepository.findOne
        .mockResolvedValueOnce(draftWorkflow) // First call for finding workflow
        .mockResolvedValueOnce(draftWorkflow); // Second call for finding with relations

      mockWorkflowRepository.save.mockResolvedValue({
        ...draftWorkflow,
        status: WorkflowStatus.ACTIVE,
        startedAt: new Date(),
      });

      mockStepRepository.save.mockResolvedValue({
        ...mockStep,
        status: StepStatus.IN_PROGRESS,
      });

      const result = await service.startWorkflow(workflowId, userId);

      expect(workflowRepository.save).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it('should throw BadRequestException when workflow is not in draft status', async () => {
      const workflowId = 'workflow-123';
      const userId = 'user-123';

      const activeWorkflow = {
        ...mockWorkflow,
        status: WorkflowStatus.ACTIVE,
      };

      mockWorkflowRepository.findOne.mockResolvedValue(activeWorkflow);

      await expect(service.startWorkflow(workflowId, userId)).rejects.toThrow(BadRequestException);
    });
  });
});
