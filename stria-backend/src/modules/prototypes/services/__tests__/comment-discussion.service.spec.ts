import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { CommentDiscussionService } from '../comment-discussion.service';
import { CommentDiscussion } from '../../entities/comment-discussion.entity';
import { 
  CreateCommentDiscussionDto, 
  UpdateCommentDiscussionDto, 
  CommentDiscussionQueryDto 
} from '../../dto';
import { 
  DiscussionType, 
  DiscussionStatus, 
  DiscussionPriority,
  CommentType
} from '../../enums/comment-discussion.enum';

describe('CommentDiscussionService', () => {
  let service: CommentDiscussionService;
  let repository: Repository<CommentDiscussion>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
  };

  const mockDiscussion = {
    id: 'discussion-123',
    title: 'Design feedback for mobile layout',
    description: 'Let\'s discuss the mobile layout design',
    type: DiscussionType.FEEDBACK,
    status: DiscussionStatus.OPEN,
    priority: DiscussionPriority.MEDIUM,
    commentCount: 1,
    participantCount: 1,
    isPrivate: false,
    allowAnonymous: false,
    requiresModeration: false,
    isPinned: false,
    isLocked: false,
    hasSolution: false,
    prototypeId: 'prototype-123',
    createdBy: 'user-123',
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    prototype: {
      id: 'prototype-123',
      name: 'Test Prototype',
      currentVersion: '1.0.0',
    },
    creator: {
      id: 'user-123',
      name: 'Test User',
      email: '<EMAIL>',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CommentDiscussionService,
        {
          provide: getRepositoryToken(CommentDiscussion),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<CommentDiscussionService>(CommentDiscussionService);
    repository = module.get<Repository<CommentDiscussion>>(getRepositoryToken(CommentDiscussion));

    // Setup query builder mock
    mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new comment discussion', async () => {
      const createDto: CreateCommentDiscussionDto = {
        title: 'Design feedback for mobile layout',
        description: 'Let\'s discuss the mobile layout design',
        content: 'I think the mobile layout needs some adjustments',
        type: DiscussionType.FEEDBACK,
        prototypeId: 'prototype-123',
      };

      const userId = 'user-123';

      mockRepository.create.mockReturnValue(mockDiscussion);
      mockRepository.save.mockResolvedValue(mockDiscussion);
      mockRepository.update.mockResolvedValue({ affected: 1 });
      mockRepository.findOne.mockResolvedValue(mockDiscussion);

      const result = await service.create(createDto, userId);

      expect(repository.create).toHaveBeenCalledWith({
        title: createDto.title,
        description: createDto.description,
        type: createDto.type,
        status: DiscussionStatus.OPEN,
        priority: createDto.priority,
        prototypeId: createDto.prototypeId,
        visualFeedbackId: createDto.visualFeedbackId,
        workflowId: createDto.workflowId,
        parentDiscussionId: createDto.parentDiscussionId,
        isPrivate: false,
        allowAnonymous: false,
        requiresModeration: false,
        isPinned: false,
        isLocked: false,
        createdBy: userId,
        metadata: {},
      });

      expect(repository.save).toHaveBeenCalledWith(mockDiscussion);
      expect(result).toBeDefined();
      expect(result.id).toBe(mockDiscussion.id);
    });

    it('should create discussion with metadata', async () => {
      const createDto: CreateCommentDiscussionDto = {
        title: 'Advanced Discussion',
        content: 'Discussion with metadata',
        prototypeId: 'prototype-123',
        tags: ['design', 'feedback'],
        category: 'design-review',
        expectedResolutionHours: 48,
        customProperties: { department: 'Design' },
        participants: ['user-456', 'user-789'],
        moderators: ['user-123'],
      };

      const userId = 'user-123';

      mockRepository.create.mockReturnValue(mockDiscussion);
      mockRepository.save.mockResolvedValue(mockDiscussion);
      mockRepository.update.mockResolvedValue({ affected: 1 });
      mockRepository.findOne.mockResolvedValue(mockDiscussion);

      await service.create(createDto, userId);

      expect(repository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: {
            tags: ['design', 'feedback'],
            category: 'design-review',
            expectedResolutionHours: 48,
            customProperties: { department: 'Design' },
          },
        })
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated discussions', async () => {
      const query: CommentDiscussionQueryDto = {
        page: 1,
        limit: 20,
      };

      const discussions = [mockDiscussion];
      const total = 1;

      mockQueryBuilder.getManyAndCount.mockResolvedValue([discussions, total]);

      const result = await service.findAll(query);

      expect(result.data).toHaveLength(1);
      expect(result.meta.total).toBe(1);
      expect(result.meta.page).toBe(1);
      expect(result.meta.limit).toBe(20);
    });

    it('should apply filters correctly', async () => {
      const query: CommentDiscussionQueryDto = {
        prototypeId: 'prototype-123',
        type: DiscussionType.FEEDBACK,
        status: DiscussionStatus.OPEN,
        priority: DiscussionPriority.HIGH,
        search: 'mobile layout',
        tags: ['design', 'mobile'],
        unresolvedOnly: true,
      };

      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll(query);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'discussion.prototypeId = :prototypeId',
        { prototypeId: 'prototype-123' }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'discussion.type = :type',
        { type: DiscussionType.FEEDBACK }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'discussion.status = :status',
        { status: DiscussionStatus.OPEN }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'discussion.priority = :priority',
        { priority: DiscussionPriority.HIGH }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(discussion.title ILIKE :search OR discussion.description ILIKE :search)',
        { search: '%mobile layout%' }
      );
    });

    it('should handle user-specific filters', async () => {
      const query: CommentDiscussionQueryDto = {
        myParticipationOnly: true,
        myModerationOnly: true,
      };
      const currentUserId = 'user-123';

      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll(query, currentUserId);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'EXISTS (SELECT 1 FROM discussion_participants dp WHERE dp.discussionId = discussion.id AND dp.userId = :currentUserId)',
        { currentUserId }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'EXISTS (SELECT 1 FROM discussion_moderators dm WHERE dm.discussionId = discussion.id AND dm.userId = :currentUserId)',
        { currentUserId }
      );
    });
  });

  describe('findOne', () => {
    it('should return a discussion by ID', async () => {
      const discussionId = 'discussion-123';

      mockRepository.findOne.mockResolvedValue(mockDiscussion);

      const result = await service.findOne(discussionId);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: discussionId },
        relations: [
          'prototype', 
          'creator', 
          'resolvedBy',
          'closedBy',
          'participants',
          'moderators',
          'comments',
          'comments.author',
          'comments.mentions',
          'comments.reactions',
          'comments.reactions.user'
        ],
      });
      expect(result).toBeDefined();
      expect(result.id).toBe(mockDiscussion.id);
    });

    it('should throw NotFoundException when discussion not found', async () => {
      const discussionId = 'nonexistent-id';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(discussionId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a discussion', async () => {
      const discussionId = 'discussion-123';
      const updateDto: UpdateCommentDiscussionDto = {
        title: 'Updated Discussion Title',
        priority: DiscussionPriority.HIGH,
        tags: ['design', 'urgent'],
      };
      const userId = 'user-123';

      mockRepository.findOne
        .mockResolvedValueOnce(mockDiscussion) // First call for finding discussion
        .mockResolvedValueOnce(mockDiscussion); // Second call for finding with relations

      mockRepository.save.mockResolvedValue({
        ...mockDiscussion,
        ...updateDto,
      });

      const result = await service.update(discussionId, updateDto, userId);

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          ...updateDto,
          metadata: {
            tags: ['design', 'urgent'],
          },
        })
      );
      expect(result).toBeDefined();
    });

    it('should throw ForbiddenException when user is not creator', async () => {
      const discussionId = 'discussion-123';
      const updateDto: UpdateCommentDiscussionDto = { title: 'Updated' };
      const userId = 'different-user';

      mockRepository.findOne.mockResolvedValue(mockDiscussion);

      await expect(service.update(discussionId, updateDto, userId)).rejects.toThrow(ForbiddenException);
    });

    it('should throw NotFoundException when discussion not found', async () => {
      const discussionId = 'nonexistent-id';
      const updateDto: UpdateCommentDiscussionDto = { title: 'Updated' };
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update(discussionId, updateDto, userId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should soft delete a discussion', async () => {
      const discussionId = 'discussion-123';
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(mockDiscussion);
      mockRepository.save.mockResolvedValue({
        ...mockDiscussion,
        deletedAt: new Date(),
      });

      await service.remove(discussionId, userId);

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          deletedAt: expect.any(Date),
        })
      );
    });

    it('should throw ForbiddenException when user is not creator', async () => {
      const discussionId = 'discussion-123';
      const userId = 'different-user';

      mockRepository.findOne.mockResolvedValue(mockDiscussion);

      await expect(service.remove(discussionId, userId)).rejects.toThrow(ForbiddenException);
    });

    it('should throw NotFoundException when discussion not found', async () => {
      const discussionId = 'nonexistent-id';
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove(discussionId, userId)).rejects.toThrow(NotFoundException);
    });
  });
});
