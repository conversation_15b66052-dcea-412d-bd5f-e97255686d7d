import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { PrototypesService } from '../prototypes.service';
import { Prototype } from '../../entities/prototype.entity';
import { CreatePrototypeDto, UpdatePrototypeDto, PrototypeQueryDto } from '../../dto';
import { PrototypeType, PrototypeStatus, FigmaIntegrationStatus } from '../../enums/prototype.enum';

describe('PrototypesService', () => {
  let service: PrototypesService;
  let repository: Repository<Prototype>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
  };

  const mockPrototype = {
    id: 'prototype-123',
    name: 'Test Prototype',
    description: 'Test description',
    type: PrototypeType.HIGH_FIDELITY,
    status: PrototypeStatus.DRAFT,
    projectId: 'project-123',
    createdBy: 'user-123',
    figmaIntegrationStatus: FigmaIntegrationStatus.NOT_CONNECTED,
    currentVersion: '1.0.0',
    versionCount: 1,
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    project: {
      id: 'project-123',
      name: 'Test Project',
      status: 'active',
    },
    creator: {
      id: 'user-123',
      name: 'Test User',
      email: '<EMAIL>',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PrototypesService,
        {
          provide: getRepositoryToken(Prototype),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<PrototypesService>(PrototypesService);
    repository = module.get<Repository<Prototype>>(getRepositoryToken(Prototype));

    // Setup query builder mock
    mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new prototype', async () => {
      const createDto: CreatePrototypeDto = {
        name: 'Test Prototype',
        description: 'Test description',
        type: PrototypeType.HIGH_FIDELITY,
        projectId: 'project-123',
      };

      const userId = 'user-123';

      mockRepository.create.mockReturnValue(mockPrototype);
      mockRepository.save.mockResolvedValue(mockPrototype);
      mockRepository.findOne.mockResolvedValue(mockPrototype);

      const result = await service.create(createDto, userId);

      expect(repository.create).toHaveBeenCalledWith({
        name: createDto.name,
        description: createDto.description,
        type: createDto.type,
        status: createDto.status,
        projectId: createDto.projectId,
        figmaFileId: createDto.figmaFileId,
        figmaFileUrl: createDto.figmaFileUrl,
        figmaEmbedUrl: createDto.figmaEmbedUrl,
        figmaIntegrationStatus: FigmaIntegrationStatus.NOT_CONNECTED,
        createdBy: userId,
        metadata: {},
      });

      expect(repository.save).toHaveBeenCalledWith(mockPrototype);
      expect(result).toBeDefined();
      expect(result.id).toBe(mockPrototype.id);
    });

    it('should create prototype with Figma integration', async () => {
      const createDto: CreatePrototypeDto = {
        name: 'Figma Prototype',
        projectId: 'project-123',
        figmaFileId: 'figma-123',
        figmaFileUrl: 'https://figma.com/file/123',
        figmaNodeIds: ['node1', 'node2'],
      };

      const userId = 'user-123';

      mockRepository.create.mockReturnValue(mockPrototype);
      mockRepository.save.mockResolvedValue(mockPrototype);
      mockRepository.findOne.mockResolvedValue(mockPrototype);

      await service.create(createDto, userId);

      expect(repository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          figmaFileId: 'figma-123',
          figmaIntegrationStatus: FigmaIntegrationStatus.CONNECTED,
          metadata: expect.objectContaining({
            figmaNodeIds: ['node1', 'node2'],
          }),
        })
      );
    });

    it('should handle metadata correctly', async () => {
      const createDto: CreatePrototypeDto = {
        name: 'Test Prototype',
        projectId: 'project-123',
        designSpecs: { width: 1920, height: 1080, scale: 1.0 },
        collaborators: ['user1', 'user2'],
        tags: ['mobile', 'responsive'],
        customProperties: { priority: 'high' },
      };

      const userId = 'user-123';

      mockRepository.create.mockReturnValue(mockPrototype);
      mockRepository.save.mockResolvedValue(mockPrototype);
      mockRepository.findOne.mockResolvedValue(mockPrototype);

      await service.create(createDto, userId);

      expect(repository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: {
            designSpecs: { width: 1920, height: 1080, scale: 1.0 },
            collaborators: ['user1', 'user2'],
            tags: ['mobile', 'responsive'],
            customProperties: { priority: 'high' },
          },
        })
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated prototypes', async () => {
      const query: PrototypeQueryDto = {
        page: 1,
        limit: 20,
      };

      const prototypes = [mockPrototype];
      const total = 1;

      mockQueryBuilder.getManyAndCount.mockResolvedValue([prototypes, total]);

      const result = await service.findAll(query);

      expect(result.data).toHaveLength(1);
      expect(result.meta.total).toBe(1);
      expect(result.meta.page).toBe(1);
      expect(result.meta.limit).toBe(20);
    });

    it('should apply filters correctly', async () => {
      const query: PrototypeQueryDto = {
        projectId: 'project-123',
        type: PrototypeType.HIGH_FIDELITY,
        status: PrototypeStatus.IN_REVIEW,
        search: 'mobile',
      };

      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll(query);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'prototype.projectId = :projectId',
        { projectId: 'project-123' }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'prototype.type = :type',
        { type: PrototypeType.HIGH_FIDELITY }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'prototype.status = :status',
        { status: PrototypeStatus.IN_REVIEW }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(prototype.name ILIKE :search OR prototype.description ILIKE :search)',
        { search: '%mobile%' }
      );
    });

    it('should handle sorting and pagination', async () => {
      const query: PrototypeQueryDto = {
        page: 2,
        limit: 10,
        sortBy: 'name',
        sortOrder: 'ASC',
      };

      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll(query);

      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('prototype.name', 'ASC');
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(10); // (page - 1) * limit
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
    });
  });

  describe('findOne', () => {
    it('should return a prototype by ID', async () => {
      const prototypeId = 'prototype-123';

      mockRepository.findOne.mockResolvedValue(mockPrototype);

      const result = await service.findOne(prototypeId);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: prototypeId },
        relations: ['project', 'creator', 'updater'],
      });
      expect(result).toBeDefined();
      expect(result.id).toBe(mockPrototype.id);
    });

    it('should throw NotFoundException when prototype not found', async () => {
      const prototypeId = 'nonexistent-id';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(prototypeId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a prototype', async () => {
      const prototypeId = 'prototype-123';
      const updateDto: UpdatePrototypeDto = {
        name: 'Updated Prototype',
        status: PrototypeStatus.IN_REVIEW,
      };
      const userId = 'user-123';

      mockRepository.findOne
        .mockResolvedValueOnce(mockPrototype) // First call for finding prototype
        .mockResolvedValueOnce(mockPrototype); // Second call for finding with relations

      mockRepository.save.mockResolvedValue({
        ...mockPrototype,
        ...updateDto,
        updatedBy: userId,
      });

      const result = await service.update(prototypeId, updateDto, userId);

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          ...updateDto,
          updatedBy: userId,
        })
      );
      expect(result).toBeDefined();
    });

    it('should throw NotFoundException when prototype not found', async () => {
      const prototypeId = 'nonexistent-id';
      const updateDto: UpdatePrototypeDto = { name: 'Updated' };
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update(prototypeId, updateDto, userId)).rejects.toThrow(NotFoundException);
    });

    it('should update metadata correctly', async () => {
      const prototypeId = 'prototype-123';
      const updateDto: UpdatePrototypeDto = {
        tags: ['updated', 'tags'],
        customProperties: { priority: 'high' },
      };
      const userId = 'user-123';

      const prototypeWithMetadata = {
        ...mockPrototype,
        metadata: { existingField: 'value' },
      };

      mockRepository.findOne
        .mockResolvedValueOnce(prototypeWithMetadata)
        .mockResolvedValueOnce(prototypeWithMetadata);

      mockRepository.save.mockResolvedValue(prototypeWithMetadata);

      await service.update(prototypeId, updateDto, userId);

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: expect.objectContaining({
            existingField: 'value',
            tags: ['updated', 'tags'],
            customProperties: { priority: 'high' },
          }),
        })
      );
    });
  });

  describe('remove', () => {
    it('should soft delete a prototype', async () => {
      const prototypeId = 'prototype-123';
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(mockPrototype);
      mockRepository.save.mockResolvedValue({
        ...mockPrototype,
        deletedAt: new Date(),
        deletedBy: userId,
      });

      await service.remove(prototypeId, userId);

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          deletedAt: expect.any(Date),
          deletedBy: userId,
        })
      );
    });

    it('should throw NotFoundException when prototype not found', async () => {
      const prototypeId = 'nonexistent-id';
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove(prototypeId, userId)).rejects.toThrow(NotFoundException);
    });
  });
});
