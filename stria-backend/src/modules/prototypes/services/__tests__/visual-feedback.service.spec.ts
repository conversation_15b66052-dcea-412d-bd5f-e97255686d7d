import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { VisualFeedbackService } from '../visual-feedback.service';
import { VisualFeedback } from '../../entities/visual-feedback.entity';
import { CreateVisualFeedbackDto, UpdateVisualFeedbackDto, VisualFeedbackQueryDto } from '../../dto';
import { FeedbackType, FeedbackStatus, FeedbackPriority, AnnotationType } from '../../enums/visual-feedback.enum';

describe('VisualFeedbackService', () => {
  let service: VisualFeedbackService;
  let repository: Repository<VisualFeedback>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
  };

  const mockVisualFeedback = {
    id: 'feedback-123',
    title: 'Button alignment issue',
    content: 'The submit button appears to be misaligned',
    type: FeedbackType.DESIGN_ISSUE,
    status: FeedbackStatus.OPEN,
    priority: FeedbackPriority.MEDIUM,
    annotationType: AnnotationType.RECTANGLE,
    positionX: 150.5,
    positionY: 200.75,
    positionZ: 1,
    width: 100,
    height: 50,
    color: '#FF6B6B',
    opacity: 0.8,
    prototypeId: 'prototype-123',
    createdBy: 'user-123',
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    prototype: {
      id: 'prototype-123',
      name: 'Test Prototype',
      currentVersion: '1.0.0',
    },
    creator: {
      id: 'user-123',
      name: 'Test User',
      email: '<EMAIL>',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VisualFeedbackService,
        {
          provide: getRepositoryToken(VisualFeedback),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<VisualFeedbackService>(VisualFeedbackService);
    repository = module.get<Repository<VisualFeedback>>(getRepositoryToken(VisualFeedback));

    // Setup query builder mock
    mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new visual feedback', async () => {
      const createDto: CreateVisualFeedbackDto = {
        title: 'Button alignment issue',
        content: 'The submit button appears to be misaligned',
        type: FeedbackType.DESIGN_ISSUE,
        annotationType: AnnotationType.RECTANGLE,
        positionX: 150.5,
        positionY: 200.75,
        width: 100,
        height: 50,
        prototypeId: 'prototype-123',
      };

      const userId = 'user-123';

      mockRepository.create.mockReturnValue(mockVisualFeedback);
      mockRepository.save.mockResolvedValue(mockVisualFeedback);
      mockRepository.findOne.mockResolvedValue(mockVisualFeedback);

      const result = await service.create(createDto, userId);

      expect(repository.create).toHaveBeenCalledWith({
        title: createDto.title,
        content: createDto.content,
        type: createDto.type,
        status: createDto.status,
        priority: createDto.priority,
        annotationType: createDto.annotationType,
        positionX: createDto.positionX,
        positionY: createDto.positionY,
        positionZ: 0,
        width: createDto.width,
        height: createDto.height,
        color: '#FF6B6B',
        borderColor: createDto.borderColor,
        backgroundColor: createDto.backgroundColor,
        opacity: 1.0,
        targetElementId: createDto.targetElementId,
        targetElementType: createDto.targetElementType,
        targetElementName: createDto.targetElementName,
        prototypeId: createDto.prototypeId,
        versionId: createDto.versionId,
        createdBy: userId,
        assignedTo: createDto.assignedTo,
        metadata: {},
      });

      expect(repository.save).toHaveBeenCalledWith(mockVisualFeedback);
      expect(result).toBeDefined();
      expect(result.id).toBe(mockVisualFeedback.id);
    });

    it('should create feedback with metadata', async () => {
      const createDto: CreateVisualFeedbackDto = {
        content: 'Test feedback',
        positionX: 100,
        positionY: 200,
        prototypeId: 'prototype-123',
        tags: ['ui', 'critical'],
        category: 'usability',
        severity: 'high',
        estimatedEffort: 2.5,
        attachments: ['file1.png', 'file2.pdf'],
        customFields: { browser: 'Chrome' },
      };

      const userId = 'user-123';

      mockRepository.create.mockReturnValue(mockVisualFeedback);
      mockRepository.save.mockResolvedValue(mockVisualFeedback);
      mockRepository.findOne.mockResolvedValue(mockVisualFeedback);

      await service.create(createDto, userId);

      expect(repository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: {
            tags: ['ui', 'critical'],
            category: 'usability',
            severity: 'high',
            estimatedEffort: 2.5,
            attachments: ['file1.png', 'file2.pdf'],
            customFields: { browser: 'Chrome' },
          },
        })
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated visual feedback', async () => {
      const query: VisualFeedbackQueryDto = {
        page: 1,
        limit: 20,
      };

      const feedbacks = [mockVisualFeedback];
      const total = 1;

      mockQueryBuilder.getManyAndCount.mockResolvedValue([feedbacks, total]);

      const result = await service.findAll(query);

      expect(result.data).toHaveLength(1);
      expect(result.meta.total).toBe(1);
      expect(result.meta.page).toBe(1);
      expect(result.meta.limit).toBe(20);
    });

    it('should apply filters correctly', async () => {
      const query: VisualFeedbackQueryDto = {
        prototypeId: 'prototype-123',
        type: FeedbackType.DESIGN_ISSUE,
        status: FeedbackStatus.OPEN,
        priority: FeedbackPriority.HIGH,
        search: 'button',
        assignedTo: 'user-456',
      };

      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll(query);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'feedback.prototypeId = :prototypeId',
        { prototypeId: 'prototype-123' }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'feedback.type = :type',
        { type: FeedbackType.DESIGN_ISSUE }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'feedback.status = :status',
        { status: FeedbackStatus.OPEN }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'feedback.priority = :priority',
        { priority: FeedbackPriority.HIGH }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(feedback.title ILIKE :search OR feedback.content ILIKE :search)',
        { search: '%button%' }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'feedback.assignedTo = :assignedTo',
        { assignedTo: 'user-456' }
      );
    });

    it('should handle position-based filtering', async () => {
      const query: VisualFeedbackQueryDto = {
        minX: 100,
        maxX: 500,
        minY: 200,
        maxY: 800,
      };

      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll(query);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'feedback.positionX >= :minX',
        { minX: 100 }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'feedback.positionX <= :maxX',
        { maxX: 500 }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'feedback.positionY >= :minY',
        { minY: 200 }
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'feedback.positionY <= :maxY',
        { maxY: 800 }
      );
    });

    it('should handle unresolved only filter', async () => {
      const query: VisualFeedbackQueryDto = {
        unresolvedOnly: true,
      };

      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll(query);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('feedback.resolvedAt IS NULL');
    });
  });

  describe('findOne', () => {
    it('should return a visual feedback by ID', async () => {
      const feedbackId = 'feedback-123';

      mockRepository.findOne.mockResolvedValue(mockVisualFeedback);

      const result = await service.findOne(feedbackId);

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: feedbackId },
        relations: ['prototype', 'version', 'creator', 'assignee', 'resolver'],
      });
      expect(result).toBeDefined();
      expect(result.id).toBe(mockVisualFeedback.id);
    });

    it('should throw NotFoundException when feedback not found', async () => {
      const feedbackId = 'nonexistent-id';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(feedbackId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a visual feedback', async () => {
      const feedbackId = 'feedback-123';
      const updateDto: UpdateVisualFeedbackDto = {
        title: 'Updated feedback title',
        status: FeedbackStatus.IN_PROGRESS,
        priority: FeedbackPriority.HIGH,
      };
      const userId = 'user-123';

      mockRepository.findOne
        .mockResolvedValueOnce(mockVisualFeedback) // First call for finding feedback
        .mockResolvedValueOnce(mockVisualFeedback); // Second call for finding with relations

      mockRepository.save.mockResolvedValue({
        ...mockVisualFeedback,
        ...updateDto,
      });

      const result = await service.update(feedbackId, updateDto, userId);

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining(updateDto)
      );
      expect(result).toBeDefined();
    });

    it('should handle resolution status change', async () => {
      const feedbackId = 'feedback-123';
      const updateDto: UpdateVisualFeedbackDto = {
        status: FeedbackStatus.RESOLVED,
      };
      const userId = 'user-123';

      const unresolvedFeedback = {
        ...mockVisualFeedback,
        resolvedAt: null,
        resolvedBy: null,
      };

      mockRepository.findOne
        .mockResolvedValueOnce(unresolvedFeedback)
        .mockResolvedValueOnce(unresolvedFeedback);

      mockRepository.save.mockResolvedValue(unresolvedFeedback);

      await service.update(feedbackId, updateDto, userId);

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: FeedbackStatus.RESOLVED,
          resolvedAt: expect.any(Date),
          resolvedBy: userId,
        })
      );
    });

    it('should throw NotFoundException when feedback not found', async () => {
      const feedbackId = 'nonexistent-id';
      const updateDto: UpdateVisualFeedbackDto = { title: 'Updated' };
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.update(feedbackId, updateDto, userId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should soft delete a visual feedback', async () => {
      const feedbackId = 'feedback-123';
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(mockVisualFeedback);
      mockRepository.save.mockResolvedValue({
        ...mockVisualFeedback,
        deletedAt: new Date(),
        deletedBy: userId,
      });

      await service.remove(feedbackId, userId);

      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          deletedAt: expect.any(Date),
          deletedBy: userId,
        })
      );
    });

    it('should throw NotFoundException when feedback not found', async () => {
      const feedbackId = 'nonexistent-id';
      const userId = 'user-123';

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove(feedbackId, userId)).rejects.toThrow(NotFoundException);
    });
  });
});
