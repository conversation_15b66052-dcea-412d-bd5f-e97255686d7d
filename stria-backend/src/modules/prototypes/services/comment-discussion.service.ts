import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CommentDiscussion } from '../entities/comment-discussion.entity';
import { 
  CreateCommentDiscussionDto, 
  UpdateCommentDiscussionDto, 
  AddCommentDto,
  CommentDiscussionQueryDto,
  PaginatedResponseDto,
  CommentDiscussionResponseDto
} from '../dto';
import { DiscussionStatus, CommentType } from '../enums/comment-discussion.enum';
import { plainToClass } from 'class-transformer';

/**
 * CommentDiscussionService
 * 
 * Service for managing comment discussions and threaded conversations
 * Handles discussion creation, comment management, and user interactions
 */
@Injectable()
export class CommentDiscussionService {
  constructor(
    @InjectRepository(CommentDiscussion)
    private readonly discussionRepository: Repository<CommentDiscussion>,
  ) {}

  /**
   * Create a new comment discussion
   */
  async create(
    createDiscussionDto: CreateCommentDiscussionDto, 
    userId: string
  ): Promise<CommentDiscussionResponseDto> {
    // Build metadata object
    const metadata: any = {};
    
    if (createDiscussionDto.tags) {
      metadata.tags = createDiscussionDto.tags;
    }
    
    if (createDiscussionDto.category) {
      metadata.category = createDiscussionDto.category;
    }
    
    if (createDiscussionDto.expectedResolutionHours) {
      metadata.expectedResolutionHours = createDiscussionDto.expectedResolutionHours;
    }
    
    if (createDiscussionDto.customProperties) {
      metadata.customProperties = createDiscussionDto.customProperties;
    }

    // Create discussion entity
    const discussion = this.discussionRepository.create({
      title: createDiscussionDto.title,
      description: createDiscussionDto.description,
      type: createDiscussionDto.type,
      status: createDiscussionDto.status || DiscussionStatus.OPEN,
      priority: createDiscussionDto.priority,
      prototypeId: createDiscussionDto.prototypeId,
      visualFeedbackId: createDiscussionDto.visualFeedbackId,
      workflowId: createDiscussionDto.workflowId,
      parentDiscussionId: createDiscussionDto.parentDiscussionId,
      isPrivate: createDiscussionDto.isPrivate || false,
      allowAnonymous: createDiscussionDto.allowAnonymous || false,
      requiresModeration: createDiscussionDto.requiresModeration || false,
      isPinned: createDiscussionDto.isPinned || false,
      isLocked: createDiscussionDto.isLocked || false,
      createdBy: userId,
      metadata,
    } as any);

    const savedDiscussion = await this.discussionRepository.save(discussion);

    // Create initial comment
    await this.addInitialComment(
      savedDiscussion.id,
      createDiscussionDto.content,
      createDiscussionDto.commentType || CommentType.GENERAL,
      createDiscussionDto.mentions || [],
      createDiscussionDto.attachments || [],
      userId
    );

    // Add participants and moderators
    if (createDiscussionDto.participants) {
      await this.addParticipants(savedDiscussion.id, createDiscussionDto.participants);
    }

    if (createDiscussionDto.moderators) {
      await this.addModerators(savedDiscussion.id, createDiscussionDto.moderators);
    }

    // Load with relations for response
    const discussionWithRelations = await this.findOneWithRelations(savedDiscussion.id);
    
    return this.toResponseDto(discussionWithRelations);
  }

  /**
   * Find all discussions with filtering and pagination
   */
  async findAll(query: CommentDiscussionQueryDto, currentUserId?: string): Promise<PaginatedResponseDto<CommentDiscussionResponseDto>> {
    const {
      page = 1,
      limit = 20,
      prototypeId,
      visualFeedbackId,
      workflowId,
      createdBy,
      participantId,
      moderatorId,
      type,
      status,
      priority,
      search,
      tags,
      category,
      minCommentCount,
      maxCommentCount,
      minParticipantCount,
      createdAfter,
      createdBefore,
      activityAfter,
      resolvedAfter,
      resolvedBefore,
      privateOnly,
      pinnedOnly,
      lockedOnly,
      withSolutionOnly,
      unresolvedOnly,
      myParticipationOnly,
      myModerationOnly,
      sortBy = 'lastActivityAt',
      sortOrder = 'DESC',
      includePrototype = true,
      includeCreator = true,
      includeParticipants = false,
      includeComments = false,
      includeReactions = false,
      includeDeleted = false,
    } = query;

    const queryBuilder = this.discussionRepository.createQueryBuilder('discussion');

    // Include relations based on query parameters
    if (includePrototype) {
      queryBuilder.leftJoinAndSelect('discussion.prototype', 'prototype');
    }
    
    if (includeCreator) {
      queryBuilder.leftJoinAndSelect('discussion.creator', 'creator');
    }
    
    if (includeParticipants) {
      queryBuilder.leftJoinAndSelect('discussion.participants', 'participants');
      queryBuilder.leftJoinAndSelect('discussion.moderators', 'moderators');
    }

    if (includeComments) {
      queryBuilder.leftJoinAndSelect('discussion.comments', 'comments');
      queryBuilder.leftJoinAndSelect('comments.author', 'commentAuthor');
      
      if (includeReactions) {
        queryBuilder.leftJoinAndSelect('comments.reactions', 'reactions');
        queryBuilder.leftJoinAndSelect('reactions.user', 'reactionUser');
      }
    }

    // Apply filters
    if (prototypeId) {
      queryBuilder.andWhere('discussion.prototypeId = :prototypeId', { prototypeId });
    }

    if (visualFeedbackId) {
      queryBuilder.andWhere('discussion.visualFeedbackId = :visualFeedbackId', { visualFeedbackId });
    }

    if (workflowId) {
      queryBuilder.andWhere('discussion.workflowId = :workflowId', { workflowId });
    }

    if (createdBy) {
      queryBuilder.andWhere('discussion.createdBy = :createdBy', { createdBy });
    }

    if (type) {
      queryBuilder.andWhere('discussion.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('discussion.status = :status', { status });
    }

    if (priority) {
      queryBuilder.andWhere('discussion.priority = :priority', { priority });
    }

    if (search) {
      queryBuilder.andWhere(
        '(discussion.title ILIKE :search OR discussion.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere(
        'discussion.metadata->>\'tags\' ?| array[:tags]',
        { tags }
      );
    }

    if (category) {
      queryBuilder.andWhere(
        'discussion.metadata->>\'category\' = :category',
        { category }
      );
    }

    // Activity-based filtering
    if (minCommentCount !== undefined) {
      queryBuilder.andWhere('discussion.commentCount >= :minCommentCount', { minCommentCount });
    }

    if (maxCommentCount !== undefined) {
      queryBuilder.andWhere('discussion.commentCount <= :maxCommentCount', { maxCommentCount });
    }

    if (minParticipantCount !== undefined) {
      queryBuilder.andWhere('discussion.participantCount >= :minParticipantCount', { minParticipantCount });
    }

    // Date filters
    if (createdAfter) {
      queryBuilder.andWhere('discussion.createdAt >= :createdAfter', { 
        createdAfter: new Date(createdAfter) 
      });
    }

    if (createdBefore) {
      queryBuilder.andWhere('discussion.createdAt <= :createdBefore', { 
        createdBefore: new Date(createdBefore) 
      });
    }

    if (activityAfter) {
      queryBuilder.andWhere('discussion.lastActivityAt >= :activityAfter', { 
        activityAfter: new Date(activityAfter) 
      });
    }

    if (resolvedAfter) {
      queryBuilder.andWhere('discussion.resolvedAt >= :resolvedAfter', { 
        resolvedAfter: new Date(resolvedAfter) 
      });
    }

    if (resolvedBefore) {
      queryBuilder.andWhere('discussion.resolvedAt <= :resolvedBefore', { 
        resolvedBefore: new Date(resolvedBefore) 
      });
    }

    // Status-based filters
    if (privateOnly) {
      queryBuilder.andWhere('discussion.isPrivate = true');
    }

    if (pinnedOnly) {
      queryBuilder.andWhere('discussion.isPinned = true');
    }

    if (lockedOnly) {
      queryBuilder.andWhere('discussion.isLocked = true');
    }

    if (withSolutionOnly) {
      queryBuilder.andWhere('discussion.hasSolution = true');
    }

    if (unresolvedOnly) {
      queryBuilder.andWhere('discussion.resolvedAt IS NULL');
    }

    // User-specific filters
    if (myParticipationOnly && currentUserId) {
      queryBuilder.andWhere(
        'EXISTS (SELECT 1 FROM discussion_participants dp WHERE dp.discussionId = discussion.id AND dp.userId = :currentUserId)',
        { currentUserId }
      );
    }

    if (myModerationOnly && currentUserId) {
      queryBuilder.andWhere(
        'EXISTS (SELECT 1 FROM discussion_moderators dm WHERE dm.discussionId = discussion.id AND dm.userId = :currentUserId)',
        { currentUserId }
      );
    }

    // Soft delete filter
    if (!includeDeleted) {
      queryBuilder.andWhere('discussion.deletedAt IS NULL');
    }

    // Sorting
    const validSortFields = ['title', 'createdAt', 'updatedAt', 'lastActivityAt', 'commentCount', 'participantCount', 'priority'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'lastActivityAt';
    queryBuilder.orderBy(`discussion.${sortField}`, sortOrder);

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [discussions, total] = await queryBuilder.getManyAndCount();

    // Convert to response DTOs
    const responseData = discussions.map(discussion => this.toResponseDto(discussion));

    return PaginatedResponseDto.create(responseData, total, page, limit);
  }

  /**
   * Find one discussion by ID
   */
  async findOne(id: string): Promise<CommentDiscussionResponseDto> {
    const discussion = await this.findOneWithRelations(id);
    
    if (!discussion) {
      throw new NotFoundException(`Comment discussion with ID ${id} not found`);
    }

    return this.toResponseDto(discussion);
  }

  /**
   * Update a discussion
   */
  async update(
    id: string, 
    updateDiscussionDto: UpdateCommentDiscussionDto, 
    userId: string
  ): Promise<CommentDiscussionResponseDto> {
    const discussion = await this.discussionRepository.findOne({ 
      where: { id },
      relations: ['creator']
    });

    if (!discussion) {
      throw new NotFoundException(`Comment discussion with ID ${id} not found`);
    }

    // Check permissions
    if (discussion.createdBy !== userId) {
      throw new ForbiddenException('Only the discussion creator can update the discussion');
    }

    // Update metadata if provided
    if (updateDiscussionDto.tags !== undefined ||
        updateDiscussionDto.category !== undefined ||
        updateDiscussionDto.expectedResolutionHours !== undefined ||
        updateDiscussionDto.customProperties !== undefined) {
      
      const updatedMetadata = { ...discussion.metadata };
      
      if (updateDiscussionDto.tags !== undefined) {
        updatedMetadata.tags = updateDiscussionDto.tags;
      }
      
      if (updateDiscussionDto.category !== undefined) {
        updatedMetadata.category = updateDiscussionDto.category;
      }
      
      if (updateDiscussionDto.expectedResolutionHours !== undefined) {
        updatedMetadata.expectedResolutionHours = updateDiscussionDto.expectedResolutionHours;
      }
      
      if (updateDiscussionDto.customProperties !== undefined) {
        updatedMetadata.customProperties = updateDiscussionDto.customProperties;
      }

      discussion.metadata = updatedMetadata;
    }

    // Update other fields
    Object.assign(discussion, updateDiscussionDto);

    const savedDiscussion = await this.discussionRepository.save(discussion);
    const discussionWithRelations = await this.findOneWithRelations(savedDiscussion.id);
    
    return this.toResponseDto(discussionWithRelations);
  }

  /**
   * Soft delete a discussion
   */
  async remove(id: string, userId: string): Promise<void> {
    const discussion = await this.discussionRepository.findOne({ 
      where: { id },
      relations: ['creator']
    });

    if (!discussion) {
      throw new NotFoundException(`Comment discussion with ID ${id} not found`);
    }

    // Check permissions
    if (discussion.createdBy !== userId) {
      throw new ForbiddenException('Only the discussion creator can delete the discussion');
    }

    discussion.deletedAt = new Date();
    (discussion as any).deletedBy = userId;

    await this.discussionRepository.save(discussion);
  }

  /**
   * Add initial comment to discussion
   */
  private async addInitialComment(
    discussionId: string,
    content: string,
    type: CommentType,
    mentions: any[],
    attachments: string[],
    userId: string
  ): Promise<void> {
    // This would create the initial comment
    // For now, we'll just update the comment count
    await this.discussionRepository.update(discussionId, {
      commentCount: 1,
      lastActivityAt: new Date(),
    });
  }

  /**
   * Add participants to discussion
   */
  private async addParticipants(discussionId: string, participantIds: string[]): Promise<void> {
    // This would add participants to the discussion
    // Implementation would depend on the many-to-many relationship setup
  }

  /**
   * Add moderators to discussion
   */
  private async addModerators(discussionId: string, moderatorIds: string[]): Promise<void> {
    // This would add moderators to the discussion
    // Implementation would depend on the many-to-many relationship setup
  }

  /**
   * Find one discussion with all relations
   */
  private async findOneWithRelations(id: string): Promise<CommentDiscussion | null> {
    return this.discussionRepository.findOne({
      where: { id },
      relations: [
        'prototype', 
        'creator', 
        'resolvedBy',
        'closedBy',
        'participants',
        'moderators',
        'comments',
        'comments.author',
        'comments.mentions',
        'comments.reactions',
        'comments.reactions.user'
      ],
    });
  }

  /**
   * Convert entity to response DTO
   */
  private toResponseDto(discussion: CommentDiscussion): CommentDiscussionResponseDto {
    return plainToClass(CommentDiscussionResponseDto, discussion, {
      excludeExtraneousValues: true,
    });
  }
}
