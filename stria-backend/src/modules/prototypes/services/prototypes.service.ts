import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Like, Between } from 'typeorm';
import { Prototype } from '../entities/prototype.entity';
import { 
  CreatePrototypeDto, 
  UpdatePrototypeDto, 
  PrototypeQueryDto,
  PaginatedResponseDto,
  PrototypeResponseDto
} from '../dto';
import { PrototypeStatus, FigmaIntegrationStatus } from '../enums/prototype.enum';
import { plainToClass } from 'class-transformer';

/**
 * PrototypesService
 * 
 * Service for managing prototypes with Figma integration
 * Handles CRUD operations, querying, and business logic
 */
@Injectable()
export class PrototypesService {
  constructor(
    @InjectRepository(Prototype)
    private readonly prototypeRepository: Repository<Prototype>,
  ) {}

  /**
   * Create a new prototype
   */
  async create(createPrototypeDto: CreatePrototypeDto, userId: string): Promise<PrototypeResponseDto> {
    // Build metadata object
    const metadata: any = {};
    
    if (createPrototypeDto.figmaNodeIds) {
      metadata.figmaNodeIds = createPrototypeDto.figmaNodeIds;
    }
    
    if (createPrototypeDto.designSpecs) {
      metadata.designSpecs = createPrototypeDto.designSpecs;
    }
    
    if (createPrototypeDto.collaborators) {
      metadata.collaborators = createPrototypeDto.collaborators;
    }
    
    if (createPrototypeDto.tags) {
      metadata.tags = createPrototypeDto.tags;
    }
    
    if (createPrototypeDto.customProperties) {
      metadata.customProperties = createPrototypeDto.customProperties;
    }

    // Create prototype entity
    const prototype = this.prototypeRepository.create({
      name: createPrototypeDto.name,
      description: createPrototypeDto.description,
      type: createPrototypeDto.type,
      status: createPrototypeDto.status,
      projectId: createPrototypeDto.projectId,
      figmaFileId: createPrototypeDto.figmaFileId,
      figmaFileUrl: createPrototypeDto.figmaFileUrl,
      figmaEmbedUrl: createPrototypeDto.figmaEmbedUrl,
      figmaIntegrationStatus: createPrototypeDto.figmaFileId 
        ? FigmaIntegrationStatus.CONNECTED 
        : FigmaIntegrationStatus.NOT_CONNECTED,
      createdBy: userId,
      metadata,
    });

    const savedPrototype = await this.prototypeRepository.save(prototype);
    
    // Load with relations for response
    const prototypeWithRelations = await this.findOneWithRelations(savedPrototype.id);
    
    return this.toResponseDto(prototypeWithRelations);
  }

  /**
   * Find all prototypes with filtering and pagination
   */
  async findAll(query: PrototypeQueryDto): Promise<PaginatedResponseDto<PrototypeResponseDto>> {
    const {
      page = 1,
      limit = 20,
      projectId,
      type,
      status,
      figmaIntegrationStatus,
      createdBy,
      search,
      tags,
      createdAfter,
      createdBefore,
      updatedAfter,
      updatedBefore,
      sortBy = 'updatedAt',
      sortOrder = 'DESC',
      includeProject = true,
      includeCreator = true,
      includeDeleted = false,
    } = query;

    const queryBuilder = this.prototypeRepository.createQueryBuilder('prototype');

    // Include relations based on query parameters
    if (includeProject) {
      queryBuilder.leftJoinAndSelect('prototype.project', 'project');
    }
    
    if (includeCreator) {
      queryBuilder.leftJoinAndSelect('prototype.creator', 'creator');
      queryBuilder.leftJoinAndSelect('prototype.updater', 'updater');
    }

    // Apply filters
    if (projectId) {
      queryBuilder.andWhere('prototype.projectId = :projectId', { projectId });
    }

    if (type) {
      queryBuilder.andWhere('prototype.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('prototype.status = :status', { status });
    }

    if (figmaIntegrationStatus) {
      queryBuilder.andWhere('prototype.figmaIntegrationStatus = :figmaIntegrationStatus', { 
        figmaIntegrationStatus 
      });
    }

    if (createdBy) {
      queryBuilder.andWhere('prototype.createdBy = :createdBy', { createdBy });
    }

    if (search) {
      queryBuilder.andWhere(
        '(prototype.name ILIKE :search OR prototype.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere(
        'prototype.metadata->>\'tags\' ?| array[:tags]',
        { tags }
      );
    }

    // Date filters
    if (createdAfter) {
      queryBuilder.andWhere('prototype.createdAt >= :createdAfter', { 
        createdAfter: new Date(createdAfter) 
      });
    }

    if (createdBefore) {
      queryBuilder.andWhere('prototype.createdAt <= :createdBefore', { 
        createdBefore: new Date(createdBefore) 
      });
    }

    if (updatedAfter) {
      queryBuilder.andWhere('prototype.updatedAt >= :updatedAfter', { 
        updatedAfter: new Date(updatedAfter) 
      });
    }

    if (updatedBefore) {
      queryBuilder.andWhere('prototype.updatedAt <= :updatedBefore', { 
        updatedBefore: new Date(updatedBefore) 
      });
    }

    // Soft delete filter
    if (!includeDeleted) {
      queryBuilder.andWhere('prototype.deletedAt IS NULL');
    }

    // Sorting
    const validSortFields = ['name', 'createdAt', 'updatedAt', 'status', 'type'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'updatedAt';
    queryBuilder.orderBy(`prototype.${sortField}`, sortOrder);

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [prototypes, total] = await queryBuilder.getManyAndCount();

    // Convert to response DTOs
    const responseData = prototypes.map(prototype => this.toResponseDto(prototype));

    return PaginatedResponseDto.create(responseData, total, page, limit);
  }

  /**
   * Find one prototype by ID
   */
  async findOne(id: string): Promise<PrototypeResponseDto> {
    const prototype = await this.findOneWithRelations(id);
    
    if (!prototype) {
      throw new NotFoundException(`Prototype with ID ${id} not found`);
    }

    return this.toResponseDto(prototype);
  }

  /**
   * Update a prototype
   */
  async update(
    id: string, 
    updatePrototypeDto: UpdatePrototypeDto, 
    userId: string
  ): Promise<PrototypeResponseDto> {
    const prototype = await this.prototypeRepository.findOne({ 
      where: { id },
      relations: ['project', 'creator']
    });

    if (!prototype) {
      throw new NotFoundException(`Prototype with ID ${id} not found`);
    }

    // Update metadata if provided
    if (updatePrototypeDto.figmaNodeIds || 
        updatePrototypeDto.designSpecs || 
        updatePrototypeDto.collaborators || 
        updatePrototypeDto.tags || 
        updatePrototypeDto.customProperties) {
      
      const updatedMetadata = { ...prototype.metadata };
      
      if (updatePrototypeDto.figmaNodeIds !== undefined) {
        updatedMetadata.figmaNodeIds = updatePrototypeDto.figmaNodeIds;
      }
      
      if (updatePrototypeDto.designSpecs !== undefined) {
        updatedMetadata.designSpecs = updatePrototypeDto.designSpecs;
      }
      
      if (updatePrototypeDto.collaborators !== undefined) {
        updatedMetadata.collaborators = updatePrototypeDto.collaborators;
      }
      
      if (updatePrototypeDto.tags !== undefined) {
        updatedMetadata.tags = updatePrototypeDto.tags;
      }
      
      if (updatePrototypeDto.customProperties !== undefined) {
        updatedMetadata.customProperties = updatePrototypeDto.customProperties;
      }

      prototype.metadata = updatedMetadata;
    }

    // Update other fields
    Object.assign(prototype, {
      ...updatePrototypeDto,
      updatedBy: userId,
    });

    // Update Figma integration status if Figma fields changed
    if (updatePrototypeDto.figmaFileId !== undefined) {
      prototype.figmaIntegrationStatus = updatePrototypeDto.figmaFileId 
        ? FigmaIntegrationStatus.CONNECTED 
        : FigmaIntegrationStatus.NOT_CONNECTED;
    }

    const savedPrototype = await this.prototypeRepository.save(prototype);
    const prototypeWithRelations = await this.findOneWithRelations(savedPrototype.id);
    
    return this.toResponseDto(prototypeWithRelations);
  }

  /**
   * Soft delete a prototype
   */
  async remove(id: string, userId: string): Promise<void> {
    const prototype = await this.prototypeRepository.findOne({ where: { id } });

    if (!prototype) {
      throw new NotFoundException(`Prototype with ID ${id} not found`);
    }

    prototype.deletedAt = new Date();
    prototype.deletedBy = userId;

    await this.prototypeRepository.save(prototype);
  }

  /**
   * Find one prototype with all relations
   */
  private async findOneWithRelations(id: string): Promise<Prototype | null> {
    return this.prototypeRepository.findOne({
      where: { id },
      relations: ['project', 'creator', 'updater'],
    });
  }

  /**
   * Convert entity to response DTO
   */
  private toResponseDto(prototype: Prototype): PrototypeResponseDto {
    return plainToClass(PrototypeResponseDto, prototype, {
      excludeExtraneousValues: true,
    });
  }
}
