import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VisualFeedback } from '../entities/visual-feedback.entity';
import { 
  CreateBusinessFeedbackDto, 
  UpdateBusinessFeedbackDto, 
  BusinessFeedbackQueryDto,
  BusinessFeedbackResponseDto,
  BusinessFeedbackSummaryDto,
  PaginatedResponseDto,
  BusinessFeedbackStatus,
  BusinessFeedbackType,
  BusinessFeedbackPriority
} from '../dto';
import { plainToClass } from 'class-transformer';

/**
 * BusinessFeedbackService
 * 
 * Service for managing lightweight business feedback
 * Focused on structured client-service provider communication
 */
@Injectable()
export class BusinessFeedbackService {
  constructor(
    @InjectRepository(VisualFeedback)
    private readonly feedbackRepository: Repository<VisualFeedback>,
  ) {}

  /**
   * Create a new business feedback
   */
  async create(
    createFeedbackDto: CreateBusinessFeedbackDto, 
    userId: string
  ): Promise<BusinessFeedbackResponseDto> {
    // Build metadata object for business feedback
    const metadata: any = {
      // Business-specific metadata
      attachments: createFeedbackDto.attachments || [],
      relatedFeedbackIds: createFeedbackDto.relatedFeedbackIds || [],
      estimatedDelay: createFeedbackDto.estimatedDelay,
      resourceRequirement: createFeedbackDto.resourceRequirement,
      riskLevel: createFeedbackDto.riskLevel,
      customProperties: createFeedbackDto.customProperties,
      
      // Reference information
      figmaPageUrl: createFeedbackDto.figmaPageUrl,
      screenReference: createFeedbackDto.screenReference,
      
      // Business context
      workflowStepId: createFeedbackDto.workflowStepId,
      milestoneId: createFeedbackDto.milestoneId,
      
      // Simplified feedback model - no complex coordinates
      feedbackModel: 'business_lightweight',
      version: '1.0'
    };

    // Create feedback entity using existing VisualFeedback entity
    // but with business-focused data structure
    const feedback = this.feedbackRepository.create({
      // Map business feedback to existing entity structure
      content: createFeedbackDto.description,
      type: this.mapBusinessTypeToFeedbackType(createFeedbackDto.type),
      priority: this.mapBusinessPriorityToFeedbackPriority(createFeedbackDto.priority),
      status: 'open', // Default status
      prototypeId: createFeedbackDto.prototypeId,
      createdBy: userId,
      
      // Store business-specific data in metadata
      metadata: {
        ...metadata,
        businessTitle: createFeedbackDto.title,
        businessType: createFeedbackDto.type,
        businessPriority: createFeedbackDto.priority,
        businessStatus: BusinessFeedbackStatus.SUBMITTED,
        assignedTo: createFeedbackDto.assignedTo,
        dueDate: createFeedbackDto.dueDate,
      },
      
      // Set default coordinates (not used for business feedback)
      positionX: 0,
      positionY: 0,
    } as any);

    const savedFeedback = await this.feedbackRepository.save(feedback);

    // Auto-assign if specified
    if (createFeedbackDto.assignedTo) {
      await this.assignFeedback(savedFeedback.id, createFeedbackDto.assignedTo, userId);
    }

    // Load with relations for response
    const feedbackWithRelations = await this.findOneWithRelations(savedFeedback.id);
    
    return this.toBusinessResponseDto(feedbackWithRelations);
  }

  /**
   * Find all business feedback with filtering and pagination
   */
  async findAll(query: BusinessFeedbackQueryDto, currentUserId?: string): Promise<PaginatedResponseDto<BusinessFeedbackResponseDto>> {
    const {
      page = 1,
      limit = 20,
      prototypeId,
      submittedBy,
      assignedTo,
      workflowStepId,
      milestoneId,
      type,
      status,
      priority,
      search,
      screenReference,
      submittedAfter,
      submittedBefore,
      dueAfter,
      dueBefore,
      resolvedAfter,
      resolvedBefore,
      overdueOnly,
      unresolvedOnly,
      assignedToMeOnly,
      submittedByMeOnly,
      highPriorityOnly,
      blockingOnly,
      sortBy = 'submittedAt',
      sortOrder = 'DESC',
      includePrototype = true,
      includeSubmitter = true,
      includeAssignee = true,
      includeDeleted = false,
    } = query;

    const queryBuilder = this.feedbackRepository.createQueryBuilder('feedback');

    // Filter for business feedback only
    queryBuilder.andWhere('feedback.metadata->>\'feedbackModel\' = :model', { 
      model: 'business_lightweight' 
    });

    // Include relations based on query parameters
    if (includePrototype) {
      queryBuilder.leftJoinAndSelect('feedback.prototype', 'prototype');
    }
    
    if (includeSubmitter) {
      queryBuilder.leftJoinAndSelect('feedback.creator', 'creator');
    }

    // Apply filters
    if (prototypeId) {
      queryBuilder.andWhere('feedback.prototypeId = :prototypeId', { prototypeId });
    }

    if (submittedBy) {
      queryBuilder.andWhere('feedback.createdBy = :submittedBy', { submittedBy });
    }

    if (assignedTo) {
      queryBuilder.andWhere('feedback.metadata->>\'assignedTo\' = :assignedTo', { assignedTo });
    }

    if (workflowStepId) {
      queryBuilder.andWhere('feedback.metadata->>\'workflowStepId\' = :workflowStepId', { workflowStepId });
    }

    if (milestoneId) {
      queryBuilder.andWhere('feedback.metadata->>\'milestoneId\' = :milestoneId', { milestoneId });
    }

    if (type) {
      queryBuilder.andWhere('feedback.metadata->>\'businessType\' = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('feedback.metadata->>\'businessStatus\' = :status', { status });
    }

    if (priority) {
      queryBuilder.andWhere('feedback.metadata->>\'businessPriority\' = :priority', { priority });
    }

    if (search) {
      queryBuilder.andWhere(
        '(feedback.metadata->>\'businessTitle\' ILIKE :search OR feedback.content ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (screenReference) {
      queryBuilder.andWhere(
        'feedback.metadata->>\'screenReference\' ILIKE :screenReference',
        { screenReference: `%${screenReference}%` }
      );
    }

    // Date filters
    if (submittedAfter) {
      queryBuilder.andWhere('feedback.createdAt >= :submittedAfter', { 
        submittedAfter: new Date(submittedAfter) 
      });
    }

    if (submittedBefore) {
      queryBuilder.andWhere('feedback.createdAt <= :submittedBefore', { 
        submittedBefore: new Date(submittedBefore) 
      });
    }

    if (dueAfter) {
      queryBuilder.andWhere('feedback.metadata->>\'dueDate\' >= :dueAfter', { 
        dueAfter: new Date(dueAfter).toISOString() 
      });
    }

    if (dueBefore) {
      queryBuilder.andWhere('feedback.metadata->>\'dueDate\' <= :dueBefore', { 
        dueBefore: new Date(dueBefore).toISOString() 
      });
    }

    // Status-based filters
    if (overdueOnly) {
      queryBuilder.andWhere('feedback.metadata->>\'dueDate\' < :now', { 
        now: new Date().toISOString() 
      });
      queryBuilder.andWhere('feedback.metadata->>\'businessStatus\' NOT IN (:...resolvedStatuses)', {
        resolvedStatuses: [BusinessFeedbackStatus.RESOLVED, BusinessFeedbackStatus.APPROVED]
      });
    }

    if (unresolvedOnly) {
      queryBuilder.andWhere('feedback.metadata->>\'businessStatus\' NOT IN (:...resolvedStatuses)', {
        resolvedStatuses: [BusinessFeedbackStatus.RESOLVED, BusinessFeedbackStatus.APPROVED]
      });
    }

    if (assignedToMeOnly && currentUserId) {
      queryBuilder.andWhere('feedback.metadata->>\'assignedTo\' = :currentUserId', { currentUserId });
    }

    if (submittedByMeOnly && currentUserId) {
      queryBuilder.andWhere('feedback.createdBy = :currentUserId', { currentUserId });
    }

    if (highPriorityOnly) {
      queryBuilder.andWhere('feedback.metadata->>\'businessPriority\' = :highPriority', { 
        highPriority: BusinessFeedbackPriority.HIGH 
      });
    }

    if (blockingOnly) {
      queryBuilder.andWhere('feedback.metadata->>\'businessPriority\' = :blocking', { 
        blocking: BusinessFeedbackPriority.BLOCKING 
      });
    }

    // Soft delete filter
    if (!includeDeleted) {
      queryBuilder.andWhere('feedback.deletedAt IS NULL');
    }

    // Sorting
    const validSortFields = ['submittedAt', 'updatedAt', 'dueDate', 'priority', 'status', 'type', 'title'];
    let sortField = 'feedback.createdAt'; // Default to createdAt for submittedAt
    
    if (sortBy === 'title') {
      sortField = 'feedback.metadata->>\'businessTitle\'';
    } else if (sortBy === 'priority') {
      sortField = 'feedback.metadata->>\'businessPriority\'';
    } else if (sortBy === 'status') {
      sortField = 'feedback.metadata->>\'businessStatus\'';
    } else if (sortBy === 'type') {
      sortField = 'feedback.metadata->>\'businessType\'';
    } else if (sortBy === 'dueDate') {
      sortField = 'feedback.metadata->>\'dueDate\'';
    } else if (validSortFields.includes(sortBy)) {
      sortField = `feedback.${sortBy === 'submittedAt' ? 'createdAt' : sortBy}`;
    }
    
    queryBuilder.orderBy(sortField, sortOrder);

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [feedbacks, total] = await queryBuilder.getManyAndCount();

    // Convert to response DTOs
    const responseData = feedbacks.map(feedback => this.toBusinessResponseDto(feedback));

    return PaginatedResponseDto.create(responseData, total, page, limit);
  }

  /**
   * Find one business feedback by ID
   */
  async findOne(id: string): Promise<BusinessFeedbackResponseDto> {
    const feedback = await this.findOneWithRelations(id);
    
    if (!feedback) {
      throw new NotFoundException(`Business feedback with ID ${id} not found`);
    }

    // Verify it's a business feedback
    if (feedback.metadata?.feedbackModel !== 'business_lightweight') {
      throw new NotFoundException(`Business feedback with ID ${id} not found`);
    }

    return this.toBusinessResponseDto(feedback);
  }

  /**
   * Update a business feedback
   */
  async update(
    id: string, 
    updateFeedbackDto: UpdateBusinessFeedbackDto, 
    userId: string
  ): Promise<BusinessFeedbackResponseDto> {
    const feedback = await this.feedbackRepository.findOne({ 
      where: { id },
      relations: ['creator']
    });

    if (!feedback) {
      throw new NotFoundException(`Business feedback with ID ${id} not found`);
    }

    // Verify it's a business feedback
    if (feedback.metadata?.feedbackModel !== 'business_lightweight') {
      throw new NotFoundException(`Business feedback with ID ${id} not found`);
    }

    // Update metadata with new values
    const updatedMetadata = { ...feedback.metadata };
    
    if (updateFeedbackDto.title !== undefined) {
      updatedMetadata.businessTitle = updateFeedbackDto.title;
    }
    
    if (updateFeedbackDto.type !== undefined) {
      updatedMetadata.businessType = updateFeedbackDto.type;
      feedback.type = this.mapBusinessTypeToFeedbackType(updateFeedbackDto.type);
    }
    
    if (updateFeedbackDto.priority !== undefined) {
      updatedMetadata.businessPriority = updateFeedbackDto.priority;
      feedback.priority = this.mapBusinessPriorityToFeedbackPriority(updateFeedbackDto.priority);
    }
    
    if (updateFeedbackDto.status !== undefined) {
      updatedMetadata.businessStatus = updateFeedbackDto.status;
      feedback.status = this.mapBusinessStatusToFeedbackStatus(updateFeedbackDto.status);
    }

    if (updateFeedbackDto.assignedTo !== undefined) {
      updatedMetadata.assignedTo = updateFeedbackDto.assignedTo;
    }

    if (updateFeedbackDto.dueDate !== undefined) {
      updatedMetadata.dueDate = updateFeedbackDto.dueDate;
    }

    if (updateFeedbackDto.resolutionNotes !== undefined) {
      updatedMetadata.resolutionNotes = updateFeedbackDto.resolutionNotes;
    }

    if (updateFeedbackDto.resolvedAt !== undefined) {
      updatedMetadata.resolvedAt = updateFeedbackDto.resolvedAt;
      updatedMetadata.resolvedBy = userId;
    }

    // Update other fields
    if (updateFeedbackDto.description !== undefined) {
      feedback.content = updateFeedbackDto.description;
    }

    feedback.metadata = updatedMetadata;

    const savedFeedback = await this.feedbackRepository.save(feedback);
    const feedbackWithRelations = await this.findOneWithRelations(savedFeedback.id);
    
    return this.toBusinessResponseDto(feedbackWithRelations);
  }

  /**
   * Soft delete a business feedback
   */
  async remove(id: string, userId: string): Promise<void> {
    const feedback = await this.feedbackRepository.findOne({ 
      where: { id },
      relations: ['creator']
    });

    if (!feedback) {
      throw new NotFoundException(`Business feedback with ID ${id} not found`);
    }

    // Verify it's a business feedback
    if (feedback.metadata?.feedbackModel !== 'business_lightweight') {
      throw new NotFoundException(`Business feedback with ID ${id} not found`);
    }

    feedback.deletedAt = new Date();
    (feedback as any).deletedBy = userId;

    await this.feedbackRepository.save(feedback);
  }

  /**
   * Assign feedback to a user
   */
  async assignFeedback(id: string, assigneeId: string, userId: string): Promise<BusinessFeedbackResponseDto> {
    const feedback = await this.feedbackRepository.findOne({ where: { id } });

    if (!feedback) {
      throw new NotFoundException(`Business feedback with ID ${id} not found`);
    }

    const updatedMetadata = { ...feedback.metadata };
    updatedMetadata.assignedTo = assigneeId;
    updatedMetadata.assignedAt = new Date().toISOString();
    updatedMetadata.assignedBy = userId;

    feedback.metadata = updatedMetadata;

    await this.feedbackRepository.save(feedback);
    const feedbackWithRelations = await this.findOneWithRelations(id);
    
    return this.toBusinessResponseDto(feedbackWithRelations);
  }

  /**
   * Resolve feedback
   */
  async resolveFeedback(
    id: string, 
    resolutionNotes: string, 
    userId: string
  ): Promise<BusinessFeedbackResponseDto> {
    const updateDto: UpdateBusinessFeedbackDto = {
      status: BusinessFeedbackStatus.RESOLVED,
      resolutionNotes,
      resolvedAt: new Date().toISOString(),
    };

    return this.update(id, updateDto, userId);
  }

  /**
   * Get feedback statistics
   */
  async getStatistics(filters?: Partial<BusinessFeedbackQueryDto>): Promise<any> {
    const queryBuilder = this.feedbackRepository.createQueryBuilder('feedback');
    
    // Filter for business feedback only
    queryBuilder.andWhere('feedback.metadata->>\'feedbackModel\' = :model', { 
      model: 'business_lightweight' 
    });

    // Apply basic filters if provided
    if (filters?.prototypeId) {
      queryBuilder.andWhere('feedback.prototypeId = :prototypeId', { 
        prototypeId: filters.prototypeId 
      });
    }

    const [feedbacks] = await queryBuilder.getManyAndCount();

    // Calculate statistics
    const stats = {
      total: feedbacks.length,
      byType: {} as Record<string, number>,
      byStatus: {} as Record<string, number>,
      byPriority: {} as Record<string, number>,
      overdue: 0,
      resolved: 0,
      averageResolutionTime: 0,
    };

    const now = new Date();
    let totalResolutionTime = 0;
    let resolvedCount = 0;

    feedbacks.forEach(feedback => {
      const businessType = feedback.metadata?.businessType || 'unknown';
      const businessStatus = feedback.metadata?.businessStatus || 'unknown';
      const businessPriority = feedback.metadata?.businessPriority || 'unknown';

      stats.byType[businessType] = (stats.byType[businessType] || 0) + 1;
      stats.byStatus[businessStatus] = (stats.byStatus[businessStatus] || 0) + 1;
      stats.byPriority[businessPriority] = (stats.byPriority[businessPriority] || 0) + 1;

      // Check if overdue
      if (feedback.metadata?.dueDate && 
          new Date(feedback.metadata.dueDate) < now &&
          ![BusinessFeedbackStatus.RESOLVED, BusinessFeedbackStatus.APPROVED].includes(businessStatus)) {
        stats.overdue++;
      }

      // Calculate resolution time
      if (businessStatus === BusinessFeedbackStatus.RESOLVED && feedback.metadata?.resolvedAt) {
        stats.resolved++;
        resolvedCount++;
        const resolutionTime = new Date(feedback.metadata.resolvedAt).getTime() - 
                              new Date(feedback.createdAt).getTime();
        totalResolutionTime += resolutionTime;
      }
    });

    if (resolvedCount > 0) {
      stats.averageResolutionTime = Math.round(totalResolutionTime / resolvedCount / (1000 * 60 * 60 * 24)); // days
    }

    return stats;
  }

  /**
   * Find one feedback with all relations
   */
  private async findOneWithRelations(id: string): Promise<VisualFeedback | null> {
    return this.feedbackRepository.findOne({
      where: { id },
      relations: [
        'prototype', 
        'creator'
      ],
    });
  }

  /**
   * Convert entity to business response DTO
   */
  private toBusinessResponseDto(feedback: VisualFeedback): BusinessFeedbackResponseDto {
    const businessData = {
      id: feedback.id,
      title: feedback.metadata?.businessTitle || 'Untitled Feedback',
      description: feedback.content,
      type: feedback.metadata?.businessType || BusinessFeedbackType.CLARIFICATION_NEEDED,
      priority: feedback.metadata?.businessPriority || BusinessFeedbackPriority.MEDIUM,
      status: feedback.metadata?.businessStatus || BusinessFeedbackStatus.SUBMITTED,
      prototype: feedback.prototype,
      submittedBy: feedback.creator,
      assignedTo: feedback.metadata?.assignedTo ? { id: feedback.metadata.assignedTo } : undefined,
      resolvedBy: feedback.metadata?.resolvedBy ? { id: feedback.metadata.resolvedBy } : undefined,
      workflowStepId: feedback.metadata?.workflowStepId,
      milestoneId: feedback.metadata?.milestoneId,
      dueDate: feedback.metadata?.dueDate,
      figmaPageUrl: feedback.metadata?.figmaPageUrl,
      screenReference: feedback.metadata?.screenReference,
      resolutionNotes: feedback.metadata?.resolutionNotes,
      metadata: feedback.metadata || {},
      submittedAt: feedback.createdAt,
      updatedAt: feedback.updatedAt,
      resolvedAt: feedback.metadata?.resolvedAt ? new Date(feedback.metadata.resolvedAt) : undefined,
      deletedAt: feedback.deletedAt,
    };

    return plainToClass(BusinessFeedbackResponseDto, businessData, {
      excludeExtraneousValues: true,
    });
  }

  /**
   * Map business feedback type to existing feedback type
   */
  private mapBusinessTypeToFeedbackType(businessType: BusinessFeedbackType): string {
    const mapping = {
      [BusinessFeedbackType.APPROVAL_DECISION]: 'general',
      [BusinessFeedbackType.CHANGE_REQUEST]: 'issue',
      [BusinessFeedbackType.CLARIFICATION_NEEDED]: 'question',
      [BusinessFeedbackType.MILESTONE_FEEDBACK]: 'general',
    };
    return mapping[businessType] || 'general';
  }

  /**
   * Map business priority to existing priority
   */
  private mapBusinessPriorityToFeedbackPriority(businessPriority: BusinessFeedbackPriority): string {
    const mapping = {
      [BusinessFeedbackPriority.LOW]: 'low',
      [BusinessFeedbackPriority.MEDIUM]: 'medium',
      [BusinessFeedbackPriority.HIGH]: 'high',
      [BusinessFeedbackPriority.BLOCKING]: 'critical',
    };
    return mapping[businessPriority] || 'medium';
  }

  /**
   * Map business status to existing status
   */
  private mapBusinessStatusToFeedbackStatus(businessStatus: BusinessFeedbackStatus): string {
    const mapping = {
      [BusinessFeedbackStatus.SUBMITTED]: 'open',
      [BusinessFeedbackStatus.UNDER_REVIEW]: 'in_progress',
      [BusinessFeedbackStatus.APPROVED]: 'resolved',
      [BusinessFeedbackStatus.CHANGES_REQUESTED]: 'open',
      [BusinessFeedbackStatus.RESOLVED]: 'resolved',
    };
    return mapping[businessStatus] || 'open';
  }
}
