import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { ScheduleModule } from '@nestjs/schedule';
import { ConfigModule } from '@nestjs/config';

// Entities
import {
  ChangeRequest,
  ImpactAssessment,
  ApprovalWorkflow,
  ApprovalStep,
  ApprovalDecision,
  ChangeHistory,
} from './entities';

// Controllers
import {
  ChangeRequestController,
  ImpactAssessmentController,
  ApprovalWorkflowController,
  ChangeHistoryController,
} from './controllers';

// Services
import {
  ChangeRequestService,
  ImpactAssessmentService,
  ApprovalWorkflowService,
  AssessmentAlgorithmEngineService,
  ChangeNotificationService,
  WorkflowAutomationService,
  ChangeIntegrationService,
} from './services';

// External dependencies
import { UsersModule } from '../users/users.module';
import { ProjectsModule } from '../projects/projects.module';

/**
 * Change Management Module
 * Sprint 7: Change Request Management System
 * 
 * Comprehensive change management system with intelligent impact assessment,
 * automated workflows, notifications, and external system integrations
 */
@Module({
  imports: [
    // TypeORM entities registration
    TypeOrmModule.forFeature([
      ChangeRequest,
      ImpactAssessment,
      ApprovalWorkflow,
      ApprovalStep,
      ApprovalDecision,
      ChangeHistory,
    ]),

    // HTTP client for external integrations
    HttpModule.register({
      timeout: 10000,
      maxRedirects: 5,
    }),

    // Schedule module for automated tasks
    ScheduleModule.forRoot(),

    // Configuration module
    ConfigModule,

    // External module dependencies
    UsersModule,
    ProjectsModule,
  ],

  controllers: [
    ChangeRequestController,
    ImpactAssessmentController,
    ApprovalWorkflowController,
    ChangeHistoryController,
  ],

  providers: [
    // Core services
    ChangeRequestService,
    ImpactAssessmentService,
    ApprovalWorkflowService,

    // Advanced services
    AssessmentAlgorithmEngineService,
    ChangeNotificationService,
    WorkflowAutomationService,
    ChangeIntegrationService,

    // Service configuration providers
    {
      provide: 'CHANGE_MANAGEMENT_CONFIG',
      useFactory: () => ({
        // Algorithm engine configuration
        algorithmEngine: {
          version: '1.0.0',
          enableMachineLearning: true,
          enableHistoricalAnalysis: true,
          confidenceThreshold: 0.6,
          similarityThreshold: 0.7,
        },

        // Notification configuration
        notifications: {
          enableEmail: true,
          enableInApp: true,
          enableSlack: false,
          enableWebhooks: true,
          defaultChannels: ['email', 'in_app'],
          retryAttempts: 3,
          retryDelayMs: 1000,
        },

        // Automation configuration
        automation: {
          enableAutoApproval: true,
          enableAutoEscalation: true,
          enableAutoAssessment: true,
          autoApprovalThresholds: {
            maxCost: 1000,
            maxHours: 8,
            requiredConfidence: 0.8,
          },
          escalationThresholds: {
            overdueDays: 2,
            criticalPriorityHours: 4,
            highPriorityHours: 24,
          },
        },

        // Integration configuration
        integrations: {
          jira: {
            enabled: false,
            baseUrl: process.env.JIRA_BASE_URL || '',
            username: process.env.JIRA_USERNAME || '',
            apiToken: process.env.JIRA_API_TOKEN || '',
            projectKey: process.env.JIRA_PROJECT_KEY || '',
          },
          github: {
            enabled: false,
            token: process.env.GITHUB_TOKEN || '',
            organization: process.env.GITHUB_ORG || '',
            repository: process.env.GITHUB_REPO || '',
          },
          slack: {
            enabled: false,
            botToken: process.env.SLACK_BOT_TOKEN || '',
            channels: {
              notifications: process.env.SLACK_NOTIFICATIONS_CHANNEL || '#change-requests',
              approvals: process.env.SLACK_APPROVALS_CHANNEL || '#approvals',
              alerts: process.env.SLACK_ALERTS_CHANNEL || '#alerts',
            },
          },
        },

        // Performance and limits
        performance: {
          maxHistoricalDataPoints: 100,
          maxSimilarChanges: 10,
          assessmentTimeoutMs: 30000,
          notificationBatchSize: 50,
          automationBatchSize: 20,
        },

        // Feature flags
        features: {
          enableAdvancedAnalytics: true,
          enablePredictiveModeling: true,
          enableRealTimeNotifications: true,
          enableBulkOperations: true,
          enableExternalIntegrations: true,
          enableAuditTrail: true,
          enableVersionControl: true,
        },
      }),
    },

    // Database configuration provider
    {
      provide: 'DATABASE_CONFIG',
      useFactory: () => ({
        connectionPoolSize: 10,
        queryTimeout: 30000,
        enableLogging: process.env.NODE_ENV === 'development',
        enableSynchronize: false, // Always use migrations in production
      }),
    },

    // Cache configuration provider
    {
      provide: 'CACHE_CONFIG',
      useFactory: () => ({
        ttl: 300, // 5 minutes default TTL
        max: 1000, // Maximum number of items in cache
        enableCaching: process.env.NODE_ENV === 'production',
        cacheKeys: {
          changeRequest: 'change_request',
          assessment: 'impact_assessment',
          workflow: 'approval_workflow',
          analytics: 'analytics',
        },
      }),
    },

    // Logging configuration provider
    {
      provide: 'LOGGING_CONFIG',
      useFactory: () => ({
        level: process.env.LOG_LEVEL || 'info',
        enableFileLogging: process.env.ENABLE_FILE_LOGGING === 'true',
        enableConsoleLogging: true,
        logDirectory: process.env.LOG_DIRECTORY || './logs',
        maxFileSize: '10MB',
        maxFiles: 5,
        enableStructuredLogging: true,
        enablePerformanceLogging: true,
      }),
    },

    // Security configuration provider
    {
      provide: 'SECURITY_CONFIG',
      useFactory: () => ({
        enableRateLimiting: true,
        rateLimitWindow: 15 * 60 * 1000, // 15 minutes
        rateLimitMax: 100, // requests per window
        enableInputValidation: true,
        enableOutputSanitization: true,
        enableAuditLogging: true,
        sensitiveFields: [
          'password',
          'token',
          'apiKey',
          'secret',
        ],
      }),
    },
  ],

  exports: [
    // Export core services for use in other modules
    ChangeRequestService,
    ImpactAssessmentService,
    ApprovalWorkflowService,
    AssessmentAlgorithmEngineService,
    ChangeNotificationService,
    WorkflowAutomationService,
    ChangeIntegrationService,

    // Export TypeORM repositories for advanced use cases
    TypeOrmModule,

    // Export configuration providers
    'CHANGE_MANAGEMENT_CONFIG',
    'DATABASE_CONFIG',
    'CACHE_CONFIG',
    'LOGGING_CONFIG',
    'SECURITY_CONFIG',
  ],
})
export class ChangeManagementModule {
  constructor() {
    console.log('🔄 Change Management Module initialized');
    console.log('📊 Features enabled:');
    console.log('  ✅ Intelligent Impact Assessment');
    console.log('  ✅ Automated Workflow Processing');
    console.log('  ✅ Multi-channel Notifications');
    console.log('  ✅ External System Integrations');
    console.log('  ✅ Advanced Analytics & Reporting');
    console.log('  ✅ Audit Trail & History Tracking');
  }
}

/**
 * Change Management Module Configuration Interface
 * Defines the structure of the module configuration
 */
export interface ChangeManagementConfig {
  algorithmEngine: {
    version: string;
    enableMachineLearning: boolean;
    enableHistoricalAnalysis: boolean;
    confidenceThreshold: number;
    similarityThreshold: number;
  };

  notifications: {
    enableEmail: boolean;
    enableInApp: boolean;
    enableSlack: boolean;
    enableWebhooks: boolean;
    defaultChannels: string[];
    retryAttempts: number;
    retryDelayMs: number;
  };

  automation: {
    enableAutoApproval: boolean;
    enableAutoEscalation: boolean;
    enableAutoAssessment: boolean;
    autoApprovalThresholds: {
      maxCost: number;
      maxHours: number;
      requiredConfidence: number;
    };
    escalationThresholds: {
      overdueDays: number;
      criticalPriorityHours: number;
      highPriorityHours: number;
    };
  };

  integrations: {
    jira: {
      enabled: boolean;
      baseUrl: string;
      username: string;
      apiToken: string;
      projectKey: string;
    };
    github: {
      enabled: boolean;
      token: string;
      organization: string;
      repository: string;
    };
    slack: {
      enabled: boolean;
      botToken: string;
      channels: {
        notifications: string;
        approvals: string;
        alerts: string;
      };
    };
  };

  performance: {
    maxHistoricalDataPoints: number;
    maxSimilarChanges: number;
    assessmentTimeoutMs: number;
    notificationBatchSize: number;
    automationBatchSize: number;
  };

  features: {
    enableAdvancedAnalytics: boolean;
    enablePredictiveModeling: boolean;
    enableRealTimeNotifications: boolean;
    enableBulkOperations: boolean;
    enableExternalIntegrations: boolean;
    enableAuditTrail: boolean;
    enableVersionControl: boolean;
  };
}
