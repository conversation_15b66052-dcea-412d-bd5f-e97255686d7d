# Change Management System

## Overview

The Change Management System is a comprehensive solution for managing change requests in software development projects. It provides intelligent impact assessment, automated workflow processing, multi-channel notifications, and external system integrations.

## Features

### 🔄 Core Change Management
- **Change Request Lifecycle**: Complete CRUD operations with status management
- **Impact Assessment**: Intelligent AI-powered impact analysis
- **Approval Workflows**: Flexible workflow engine with multiple approval types
- **Audit Trail**: Complete history tracking and change logging

### 🤖 Intelligent Automation
- **Algorithm Engine**: Machine learning-powered impact assessment
- **Automated Workflows**: Rule-based automation for approvals and escalations
- **Smart Notifications**: Multi-channel notification system
- **Predictive Analytics**: Historical data analysis and trend prediction

### 🔗 External Integrations
- **Jira Integration**: Bi-directional sync with <PERSON>ra issues
- **GitHub Integration**: Issue creation and status synchronization
- **Slack Integration**: Real-time notifications and updates
- **Webhook Support**: Custom webhook endpoints for third-party systems

### 📊 Advanced Analytics
- **Performance Metrics**: Workflow efficiency and bottleneck analysis
- **Impact Trends**: Historical impact analysis and patterns
- **Risk Assessment**: Comprehensive risk evaluation and mitigation
- **Custom Reports**: Flexible reporting and data export

## Architecture

### Module Structure
```
change-management/
├── controllers/          # API endpoints
├── services/            # Business logic
├── entities/            # Database models
├── dto/                 # Data transfer objects
├── enums/               # Type definitions
├── __tests__/           # Integration tests
└── README.md           # This file
```

### Core Components

#### 1. Change Request Service
Handles the complete lifecycle of change requests including creation, updates, status management, and approval processing.

#### 2. Impact Assessment Service
Provides intelligent impact assessment using machine learning algorithms and historical data analysis.

#### 3. Approval Workflow Service
Manages complex approval workflows with support for sequential, parallel, and conditional approvals.

#### 4. Algorithm Engine Service
Advanced AI-powered assessment engine that analyzes historical data and predicts impact across multiple dimensions.

#### 5. Notification Service
Multi-channel notification system supporting email, in-app, Slack, and webhook notifications.

#### 6. Automation Service
Rule-based automation engine for handling routine tasks and escalations.

#### 7. Integration Service
External system integration layer supporting Jira, GitHub, Slack, and custom webhooks.

## API Endpoints

### Change Requests
- `POST /change-requests` - Create new change request
- `GET /change-requests` - List change requests with filtering
- `GET /change-requests/:id` - Get specific change request
- `PUT /change-requests/:id` - Update change request
- `PUT /change-requests/:id/status` - Update status
- `PUT /change-requests/:id/assign` - Assign to user
- `PUT /change-requests/:id/approve` - Approve change request
- `PUT /change-requests/:id/reject` - Reject change request
- `DELETE /change-requests/:id` - Delete change request

### Impact Assessments
- `POST /impact-assessments` - Create assessment
- `POST /impact-assessments/automated` - Trigger automated assessment
- `POST /impact-assessments/manual` - Create manual assessment
- `GET /impact-assessments/:id` - Get assessment details
- `GET /impact-assessments/change-request/:id` - Get by change request
- `PUT /impact-assessments/:id/complete` - Complete assessment

### Approval Workflows
- `POST /approval-workflows` - Create workflow
- `POST /approval-workflows/quick` - Create quick workflow
- `GET /approval-workflows/:id` - Get workflow details
- `PUT /approval-workflows/:id/start` - Start workflow
- `PUT /approval-workflows/:id/cancel` - Cancel workflow
- `POST /approval-workflows/:workflowId/steps/:stepId/decision` - Process decision

### Change History
- `GET /change-history/change-request/:id` - Get change history
- `GET /change-history/change-request/:id/timeline` - Get timeline view
- `GET /change-history/change-request/:id/comparison` - Compare versions
- `POST /change-history/change-request/:id/export` - Export history

## Configuration

### Environment Variables
```bash
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/stria

# Jira Integration
JIRA_ENABLED=false
JIRA_BASE_URL=https://your-domain.atlassian.net
JIRA_USERNAME=your-username
JIRA_API_TOKEN=your-api-token
JIRA_PROJECT_KEY=PROJ

# GitHub Integration
GITHUB_ENABLED=false
GITHUB_TOKEN=your-github-token
GITHUB_ORG=your-organization
GITHUB_REPO=your-repository

# Slack Integration
SLACK_ENABLED=false
SLACK_BOT_TOKEN=xoxb-your-bot-token
SLACK_NOTIFICATIONS_CHANNEL=#change-requests
SLACK_APPROVALS_CHANNEL=#approvals
SLACK_ALERTS_CHANNEL=#alerts

# Algorithm Engine
ALGORITHM_ENGINE_ENABLED=true
ML_CONFIDENCE_THRESHOLD=0.6
SIMILARITY_THRESHOLD=0.7

# Automation
AUTO_APPROVAL_ENABLED=true
AUTO_ESCALATION_ENABLED=true
AUTO_ASSESSMENT_ENABLED=true
```

### Module Configuration
The module can be configured through the `CHANGE_MANAGEMENT_CONFIG` provider:

```typescript
{
  algorithmEngine: {
    version: '1.0.0',
    enableMachineLearning: true,
    enableHistoricalAnalysis: true,
    confidenceThreshold: 0.6,
    similarityThreshold: 0.7,
  },
  notifications: {
    enableEmail: true,
    enableInApp: true,
    enableSlack: false,
    enableWebhooks: true,
    defaultChannels: ['email', 'in_app'],
    retryAttempts: 3,
  },
  automation: {
    enableAutoApproval: true,
    enableAutoEscalation: true,
    enableAutoAssessment: true,
    autoApprovalThresholds: {
      maxCost: 1000,
      maxHours: 8,
      requiredConfidence: 0.8,
    },
  },
  // ... more configuration options
}
```

## Usage Examples

### Creating a Change Request
```typescript
const changeRequest = await changeRequestService.create({
  title: 'Add User Dashboard',
  description: 'Implement comprehensive user dashboard',
  type: ChangeRequestType.FEATURE_ADDITION,
  priority: ChangeRequestPriority.HIGH,
  complexity: ChangeRequestComplexity.MAJOR,
  projectId: 'project-123',
  impactAreas: [
    ChangeRequestImpactArea.FRONTEND,
    ChangeRequestImpactArea.BACKEND,
  ],
  businessJustification: 'Improve user experience',
  technicalDetails: 'React dashboard with real-time data',
  estimatedCost: 15000,
  estimatedHours: 120,
}, userId);
```

### Triggering Automated Assessment
```typescript
const assessment = await impactAssessmentService.createAutomatedAssessment({
  changeRequestId: 'change-123',
  useHistoricalData: true,
  useMachineLearning: true,
  includeRiskAnalysis: true,
  confidenceThreshold: 0.7,
}, userId);
```

### Creating Approval Workflow
```typescript
const workflow = await approvalWorkflowService.createQuickWorkflow({
  changeRequestId: 'change-123',
  workflowType: WorkflowType.SEQUENTIAL_APPROVAL,
  approverIds: ['user-1', 'user-2'],
  priority: WorkflowPriority.HIGH,
  requireAllApprovals: true,
  enableEscalation: true,
}, userId);
```

## Testing

### Running Tests
```bash
# Unit tests
npm run test

# Integration tests
npm run test:e2e

# Test coverage
npm run test:cov

# Specific test file
npm run test change-request.service.spec.ts
```

### Test Structure
- **Unit Tests**: Located in `services/__tests__/` directory
- **Integration Tests**: Located in `__tests__/` directory
- **Mock Data**: Comprehensive mock data for all entities
- **Test Coverage**: Aim for >90% code coverage

## Performance Considerations

### Optimization Strategies
1. **Database Indexing**: Proper indexes on frequently queried fields
2. **Query Optimization**: Efficient database queries with proper joins
3. **Caching**: Redis caching for frequently accessed data
4. **Batch Processing**: Bulk operations for large datasets
5. **Async Processing**: Background jobs for heavy computations

### Monitoring
- **Performance Metrics**: Response times and throughput
- **Error Tracking**: Comprehensive error logging and monitoring
- **Resource Usage**: Memory and CPU utilization tracking
- **Business Metrics**: Change request processing times and success rates

## Security

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (RBAC)
- Resource-level permissions
- Audit logging for all operations

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Sensitive data encryption

## Deployment

### Docker Support
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "start:prod"]
```

### Health Checks
- `/health` - Basic health check
- `/health/detailed` - Detailed system status
- Database connectivity checks
- External service availability checks

## Contributing

### Development Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run database migrations: `npm run migration:run`
5. Start development server: `npm run start:dev`

### Code Standards
- TypeScript strict mode
- ESLint + Prettier formatting
- Comprehensive unit tests
- API documentation with Swagger
- Git conventional commits

## Support

For questions, issues, or contributions, please refer to:
- **Documentation**: `/docs` directory
- **API Reference**: Swagger UI at `/api/docs`
- **Issue Tracking**: GitHub Issues
- **Code Review**: Pull Request process

## License

This project is licensed under the MIT License - see the LICENSE file for details.
