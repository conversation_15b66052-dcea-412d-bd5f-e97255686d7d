import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ApprovalWorkflowService } from '../services/approval-workflow.service';
import {
  CreateApprovalWorkflowDto,
  QuickApprovalWorkflowDto,
  WorkflowTemplateDto,
  BulkWorkflowCreationDto,
  ConditionalWorkflowDto,
  ApprovalWorkflowResponseDto,
  WorkflowListResponseDto,
  WorkflowSummaryDto,
  WorkflowAnalyticsDto,
} from '../dto/approval-workflow';
import { 
  WorkflowStatus, 
  WorkflowType, 
  WorkflowPriority, 
  ApprovalDecision as ApprovalDecisionEnum 
} from '../enums';

/**
 * Approval Workflow Controller
 * Sprint 7: Change Request Management System
 * 
 * RESTful API endpoints for approval workflow operations
 * Handles workflow creation, management, decision processing, and analytics
 */
@ApiTags('Approval Workflows')
@Controller('approval-workflows')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ApprovalWorkflowController {
  constructor(private readonly approvalWorkflowService: ApprovalWorkflowService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new approval workflow',
    description: 'Creates a new structured approval workflow for a change request',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Approval workflow created successfully',
    type: ApprovalWorkflowResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid workflow data',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Approval workflow already exists for this change request',
  })
  @ApiBody({ type: CreateApprovalWorkflowDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createWorkflow(
    @Body() createDto: CreateApprovalWorkflowDto,
    @Request() req: any,
  ): Promise<ApprovalWorkflowResponseDto> {
    const userId = req.user.id;
    return this.approvalWorkflowService.create(createDto, userId);
  }

  @Post('quick')
  @ApiOperation({
    summary: 'Create quick approval workflow',
    description: 'Creates a workflow using predefined templates for common scenarios',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Quick workflow created successfully',
    type: ApprovalWorkflowResponseDto,
  })
  @ApiBody({ type: QuickApprovalWorkflowDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createQuickWorkflow(
    @Body() quickDto: QuickApprovalWorkflowDto,
    @Request() req: any,
  ): Promise<ApprovalWorkflowResponseDto> {
    const userId = req.user.id;
    return this.approvalWorkflowService.createQuickWorkflow(quickDto, userId);
  }

  @Post('template')
  @ApiOperation({
    summary: 'Create workflow from template',
    description: 'Creates a workflow based on a predefined template',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Workflow created from template successfully',
    type: ApprovalWorkflowResponseDto,
  })
  @ApiBody({ type: WorkflowTemplateDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createFromTemplate(
    @Body() templateDto: WorkflowTemplateDto,
    @Request() req: any,
  ): Promise<ApprovalWorkflowResponseDto> {
    const userId = req.user.id;
    // TODO: Implement template-based workflow creation
    throw new Error('Template-based workflow creation not yet implemented');
  }

  @Post('bulk')
  @ApiOperation({
    summary: 'Bulk create workflows',
    description: 'Creates workflows for multiple change requests',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Workflows created successfully',
    type: WorkflowListResponseDto,
  })
  @ApiBody({ type: BulkWorkflowCreationDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async bulkCreate(
    @Body() bulkDto: BulkWorkflowCreationDto,
    @Request() req: any,
  ): Promise<WorkflowListResponseDto> {
    const userId = req.user.id;
    // TODO: Implement bulk workflow creation
    throw new Error('Bulk workflow creation not yet implemented');
  }

  @Post('conditional')
  @ApiOperation({
    summary: 'Create conditional workflow',
    description: 'Creates a workflow with conditional logic and dynamic steps',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Conditional workflow created successfully',
    type: ApprovalWorkflowResponseDto,
  })
  @ApiBody({ type: ConditionalWorkflowDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createConditionalWorkflow(
    @Body() conditionalDto: ConditionalWorkflowDto,
    @Request() req: any,
  ): Promise<ApprovalWorkflowResponseDto> {
    const userId = req.user.id;
    // TODO: Implement conditional workflow creation
    throw new Error('Conditional workflow creation not yet implemented');
  }

  @Get()
  @ApiOperation({
    summary: 'Get all approval workflows',
    description: 'Retrieves a paginated list of approval workflows with filtering options',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflows retrieved successfully',
    type: WorkflowListResponseDto,
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'status', required: false, enum: WorkflowStatus, description: 'Filter by status' })
  @ApiQuery({ name: 'workflowType', required: false, enum: WorkflowType, description: 'Filter by type' })
  @ApiQuery({ name: 'priority', required: false, enum: WorkflowPriority, description: 'Filter by priority' })
  @ApiQuery({ name: 'assignedToId', required: false, type: String, description: 'Filter by current approver' })
  @ApiQuery({ name: 'createdById', required: false, type: String, description: 'Filter by creator' })
  @ApiQuery({ name: 'changeRequestId', required: false, type: String, description: 'Filter by change request' })
  @ApiQuery({ name: 'overdue', required: false, type: Boolean, description: 'Filter overdue workflows' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field (default: createdAt)' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order (default: DESC)' })
  async findAll(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: WorkflowStatus,
    @Query('workflowType') workflowType?: WorkflowType,
    @Query('priority') priority?: WorkflowPriority,
    @Query('assignedToId') assignedToId?: string,
    @Query('createdById') createdById?: string,
    @Query('changeRequestId') changeRequestId?: string,
    @Query('overdue') overdue?: boolean,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ): Promise<WorkflowListResponseDto> {
    const userId = req.user.id;
    const options = {
      page,
      limit,
      status,
      workflowType,
      priority,
      assignedToId,
      createdById,
      changeRequestId,
      overdue,
      sortBy,
      sortOrder,
    };
    return this.approvalWorkflowService.findAll(userId, options);
  }

  @Get('analytics')
  @ApiOperation({
    summary: 'Get workflow analytics',
    description: 'Retrieves analytics and performance metrics for approval workflows',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Analytics retrieved successfully',
    type: WorkflowAnalyticsDto,
  })
  @ApiQuery({ name: 'dateFrom', required: false, type: String, description: 'Filter from date (ISO string)' })
  @ApiQuery({ name: 'dateTo', required: false, type: String, description: 'Filter to date (ISO string)' })
  @ApiQuery({ name: 'workflowType', required: false, enum: WorkflowType, description: 'Filter by type' })
  async getAnalytics(
    @Request() req: any,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
    @Query('workflowType') workflowType?: WorkflowType,
  ): Promise<WorkflowAnalyticsDto> {
    const userId = req.user.id;
    const options = { dateFrom, dateTo, workflowType };
    // TODO: Implement workflow analytics
    throw new Error('Workflow analytics not yet implemented');
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get workflow by ID',
    description: 'Retrieves a specific approval workflow with full details including steps and decisions',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow retrieved successfully',
    type: ApprovalWorkflowResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Workflow not found',
  })
  @ApiParam({ name: 'id', type: String, description: 'Workflow ID' })
  async findById(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<ApprovalWorkflowResponseDto> {
    const userId = req.user.id;
    return this.approvalWorkflowService.findById(id, userId);
  }

  @Put(':id/start')
  @ApiOperation({
    summary: 'Start workflow',
    description: 'Starts an approval workflow and activates the first step',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow started successfully',
    type: ApprovalWorkflowResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Workflow can only be started from draft status',
  })
  @ApiParam({ name: 'id', type: String, description: 'Workflow ID' })
  async startWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<ApprovalWorkflowResponseDto> {
    const userId = req.user.id;
    return this.approvalWorkflowService.startWorkflow(id, userId);
  }

  @Put(':id/cancel')
  @ApiOperation({
    summary: 'Cancel workflow',
    description: 'Cancels an active workflow with optional reason',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow cancelled successfully',
    type: ApprovalWorkflowResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Workflow is already completed or cancelled',
  })
  @ApiParam({ name: 'id', type: String, description: 'Workflow ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        reason: { type: 'string', description: 'Cancellation reason' },
      },
    },
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async cancelWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { reason?: string },
    @Request() req: any,
  ): Promise<ApprovalWorkflowResponseDto> {
    const userId = req.user.id;
    return this.approvalWorkflowService.cancelWorkflow(id, userId, body.reason);
  }

  @Post(':workflowId/steps/:stepId/decision')
  @ApiOperation({
    summary: 'Process approval decision',
    description: 'Processes an approval decision for a specific workflow step',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Decision processed successfully',
    type: ApprovalWorkflowResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Step is not in a state that allows decisions',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'User is not authorized to make decisions for this step',
  })
  @ApiParam({ name: 'workflowId', type: String, description: 'Workflow ID' })
  @ApiParam({ name: 'stepId', type: String, description: 'Step ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        decision: { 
          type: 'string', 
          enum: Object.values(ApprovalDecisionEnum),
          description: 'Approval decision' 
        },
        comments: { type: 'string', description: 'Decision comments' },
        conditions: { type: 'string', description: 'Approval conditions' },
      },
      required: ['decision'],
    },
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async processDecision(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Param('stepId', ParseUUIDPipe) stepId: string,
    @Body() decisionData: {
      decision: ApprovalDecisionEnum;
      comments?: string;
      conditions?: string;
    },
    @Request() req: any,
  ): Promise<ApprovalWorkflowResponseDto> {
    const userId = req.user.id;
    return this.approvalWorkflowService.processDecision(
      workflowId,
      stepId,
      decisionData.decision,
      userId,
      decisionData.comments,
      decisionData.conditions,
    );
  }

  @Get(':id/summary')
  @ApiOperation({
    summary: 'Get workflow summary',
    description: 'Retrieves a summary view of the workflow for dashboard display',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow summary retrieved successfully',
    type: WorkflowSummaryDto,
  })
  @ApiParam({ name: 'id', type: String, description: 'Workflow ID' })
  async getSummary(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<WorkflowSummaryDto> {
    const userId = req.user.id;
    const workflow = await this.approvalWorkflowService.findById(id, userId);
    
    // Transform to summary DTO
    return {
      id: workflow.id,
      changeRequestId: workflow.changeRequestId,
      name: workflow.name,
      workflowType: workflow.workflowType,
      status: workflow.status,
      priority: workflow.priority,
      currentStepOrder: workflow.currentStepOrder,
      totalSteps: workflow.totalSteps,
      completedSteps: workflow.completedSteps,
      currentApproverId: workflow.currentApproverId,
      deadline: workflow.deadline,
      createdAt: workflow.createdAt,
      progressPercentage: workflow.progressPercentage,
      isOverdue: workflow.isOverdue,
    };
  }

  @Get(':id/history')
  @ApiOperation({
    summary: 'Get workflow history',
    description: 'Retrieves the complete history of decisions and actions for a workflow',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow history retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        workflowId: { type: 'string' },
        history: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              timestamp: { type: 'string' },
              action: { type: 'string' },
              actor: { type: 'string' },
              stepName: { type: 'string' },
              decision: { type: 'string' },
              comments: { type: 'string' },
            },
          },
        },
      },
    },
  })
  @ApiParam({ name: 'id', type: String, description: 'Workflow ID' })
  async getHistory(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<any> {
    const userId = req.user.id;
    // TODO: Implement workflow history retrieval
    throw new Error('Workflow history not yet implemented');
  }
}
