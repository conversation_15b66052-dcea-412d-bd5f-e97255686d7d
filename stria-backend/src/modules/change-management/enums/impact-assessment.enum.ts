/**
 * Impact Assessment Enums
 * Sprint 7: Change Request Management System
 * 
 * Defines enums for intelligent impact assessment functionality
 */

/**
 * Impact Assessment Status
 */
export enum ImpactAssessmentStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REQUIRES_REVIEW = 'requires_review',
}

export const DEFAULT_IMPACT_ASSESSMENT_STATUS = ImpactAssessmentStatus.PENDING;

/**
 * Impact Assessment Method - how the assessment was conducted
 */
export enum ImpactAssessmentMethod {
  AUTOMATED = 'automated',
  MANUAL = 'manual',
  EXPERT_MANUAL = 'expert_manual',
  HYBRID = 'hybrid',
  EXPERT_REVIEW = 'expert_review',
  HISTORICAL_DATA = 'historical_data',
  MACHINE_LEARNING = 'machine_learning',
}

export const DEFAULT_IMPACT_ASSESSMENT_METHOD = ImpactAssessmentMethod.AUTOMATED;

/**
 * Impact Level - overall impact magnitude
 */
export enum ImpactLevel {
  MINIMAL = 'minimal',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  SEVERE = 'severe',
}

export const DEFAULT_IMPACT_LEVEL = ImpactLevel.MEDIUM;

/**
 * Time Impact Categories
 */
export enum TimeImpactCategory {
  NO_IMPACT = 'no_impact',
  MINOR_DELAY = 'minor_delay',
  MODERATE_DELAY = 'moderate_delay',
  SIGNIFICANT_DELAY = 'significant_delay',
  MAJOR_DELAY = 'major_delay',
  CRITICAL_DELAY = 'critical_delay',
}

export const DEFAULT_TIME_IMPACT_CATEGORY = TimeImpactCategory.MINOR_DELAY;

/**
 * Cost Impact Categories
 */
export enum CostImpactCategory {
  NO_COST = 'no_cost',
  MINIMAL_COST = 'minimal_cost',
  LOW_COST = 'low_cost',
  MODERATE_COST = 'moderate_cost',
  HIGH_COST = 'high_cost',
  VERY_HIGH_COST = 'very_high_cost',
}

export const DEFAULT_COST_IMPACT_CATEGORY = CostImpactCategory.MODERATE_COST;

/**
 * Resource Impact Types
 */
export enum ResourceImpactType {
  DEVELOPER_TIME = 'developer_time',
  DESIGNER_TIME = 'designer_time',
  PROJECT_MANAGER_TIME = 'project_manager_time',
  QA_TIME = 'qa_time',
  DEVOPS_TIME = 'devops_time',
  EXTERNAL_CONSULTANT = 'external_consultant',
  THIRD_PARTY_SERVICES = 'third_party_services',
  INFRASTRUCTURE = 'infrastructure',
  TOOLS_AND_SOFTWARE = 'tools_and_software',
  TRAINING = 'training',
}

/**
 * Risk Impact Categories
 */
export enum RiskImpactCategory {
  NO_RISK = 'no_risk',
  LOW_RISK = 'low_risk',
  MEDIUM_RISK = 'medium_risk',
  HIGH_RISK = 'high_risk',
  CRITICAL_RISK = 'critical_risk',
}

export const DEFAULT_RISK_IMPACT_CATEGORY = RiskImpactCategory.MEDIUM_RISK;

/**
 * Technical Risk Types
 */
export enum TechnicalRiskType {
  COMPATIBILITY_ISSUES = 'compatibility_issues',
  PERFORMANCE_DEGRADATION = 'performance_degradation',
  SECURITY_VULNERABILITIES = 'security_vulnerabilities',
  DATA_INTEGRITY = 'data_integrity',
  SYSTEM_STABILITY = 'system_stability',
  INTEGRATION_FAILURES = 'integration_failures',
  SCALABILITY_CONCERNS = 'scalability_concerns',
  MAINTENANCE_COMPLEXITY = 'maintenance_complexity',
  TECHNICAL_DEBT = 'technical_debt',
  DEPENDENCY_CONFLICTS = 'dependency_conflicts',
}

/**
 * Business Risk Types
 */
export enum BusinessRiskType {
  TIMELINE_DELAY = 'timeline_delay',
  BUDGET_OVERRUN = 'budget_overrun',
  SCOPE_CREEP = 'scope_creep',
  STAKEHOLDER_DISSATISFACTION = 'stakeholder_dissatisfaction',
  MARKET_OPPORTUNITY_LOSS = 'market_opportunity_loss',
  COMPETITIVE_DISADVANTAGE = 'competitive_disadvantage',
  REGULATORY_COMPLIANCE = 'regulatory_compliance',
  REPUTATION_DAMAGE = 'reputation_damage',
  RESOURCE_UNAVAILABILITY = 'resource_unavailability',
  CLIENT_RELATIONSHIP = 'client_relationship',
}

/**
 * Confidence Level - how confident we are in the assessment
 */
export enum ConfidenceLevel {
  VERY_LOW = 'very_low',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  VERY_HIGH = 'very_high',
}

export const DEFAULT_CONFIDENCE_LEVEL = ConfidenceLevel.MEDIUM;

/**
 * Assessment Quality Indicators
 */
export enum AssessmentQuality {
  POOR = 'poor',
  FAIR = 'fair',
  GOOD = 'good',
  EXCELLENT = 'excellent',
}

export const DEFAULT_ASSESSMENT_QUALITY = AssessmentQuality.GOOD;

/**
 * Impact Assessment descriptions for documentation and UI display
 */
export const IMPACT_ASSESSMENT_STATUS_DESCRIPTIONS = {
  [ImpactAssessmentStatus.PENDING]: 'Assessment is queued for processing',
  [ImpactAssessmentStatus.IN_PROGRESS]: 'Assessment is currently being conducted',
  [ImpactAssessmentStatus.COMPLETED]: 'Assessment has been completed successfully',
  [ImpactAssessmentStatus.FAILED]: 'Assessment failed due to errors',
  [ImpactAssessmentStatus.CANCELLED]: 'Assessment was cancelled',
  [ImpactAssessmentStatus.REQUIRES_REVIEW]: 'Assessment completed but requires manual review',
} as const;

export const IMPACT_LEVEL_DESCRIPTIONS = {
  [ImpactLevel.MINIMAL]: 'Minimal impact on project timeline and resources',
  [ImpactLevel.LOW]: 'Low impact with minor adjustments needed',
  [ImpactLevel.MEDIUM]: 'Medium impact requiring moderate resource allocation',
  [ImpactLevel.HIGH]: 'High impact with significant resource requirements',
  [ImpactLevel.SEVERE]: 'Severe impact requiring major project adjustments',
} as const;

export const TIME_IMPACT_DESCRIPTIONS = {
  [TimeImpactCategory.NO_IMPACT]: 'No impact on project timeline',
  [TimeImpactCategory.MINOR_DELAY]: 'Minor delay of 1-3 days',
  [TimeImpactCategory.MODERATE_DELAY]: 'Moderate delay of 1-2 weeks',
  [TimeImpactCategory.SIGNIFICANT_DELAY]: 'Significant delay of 2-4 weeks',
  [TimeImpactCategory.MAJOR_DELAY]: 'Major delay of 1-2 months',
  [TimeImpactCategory.CRITICAL_DELAY]: 'Critical delay of more than 2 months',
} as const;

export const COST_IMPACT_DESCRIPTIONS = {
  [CostImpactCategory.NO_COST]: 'No additional cost',
  [CostImpactCategory.MINIMAL_COST]: 'Minimal cost increase (< $1,000)',
  [CostImpactCategory.LOW_COST]: 'Low cost increase ($1,000 - $5,000)',
  [CostImpactCategory.MODERATE_COST]: 'Moderate cost increase ($5,000 - $15,000)',
  [CostImpactCategory.HIGH_COST]: 'High cost increase ($15,000 - $50,000)',
  [CostImpactCategory.VERY_HIGH_COST]: 'Very high cost increase (> $50,000)',
} as const;
