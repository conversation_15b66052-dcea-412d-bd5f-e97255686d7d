export enum AutomationTrigger {
  CHANGE_REQUEST_CREATED = 'change_request_created',
  CHANGE_REQUEST_UPDATED = 'change_request_updated',
  IMPACT_ASSESSMENT_COMPLETED = 'impact_assessment_completed',
  WORKFLOW_STARTED = 'workflow_started',
  WORKFLOW_OVERDUE = 'workflow_overdue',
  APPROVAL_REQUIRED = 'approval_required',
  APPROVAL_COMPLETED = 'approval_completed',
  STATUS_CHANGED = 'status_changed',
}

export enum AutomationAction {
  TRIGGER_IMPACT_ASSESSMENT = 'trigger_impact_assessment',
  AUTO_APPROVE = 'auto_approve',
  AUTO_REJECT = 'auto_reject',
  ESCALATE_WORKFLOW = 'escalate_workflow',
  SEND_NOTIFICATION = 'send_notification',
  UPDATE_STATUS = 'update_status',
  ASSIGN_USER = 'assign_user',
  CREATE_WORKFLOW = 'create_workflow',
  CANCEL_WORKFLOW = 'cancel_workflow',
}

export enum AutomationCondition {
  IMPACT_LEVEL_EQUALS = 'impact_level_equals',
  IMPACT_LEVEL_GREATER_THAN = 'impact_level_greater_than',
  CONFIDENCE_LEVEL_EQUALS = 'confidence_level_equals',
  CONFIDENCE_LEVEL_GREATER_THAN = 'confidence_level_greater_than',
  PRIORITY_EQUALS = 'priority_equals',
  PRIORITY_GREATER_THAN = 'priority_greater_than',
  TIME_DELAY_GREATER_THAN = 'time_delay_greater_than',
  COST_GREATER_THAN = 'cost_greater_than',
  RISK_LEVEL_EQUALS = 'risk_level_equals',
  USER_ROLE_EQUALS = 'user_role_equals',
  PROJECT_TYPE_EQUALS = 'project_type_equals',
}
