export enum IntegrationType {
  JIRA = 'jira',
  GITHUB = 'github',
  SLACK = 'slack',
  FIGMA = 'figma',
  HUBSPOT = 'hubspot',
  WEBHOOK = 'webhook',
  API = 'api',
  DATABASE = 'database',
}

export enum IntegrationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  ERROR = 'error',
  SYNCING = 'syncing',
  DISCONNECTED = 'disconnected',
}

export enum SyncDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
  BIDIRECTIONAL = 'bidirectional',
}

export enum WebhookEventType {
  CHANGE_REQUEST_CREATED = 'change_request_created',
  CHANGE_REQUEST_UPDATED = 'change_request_updated',
  CHANGE_REQUEST_APPROVED = 'change_request_approved',
  CHANGE_REQUEST_REJECTED = 'change_request_rejected',
  IMPACT_ASSESSMENT_COMPLETED = 'impact_assessment_completed',
  WORKFLOW_STARTED = 'workflow_started',
  WORKFLOW_COMPLETED = 'workflow_completed',
}

export enum ExternalSystemType {
  JIRA = 'jira',
  GITHUB = 'github',
  SLACK = 'slack',
  FIGMA = 'figma',
  HUBSPOT = 'hubspot',
  PROJECT_MANAGEMENT = 'project_management',
  VERSION_CONTROL = 'version_control',
  COMMUNICATION = 'communication',
  DESIGN = 'design',
  CRM = 'crm',
  MONITORING = 'monitoring',
  CUSTOM = 'custom',
}

export enum DataMappingType {
  DIRECT = 'direct',
  TRANSFORMED = 'transformed',
  CALCULATED = 'calculated',
  CONDITIONAL = 'conditional',
}
