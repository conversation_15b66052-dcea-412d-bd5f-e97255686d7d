export enum NotificationType {
  CHANGE_REQUEST_CREATED = 'change_request_created',
  CHANGE_REQUEST_UPDATED = 'change_request_updated',
  CHANGE_REQUEST_APPROVED = 'change_request_approved',
  CHANGE_REQUEST_REJECTED = 'change_request_rejected',
  CHANGE_REQUEST_COMPLETED = 'change_request_completed',
  CHANGE_REQUEST_CANCELLED = 'change_request_cancelled',
  WORKFLOW_STARTED = 'workflow_started',
  WORKFLOW_COMPLETED = 'workflow_completed',
  APPROVAL_REQUIRED = 'approval_required',
  APPROVAL_OVERDUE = 'approval_overdue',
  IMPACT_ASSESSMENT_COMPLETED = 'impact_assessment_completed',
}

export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  IN_APP = 'in_app',
  SLACK = 'slack',
  WEBHOOK = 'webhook',
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical',
}

export enum NotificationFrequency {
  IMMEDIATE = 'immediate',
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
}
