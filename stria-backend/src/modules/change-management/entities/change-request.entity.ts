import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  OneToMany,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Project } from '../../projects/entities/project.entity';
import {
  ChangeRequestType,
  ChangeRequestPriority,
  ChangeRequestStatus,
  ChangeRequestComplexity,
  ChangeRequestImpactArea,
  ChangeRequestSource,
  ChangeRequestUrgency,
  ChangeRequestRiskLevel,
  DEFAULT_CHANGE_REQUEST_TYPE,
  DEFAULT_CHANGE_REQUEST_PRIORITY,
  DEFAULT_CHANGE_REQUEST_STATUS,
  DEFAULT_CHANGE_REQUEST_COMPLEXITY,
  DEFAULT_CHANGE_REQUEST_SOURCE,
  DEFAULT_CHANGE_REQUEST_URGENCY,
  DEFAULT_CHANGE_REQUEST_RISK_LEVEL,
} from '../enums';

/**
 * ChangeRequest Entity
 * Sprint 7: Change Request Management System
 * 
 * Represents a structured change request with comprehensive tracking and management capabilities
 */
@Entity('change_requests')
export class ChangeRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Basic Information
  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'text', nullable: true })
  businessJustification: string;

  @Column({ type: 'text', nullable: true })
  technicalDetails: string;

  // Classification
  @Column({
    type: 'enum',
    enum: ChangeRequestType,
    default: DEFAULT_CHANGE_REQUEST_TYPE,
  })
  @Index('idx_change_requests_type')
  type: ChangeRequestType;

  @Column({
    type: 'enum',
    enum: ChangeRequestPriority,
    default: DEFAULT_CHANGE_REQUEST_PRIORITY,
  })
  @Index('idx_change_requests_priority')
  priority: ChangeRequestPriority;

  @Column({
    type: 'enum',
    enum: ChangeRequestStatus,
    default: DEFAULT_CHANGE_REQUEST_STATUS,
  })
  @Index('idx_change_requests_status')
  status: ChangeRequestStatus;

  @Column({
    type: 'enum',
    enum: ChangeRequestComplexity,
    default: DEFAULT_CHANGE_REQUEST_COMPLEXITY,
  })
  complexity: ChangeRequestComplexity;

  @Column({
    type: 'enum',
    enum: ChangeRequestSource,
    default: DEFAULT_CHANGE_REQUEST_SOURCE,
  })
  source: ChangeRequestSource;

  @Column({
    type: 'enum',
    enum: ChangeRequestUrgency,
    default: DEFAULT_CHANGE_REQUEST_URGENCY,
  })
  urgency: ChangeRequestUrgency;

  @Column({
    type: 'enum',
    enum: ChangeRequestRiskLevel,
    default: DEFAULT_CHANGE_REQUEST_RISK_LEVEL,
  })
  riskLevel: ChangeRequestRiskLevel;

  // Impact Areas (stored as JSON array)
  @Column({
    type: 'jsonb',
    default: '[]',
    comment: 'Array of ChangeRequestImpactArea enums',
  })
  impactAreas: ChangeRequestImpactArea[];

  // Relationships
  @ManyToOne(() => Project, { nullable: false })
  @JoinColumn({ name: 'project_id' })
  project: Project;

  @Column({ name: 'project_id', type: 'uuid' })
  @Index('idx_change_requests_project_id')
  projectId: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'requester_id' })
  requester: User;

  @Column({ name: 'requester_id', type: 'uuid' })
  @Index('idx_change_requests_requester_id')
  requesterId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to_id' })
  assignedTo: User;

  @Column({ name: 'assigned_to_id', type: 'uuid', nullable: true })
  @Index('idx_change_requests_assigned_to_id')
  assignedToId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approved_by_id' })
  approvedBy: User;

  @Column({ name: 'approved_by_id', type: 'uuid', nullable: true })
  approvedById: string;

  // Timeline Information
  @Column({ name: 'requested_completion_date', type: 'date', nullable: true })
  requestedCompletionDate: Date;

  @Column({ name: 'estimated_completion_date', type: 'date', nullable: true })
  estimatedCompletionDate: Date;

  @Column({ name: 'actual_completion_date', type: 'date', nullable: true })
  actualCompletionDate: Date;

  @Column({ name: 'submitted_at', type: 'timestamp', nullable: true })
  submittedAt: Date;

  @Column({ name: 'approved_at', type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ name: 'rejected_at', type: 'timestamp', nullable: true })
  rejectedAt: Date;

  @Column({ name: 'completed_at', type: 'timestamp', nullable: true })
  completedAt: Date;

  // Cost and Effort Estimates
  @Column({
    name: 'estimated_cost',
    type: 'decimal',
    precision: 12,
    scale: 2,
    nullable: true,
  })
  estimatedCost: number;

  @Column({
    name: 'actual_cost',
    type: 'decimal',
    precision: 12,
    scale: 2,
    nullable: true,
  })
  actualCost: number;

  @Column({
    name: 'estimated_hours',
    type: 'decimal',
    precision: 8,
    scale: 2,
    nullable: true,
  })
  estimatedHours: number;

  @Column({
    name: 'actual_hours',
    type: 'decimal',
    precision: 8,
    scale: 2,
    nullable: true,
  })
  actualHours: number;

  // Additional Information
  @Column({ type: 'text', nullable: true })
  rejectionReason: string;

  @Column({ type: 'text', nullable: true })
  implementationNotes: string;

  @Column({ type: 'text', nullable: true })
  testingRequirements: string;

  @Column({ type: 'text', nullable: true })
  rollbackPlan: string;

  // External References
  @Column({ name: 'external_ticket_id', type: 'varchar', length: 255, nullable: true })
  externalTicketId: string;

  @Column({ name: 'external_system', type: 'varchar', length: 100, nullable: true })
  externalSystem: string;

  // Metadata and Configuration
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    tags?: string[];
    customFields?: Record<string, any>;
    attachments?: Array<{
      id: string;
      name: string;
      url: string;
      type: string;
      size: number;
      uploadedAt: string;
      uploadedBy: string;
    }>;
    relatedChangeRequests?: string[];
    stakeholders?: Array<{
      userId: string;
      role: string;
      notificationPreferences: Record<string, boolean>;
    }>;
    approvalHistory?: Array<{
      stepId: string;
      approver: string;
      decision: string;
      timestamp: string;
      comments?: string;
    }>;
    [key: string]: any;
  };

  // External System References
  @Column({ type: 'jsonb', nullable: true })
  externalReferences?: {
    jira?: {
      issueKey: string;
      issueId: string;
      url: string;
    };
    github?: {
      issueNumber: number;
      issueId: number;
      url: string;
    };
    slack?: {
      channelId: string;
      messageId: string;
    };
    [key: string]: any;
  };

  // Audit Fields
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'version', type: 'integer', default: 1 })
  version: number;

  // Computed Properties
  get isOverdue(): boolean {
    if (!this.requestedCompletionDate) return false;
    return new Date() > this.requestedCompletionDate && 
           ![ChangeRequestStatus.COMPLETED, ChangeRequestStatus.CANCELLED, ChangeRequestStatus.REJECTED].includes(this.status);
  }

  get daysUntilDeadline(): number | null {
    if (!this.requestedCompletionDate) return null;
    const now = new Date();
    const deadline = new Date(this.requestedCompletionDate);
    const diffTime = deadline.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  get costVariance(): number | null {
    if (!this.estimatedCost || !this.actualCost) return null;
    return this.actualCost - this.estimatedCost;
  }

  get hoursVariance(): number | null {
    if (!this.estimatedHours || !this.actualHours) return null;
    return this.actualHours - this.estimatedHours;
  }

  get isHighPriority(): boolean {
    return [ChangeRequestPriority.HIGH, ChangeRequestPriority.URGENT, ChangeRequestPriority.CRITICAL].includes(this.priority);
  }

  get isHighRisk(): boolean {
    return [ChangeRequestRiskLevel.HIGH, ChangeRequestRiskLevel.VERY_HIGH].includes(this.riskLevel);
  }
}
