import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChangeRequest } from '../entities/change-request.entity';
import { ApprovalWorkflow } from '../entities/approval-workflow.entity';
import { User } from '../../users/entities/user.entity';
import {
  ChangeRequestStatus,
  ChangeEventType,
  WorkflowStatus,
  ApprovalStepStatus,
  NotificationType,
  NotificationChannel,
  NotificationPriority,
  NotificationFrequency,
} from '../enums';

/**
 * Change Notification Service
 * Sprint 7: Change Request Management System
 * 
 * Handles all notification and communication for change management events
 * Supports multiple channels, templates, and delivery preferences
 */
@Injectable()
export class ChangeNotificationService {
  private readonly logger = new Logger(ChangeNotificationService.name);

  // Notification configuration
  private readonly notificationConfig = {
    channels: {
      email: { enabled: true, priority: 1 },
      inApp: { enabled: true, priority: 2 },
      slack: { enabled: false, priority: 3 },
      webhook: { enabled: false, priority: 4 },
    },
    templates: {
      changeRequestCreated: 'change-request-created',
      changeRequestUpdated: 'change-request-updated',
      changeRequestApproved: 'change-request-approved',
      changeRequestRejected: 'change-request-rejected',
      workflowStarted: 'workflow-started',
      approvalRequired: 'approval-required',
      approvalOverdue: 'approval-overdue',
      workflowCompleted: 'workflow-completed',
      impactAssessmentCompleted: 'impact-assessment-completed',
      reminderNotification: 'reminder-notification',
    },
    defaultSettings: {
      frequency: NotificationFrequency.IMMEDIATE,
      channels: [NotificationChannel.EMAIL, NotificationChannel.IN_APP],
      priority: NotificationPriority.MEDIUM,
    },
  };

  constructor(
    @InjectRepository(ChangeRequest)
    private readonly changeRequestRepository: Repository<ChangeRequest>,
    @InjectRepository(ApprovalWorkflow)
    private readonly workflowRepository: Repository<ApprovalWorkflow>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    // TODO: Inject email service, slack service, etc.
  ) {}

  /**
   * Send change request created notification
   */
  async notifyChangeRequestCreated(
    changeRequest: ChangeRequest,
    recipients?: string[],
  ): Promise<NotificationResult> {
    this.logger.log(`Sending change request created notification for: ${changeRequest.id}`);

    const defaultRecipients = await this.getDefaultRecipients(changeRequest, 'created');
    const allRecipients = [...new Set([...defaultRecipients, ...(recipients || [])])];

    const notificationData = {
      type: NotificationType.CHANGE_REQUEST_CREATED,
      changeRequestId: changeRequest.id,
      title: `New Change Request: ${changeRequest.title}`,
      message: `A new change request has been created for project ${changeRequest.projectId}`,
      data: {
        changeRequest: this.sanitizeChangeRequestData(changeRequest),
        actionUrl: `/change-requests/${changeRequest.id}`,
        priority: changeRequest.priority,
        type: changeRequest.type,
        requester: changeRequest.requesterId,
      },
      priority: this.mapPriorityToNotificationPriority(changeRequest.priority),
    };

    return this.sendNotification(notificationData, allRecipients);
  }

  /**
   * Send change request status update notification
   */
  async notifyChangeRequestStatusUpdate(
    changeRequest: ChangeRequest,
    oldStatus: ChangeRequestStatus,
    newStatus: ChangeRequestStatus,
    updatedBy: string,
    reason?: string,
  ): Promise<NotificationResult> {
    this.logger.log(`Sending status update notification: ${oldStatus} -> ${newStatus}`);

    const recipients = await this.getStatusUpdateRecipients(changeRequest, newStatus);

    const notificationData = {
      type: this.getNotificationTypeForStatus(newStatus),
      changeRequestId: changeRequest.id,
      title: `Change Request ${this.getStatusDisplayName(newStatus)}: ${changeRequest.title}`,
      message: this.getStatusUpdateMessage(changeRequest, oldStatus, newStatus, reason),
      data: {
        changeRequest: this.sanitizeChangeRequestData(changeRequest),
        oldStatus,
        newStatus,
        updatedBy,
        reason,
        actionUrl: `/change-requests/${changeRequest.id}`,
      },
      priority: this.getStatusUpdatePriority(newStatus),
    };

    return this.sendNotification(notificationData, recipients);
  }

  /**
   * Send approval workflow started notification
   */
  async notifyWorkflowStarted(
    workflow: ApprovalWorkflow,
    changeRequest: ChangeRequest,
  ): Promise<NotificationResult> {
    this.logger.log(`Sending workflow started notification for: ${workflow.id}`);

    const recipients = await this.getWorkflowRecipients(workflow, 'started');

    const notificationData = {
      type: NotificationType.WORKFLOW_STARTED,
      changeRequestId: changeRequest.id,
      workflowId: workflow.id,
      title: `Approval Workflow Started: ${workflow.name}`,
      message: `An approval workflow has been started for change request: ${changeRequest.title}`,
      data: {
        workflow: this.sanitizeWorkflowData(workflow),
        changeRequest: this.sanitizeChangeRequestData(changeRequest),
        actionUrl: `/workflows/${workflow.id}`,
        estimatedDuration: workflow.estimatedDurationHours,
        deadline: workflow.deadline,
      },
      priority: NotificationPriority.MEDIUM,
    };

    return this.sendNotification(notificationData, recipients);
  }

  /**
   * Send approval required notification
   */
  async notifyApprovalRequired(
    workflow: ApprovalWorkflow,
    stepId: string,
    approver: User,
    changeRequest: ChangeRequest,
  ): Promise<NotificationResult> {
    this.logger.log(`Sending approval required notification to: ${approver.id}`);

    const notificationData = {
      type: NotificationType.APPROVAL_REQUIRED,
      changeRequestId: changeRequest.id,
      workflowId: workflow.id,
      stepId,
      title: `Approval Required: ${changeRequest.title}`,
      message: `Your approval is required for change request: ${changeRequest.title}`,
      data: {
        workflow: this.sanitizeWorkflowData(workflow),
        changeRequest: this.sanitizeChangeRequestData(changeRequest),
        approver: { id: approver.id, name: approver.firstName + ' ' + approver.lastName },
        actionUrl: `/workflows/${workflow.id}/approve`,
        deadline: workflow.deadline,
        urgency: workflow.urgency,
      },
      priority: this.mapUrgencyToNotificationPriority(workflow.urgency),
    };

    return this.sendNotification(notificationData, [approver.id]);
  }

  /**
   * Send overdue approval notification
   */
  async notifyApprovalOverdue(
    workflow: ApprovalWorkflow,
    stepId: string,
    approver: User,
    changeRequest: ChangeRequest,
    hoursOverdue: number,
  ): Promise<NotificationResult> {
    this.logger.log(`Sending overdue approval notification: ${hoursOverdue} hours overdue`);

    const notificationData = {
      type: NotificationType.APPROVAL_OVERDUE,
      changeRequestId: changeRequest.id,
      workflowId: workflow.id,
      stepId,
      title: `OVERDUE: Approval Required - ${changeRequest.title}`,
      message: `Your approval for "${changeRequest.title}" is ${hoursOverdue} hours overdue`,
      data: {
        workflow: this.sanitizeWorkflowData(workflow),
        changeRequest: this.sanitizeChangeRequestData(changeRequest),
        approver: { id: approver.id, name: approver.firstName + ' ' + approver.lastName },
        hoursOverdue,
        actionUrl: `/workflows/${workflow.id}/approve`,
        escalationWarning: hoursOverdue > 24,
      },
      priority: NotificationPriority.HIGH,
    };

    return this.sendNotification(notificationData, [approver.id]);
  }

  /**
   * Send workflow completed notification
   */
  async notifyWorkflowCompleted(
    workflow: ApprovalWorkflow,
    changeRequest: ChangeRequest,
    finalDecision: string,
  ): Promise<NotificationResult> {
    this.logger.log(`Sending workflow completed notification: ${finalDecision}`);

    const recipients = await this.getWorkflowRecipients(workflow, 'completed');

    const notificationData = {
      type: NotificationType.WORKFLOW_COMPLETED,
      changeRequestId: changeRequest.id,
      workflowId: workflow.id,
      title: `Workflow ${finalDecision}: ${changeRequest.title}`,
      message: `The approval workflow for "${changeRequest.title}" has been ${finalDecision}`,
      data: {
        workflow: this.sanitizeWorkflowData(workflow),
        changeRequest: this.sanitizeChangeRequestData(changeRequest),
        finalDecision,
        completedAt: workflow.completedAt,
        duration: workflow.durationMinutes,
        actionUrl: `/change-requests/${changeRequest.id}`,
      },
      priority: this.getDecisionPriority(finalDecision),
    };

    return this.sendNotification(notificationData, recipients);
  }

  /**
   * Send impact assessment completed notification
   */
  async notifyImpactAssessmentCompleted(
    changeRequest: ChangeRequest,
    assessmentId: string,
    overallImpact: string,
    requiresReview: boolean,
  ): Promise<NotificationResult> {
    this.logger.log(`Sending impact assessment completed notification`);

    const recipients = await this.getAssessmentRecipients(changeRequest, requiresReview);

    const notificationData = {
      type: NotificationType.IMPACT_ASSESSMENT_COMPLETED,
      changeRequestId: changeRequest.id,
      assessmentId,
      title: `Impact Assessment Completed: ${changeRequest.title}`,
      message: `Impact assessment shows ${overallImpact} impact for "${changeRequest.title}"`,
      data: {
        changeRequest: this.sanitizeChangeRequestData(changeRequest),
        overallImpact,
        requiresReview,
        actionUrl: `/impact-assessments/${assessmentId}`,
        reviewUrl: requiresReview ? `/impact-assessments/${assessmentId}/review` : null,
      },
      priority: requiresReview ? NotificationPriority.HIGH : NotificationPriority.MEDIUM,
    };

    return this.sendNotification(notificationData, recipients);
  }

  /**
   * Send reminder notifications
   */
  async sendReminders(): Promise<NotificationResult[]> {
    this.logger.log('Sending scheduled reminder notifications');

    const results: NotificationResult[] = [];

    // Find overdue approvals
    const overdueWorkflows = await this.findOverdueWorkflows();
    for (const workflow of overdueWorkflows) {
      const changeRequest = await this.changeRequestRepository.findOne({
        where: { id: workflow.changeRequestId },
      });

      if (changeRequest && workflow.currentApproverId) {
        const approver = await this.userRepository.findOne({
          where: { id: workflow.currentApproverId },
        });

        if (approver) {
          const hoursOverdue = this.calculateHoursOverdue(workflow);
          const result = await this.notifyApprovalOverdue(
            workflow,
            '', // TODO: Get current step ID
            approver,
            changeRequest,
            hoursOverdue,
          );
          results.push(result);
        }
      }
    }

    // Find pending assessments
    // TODO: Implement pending assessment reminders

    return results;
  }

  /**
   * Send custom notification
   */
  async sendCustomNotification(
    type: NotificationType,
    recipients: string[],
    title: string,
    message: string,
    data?: Record<string, any>,
    priority?: NotificationPriority,
  ): Promise<NotificationResult> {
    this.logger.log(`Sending custom notification: ${type}`);

    const notificationData = {
      type,
      title,
      message,
      data: data || {},
      priority: priority || NotificationPriority.MEDIUM,
    };

    return this.sendNotification(notificationData, recipients);
  }

  /**
   * Core notification sending method
   */
  private async sendNotification(
    notificationData: NotificationData,
    recipients: string[],
  ): Promise<NotificationResult> {
    const startTime = Date.now();
    const results: ChannelResult[] = [];

    try {
      // Get user preferences for each recipient
      const userPreferences = await this.getUserNotificationPreferences(recipients);

      // Send notifications through each enabled channel
      for (const recipient of recipients) {
        const preferences = userPreferences[recipient] || this.notificationConfig.defaultSettings;
        
        for (const channel of preferences.channels) {
          if (this.shouldSendNotification(notificationData, channel, preferences)) {
            const channelResult = await this.sendThroughChannel(
              channel,
              notificationData,
              recipient,
              preferences,
            );
            results.push(channelResult);
          }
        }
      }

      const endTime = Date.now();

      return {
        success: results.every(r => r.success),
        totalRecipients: recipients.length,
        channelResults: results,
        executionTime: endTime - startTime,
        notificationId: this.generateNotificationId(),
      };

    } catch (error) {
      this.logger.error(`Notification sending failed: ${error.message}`, error.stack);
      
      return {
        success: false,
        totalRecipients: recipients.length,
        channelResults: results,
        executionTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  /**
   * Send notification through specific channel
   */
  private async sendThroughChannel(
    channel: NotificationChannel,
    notificationData: NotificationData,
    recipient: string,
    preferences: UserNotificationPreferences,
  ): Promise<ChannelResult> {
    try {
      switch (channel) {
        case NotificationChannel.EMAIL:
          return await this.sendEmailNotification(notificationData, recipient);
        
        case NotificationChannel.IN_APP:
          return await this.sendInAppNotification(notificationData, recipient);
        
        case NotificationChannel.SLACK:
          return await this.sendSlackNotification(notificationData, recipient);
        
        case NotificationChannel.WEBHOOK:
          return await this.sendWebhookNotification(notificationData, recipient);
        
        default:
          throw new Error(`Unsupported notification channel: ${channel}`);
      }
    } catch (error) {
      this.logger.error(`Channel ${channel} failed for recipient ${recipient}: ${error.message}`);
      
      return {
        channel,
        recipient,
        success: false,
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(
    notificationData: NotificationData,
    recipient: string,
  ): Promise<ChannelResult> {
    // TODO: Implement email sending logic
    this.logger.debug(`Sending email notification to ${recipient}`);
    
    return {
      channel: NotificationChannel.EMAIL,
      recipient,
      success: true,
      timestamp: new Date(),
    };
  }

  /**
   * Send in-app notification
   */
  private async sendInAppNotification(
    notificationData: NotificationData,
    recipient: string,
  ): Promise<ChannelResult> {
    // TODO: Implement in-app notification logic
    this.logger.debug(`Sending in-app notification to ${recipient}`);
    
    return {
      channel: NotificationChannel.IN_APP,
      recipient,
      success: true,
      timestamp: new Date(),
    };
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(
    notificationData: NotificationData,
    recipient: string,
  ): Promise<ChannelResult> {
    // TODO: Implement Slack notification logic
    this.logger.debug(`Sending Slack notification to ${recipient}`);
    
    return {
      channel: NotificationChannel.SLACK,
      recipient,
      success: true,
      timestamp: new Date(),
    };
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(
    notificationData: NotificationData,
    recipient: string,
  ): Promise<ChannelResult> {
    // TODO: Implement webhook notification logic
    this.logger.debug(`Sending webhook notification to ${recipient}`);
    
    return {
      channel: NotificationChannel.WEBHOOK,
      recipient,
      success: true,
      timestamp: new Date(),
    };
  }

  /**
   * Helper methods
   */
  private async getDefaultRecipients(changeRequest: ChangeRequest, event: string): Promise<string[]> {
    const recipients: string[] = [];
    
    // Always include requester
    recipients.push(changeRequest.requesterId);
    
    // Include assigned user if exists
    if (changeRequest.assignedToId) {
      recipients.push(changeRequest.assignedToId);
    }
    
    // TODO: Add project stakeholders, managers, etc.
    
    return [...new Set(recipients)];
  }

  private async getStatusUpdateRecipients(
    changeRequest: ChangeRequest,
    newStatus: ChangeRequestStatus,
  ): Promise<string[]> {
    return this.getDefaultRecipients(changeRequest, 'status_update');
  }

  private async getWorkflowRecipients(workflow: ApprovalWorkflow, event: string): Promise<string[]> {
    const recipients: string[] = [];
    
    // Include workflow creator
    recipients.push(workflow.createdById);
    
    // Include current approver
    if (workflow.currentApproverId) {
      recipients.push(workflow.currentApproverId);
    }
    
    // TODO: Add all workflow participants
    
    return [...new Set(recipients)];
  }

  private async getAssessmentRecipients(
    changeRequest: ChangeRequest,
    requiresReview: boolean,
  ): Promise<string[]> {
    const recipients = await this.getDefaultRecipients(changeRequest, 'assessment');
    
    // TODO: Add assessment reviewers if review is required
    
    return recipients;
  }

  private async findOverdueWorkflows(): Promise<ApprovalWorkflow[]> {
    return this.workflowRepository
      .createQueryBuilder('workflow')
      .where('workflow.status = :status', { status: WorkflowStatus.ACTIVE })
      .andWhere('workflow.deadline < :now', { now: new Date() })
      .getMany();
  }

  private calculateHoursOverdue(workflow: ApprovalWorkflow): number {
    if (!workflow.deadline) return 0;
    
    const now = new Date();
    const deadline = new Date(workflow.deadline);
    const diffMs = now.getTime() - deadline.getTime();
    
    return Math.max(0, Math.round(diffMs / (1000 * 60 * 60)));
  }

  private async getUserNotificationPreferences(
    recipients: string[],
  ): Promise<Record<string, UserNotificationPreferences>> {
    // TODO: Implement user preference retrieval
    const preferences: Record<string, UserNotificationPreferences> = {};
    
    recipients.forEach(recipient => {
      preferences[recipient] = this.notificationConfig.defaultSettings;
    });
    
    return preferences;
  }

  private shouldSendNotification(
    notificationData: NotificationData,
    channel: NotificationChannel,
    preferences: UserNotificationPreferences,
  ): boolean {
    // Check if channel is enabled
    if (!this.notificationConfig.channels[channel]?.enabled) {
      return false;
    }
    
    // Check user preferences
    if (!preferences.channels.includes(channel)) {
      return false;
    }
    
    // Check frequency settings
    // TODO: Implement frequency checking logic
    
    return true;
  }

  private sanitizeChangeRequestData(changeRequest: ChangeRequest): any {
    return {
      id: changeRequest.id,
      title: changeRequest.title,
      type: changeRequest.type,
      priority: changeRequest.priority,
      status: changeRequest.status,
      complexity: changeRequest.complexity,
      projectId: changeRequest.projectId,
      requesterId: changeRequest.requesterId,
      createdAt: changeRequest.createdAt,
    };
  }

  private sanitizeWorkflowData(workflow: ApprovalWorkflow): any {
    return {
      id: workflow.id,
      name: workflow.name,
      workflowType: workflow.workflowType,
      status: workflow.status,
      priority: workflow.priority,
      urgency: workflow.urgency,
      currentStepOrder: workflow.currentStepOrder,
      totalSteps: workflow.totalSteps,
      deadline: workflow.deadline,
      createdAt: workflow.createdAt,
    };
  }

  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Additional helper methods for mapping and formatting
  private mapPriorityToNotificationPriority(priority: any): NotificationPriority {
    const mapping: Record<string, NotificationPriority> = {
      'low': NotificationPriority.LOW,
      'medium': NotificationPriority.MEDIUM,
      'high': NotificationPriority.HIGH,
      'urgent': NotificationPriority.HIGH,
      'critical': NotificationPriority.CRITICAL,
    };
    return mapping[priority] || NotificationPriority.MEDIUM;
  }

  private mapUrgencyToNotificationPriority(urgency: any): NotificationPriority {
    const mapping: Record<string, NotificationPriority> = {
      'low': NotificationPriority.LOW,
      'normal': NotificationPriority.MEDIUM,
      'high': NotificationPriority.HIGH,
      'urgent': NotificationPriority.HIGH,
      'critical': NotificationPriority.CRITICAL,
    };
    return mapping[urgency] || NotificationPriority.MEDIUM;
  }

  private getNotificationTypeForStatus(status: ChangeRequestStatus): NotificationType {
    const mapping: Partial<Record<ChangeRequestStatus, NotificationType>> = {
      [ChangeRequestStatus.APPROVED]: NotificationType.CHANGE_REQUEST_APPROVED,
      [ChangeRequestStatus.REJECTED]: NotificationType.CHANGE_REQUEST_REJECTED,
      [ChangeRequestStatus.COMPLETED]: NotificationType.CHANGE_REQUEST_COMPLETED,
      [ChangeRequestStatus.CANCELLED]: NotificationType.CHANGE_REQUEST_CANCELLED,
    };
    return mapping[status] || NotificationType.CHANGE_REQUEST_UPDATED;
  }

  private getStatusDisplayName(status: ChangeRequestStatus): string {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  private getStatusUpdateMessage(
    changeRequest: ChangeRequest,
    oldStatus: ChangeRequestStatus,
    newStatus: ChangeRequestStatus,
    reason?: string,
  ): string {
    const statusName = this.getStatusDisplayName(newStatus);
    let message = `Change request "${changeRequest.title}" has been ${statusName.toLowerCase()}`;
    
    if (reason) {
      message += `. Reason: ${reason}`;
    }
    
    return message;
  }

  private getStatusUpdatePriority(status: ChangeRequestStatus): NotificationPriority {
    const highPriorityStatuses = [
      ChangeRequestStatus.APPROVED,
      ChangeRequestStatus.REJECTED,
      ChangeRequestStatus.CANCELLED,
    ];
    
    return highPriorityStatuses.includes(status) 
      ? NotificationPriority.HIGH 
      : NotificationPriority.MEDIUM;
  }

  private getDecisionPriority(decision: string): NotificationPriority {
    return decision === 'approved' ? NotificationPriority.HIGH : NotificationPriority.MEDIUM;
  }
}

// Type definitions
interface NotificationData {
  type: NotificationType;
  changeRequestId?: string;
  workflowId?: string;
  stepId?: string;
  assessmentId?: string;
  title: string;
  message: string;
  data: Record<string, any>;
  priority: NotificationPriority;
}

interface NotificationResult {
  success: boolean;
  totalRecipients: number;
  channelResults: ChannelResult[];
  executionTime: number;
  notificationId?: string;
  error?: string;
}

interface ChannelResult {
  channel: NotificationChannel;
  recipient: string;
  success: boolean;
  timestamp: Date;
  error?: string;
}

interface UserNotificationPreferences {
  frequency: NotificationFrequency;
  channels: NotificationChannel[];
  priority: NotificationPriority;
}
