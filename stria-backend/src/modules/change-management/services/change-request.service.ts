import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, In, Between } from 'typeorm';
import { plainToClass } from 'class-transformer';
import { ChangeRequest } from '../entities/change-request.entity';
import { Project } from '../../projects/entities/project.entity';
import { User } from '../../users/entities/user.entity';
import {
  CreateChangeRequestDto,
  CreateChangeRequestWithAssessmentDto,
  BulkCreateChangeRequestDto,
  UpdateChangeRequestDto,
  UpdateChangeRequestStatusDto,
  AssignChangeRequestDto,
  ApproveChangeRequestDto,
  RejectChangeRequestDto,
  BulkUpdateChangeRequestDto,
  ChangeRequestResponseDto,
  ChangeRequestListResponseDto,
  ChangeRequestSummaryDto,
  ChangeRequestStatisticsDto,
} from '../dto/change-request';
import {
  ChangeRequestStatus,
  ChangeRequestPriority,
  ChangeRequestType,
  ChangeEventType,
  DEFAULT_CHANGE_REQUEST_STATUS,
} from '../enums';

/**
 * Change Request Service
 * Sprint 7: Change Request Management System
 * 
 * Handles all business logic for change request management
 */
@Injectable()
export class ChangeRequestService {
  private readonly logger = new Logger(ChangeRequestService.name);

  constructor(
    @InjectRepository(ChangeRequest)
    private readonly changeRequestRepository: Repository<ChangeRequest>,
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Create a new change request
   */
  async create(
    createChangeRequestDto: CreateChangeRequestDto,
    requesterId: string,
  ): Promise<ChangeRequestResponseDto> {
    this.logger.log(`Creating change request: ${createChangeRequestDto.title}`);

    // Validate project exists and user has access
    const project = await this.validateProjectAccess(createChangeRequestDto.projectId, requesterId);

    // Validate assigned user if provided
    if (createChangeRequestDto.assignedToId) {
      await this.validateUserExists(createChangeRequestDto.assignedToId);
    }

    // Create change request entity
    const changeRequest = this.changeRequestRepository.create({
      ...createChangeRequestDto,
      requesterId,
      status: DEFAULT_CHANGE_REQUEST_STATUS,
      submittedAt: new Date(),
    });

    // Save to database
    const savedChangeRequest = await this.changeRequestRepository.save(changeRequest);

    this.logger.log(`Change request created with ID: ${savedChangeRequest.id}`);

    return plainToClass(ChangeRequestResponseDto, savedChangeRequest, {
      excludeExtraneousValues: true,
    });
  }

  /**
   * Create change request with automatic impact assessment
   */
  async createWithAssessment(
    createDto: CreateChangeRequestWithAssessmentDto,
    requesterId: string,
  ): Promise<ChangeRequestResponseDto> {
    const changeRequest = await this.create(createDto, requesterId);

    // TODO: Trigger impact assessment if requested
    if (createDto.autoTriggerAssessment) {
      this.logger.log(`Triggering automatic impact assessment for change request: ${changeRequest.id}`);
      // This will be implemented in STRIA-117
    }

    // TODO: Start approval workflow if requested
    if (createDto.autoStartWorkflow && createDto.initialApprovers?.length) {
      this.logger.log(`Starting approval workflow for change request: ${changeRequest.id}`);
      // This will be implemented in STRIA-118
    }

    return changeRequest;
  }

  /**
   * Bulk create change requests
   */
  async bulkCreate(
    bulkCreateDto: BulkCreateChangeRequestDto,
    requesterId: string,
  ): Promise<ChangeRequestListResponseDto> {
    this.logger.log(`Bulk creating ${bulkCreateDto.changeRequests.length} change requests`);

    const createdRequests: ChangeRequestResponseDto[] = [];
    const errors: Array<{ index: number; error: string }> = [];

    for (let i = 0; i < bulkCreateDto.changeRequests.length; i++) {
      try {
        const changeRequestDto = bulkCreateDto.changeRequests[i];
        const created = await this.create(changeRequestDto, requesterId);
        createdRequests.push(created);
      } catch (error) {
        errors.push({
          index: i,
          error: error.message,
        });
      }
    }

    if (errors.length > 0) {
      this.logger.warn(`Bulk create completed with ${errors.length} errors`);
    }

    return {
      items: createdRequests,
      total: createdRequests.length,
      page: 1,
      limit: createdRequests.length,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    };
  }

  /**
   * Find change request by ID
   */
  async findById(id: string, userId: string): Promise<ChangeRequestResponseDto> {
    const changeRequest = await this.changeRequestRepository.findOne({
      where: { id },
      relations: ['project', 'requester', 'assignedTo', 'approvedBy'],
    });

    if (!changeRequest) {
      throw new NotFoundException('Change request not found');
    }

    // Check access permissions
    await this.validateChangeRequestAccess(changeRequest, userId);

    return plainToClass(ChangeRequestResponseDto, changeRequest, {
      excludeExtraneousValues: true,
    });
  }

  /**
   * Find all change requests with filtering and pagination
   */
  async findAll(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      projectId?: string;
      status?: ChangeRequestStatus;
      priority?: ChangeRequestPriority;
      type?: ChangeRequestType;
      assignedToId?: string;
      requesterId?: string;
      search?: string;
      sortBy?: string;
      sortOrder?: 'ASC' | 'DESC';
      dateFrom?: string;
      dateTo?: string;
    } = {},
  ): Promise<ChangeRequestListResponseDto> {
    const {
      page = 1,
      limit = 20,
      projectId,
      status,
      priority,
      type,
      assignedToId,
      requesterId,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      dateFrom,
      dateTo,
    } = options;

    const queryBuilder = this.changeRequestRepository
      .createQueryBuilder('cr')
      .leftJoinAndSelect('cr.project', 'project')
      .leftJoinAndSelect('cr.requester', 'requester')
      .leftJoinAndSelect('cr.assignedTo', 'assignedTo');

    // Apply filters
    if (projectId) {
      queryBuilder.andWhere('cr.projectId = :projectId', { projectId });
    }

    if (status) {
      queryBuilder.andWhere('cr.status = :status', { status });
    }

    if (priority) {
      queryBuilder.andWhere('cr.priority = :priority', { priority });
    }

    if (type) {
      queryBuilder.andWhere('cr.type = :type', { type });
    }

    if (assignedToId) {
      queryBuilder.andWhere('cr.assignedToId = :assignedToId', { assignedToId });
    }

    if (requesterId) {
      queryBuilder.andWhere('cr.requesterId = :requesterId', { requesterId });
    }

    if (search) {
      queryBuilder.andWhere(
        '(cr.title ILIKE :search OR cr.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (dateFrom) {
      queryBuilder.andWhere('cr.createdAt >= :dateFrom', { dateFrom });
    }

    if (dateTo) {
      queryBuilder.andWhere('cr.createdAt <= :dateTo', { dateTo });
    }

    // Apply sorting
    queryBuilder.orderBy(`cr.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      items: items.map(item => 
        plainToClass(ChangeRequestResponseDto, item, {
          excludeExtraneousValues: true,
        })
      ),
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Update change request
   */
  async update(
    id: string,
    updateDto: UpdateChangeRequestDto,
    userId: string,
  ): Promise<ChangeRequestResponseDto> {
    const changeRequest = await this.findChangeRequestById(id);
    
    // Check permissions
    await this.validateChangeRequestAccess(changeRequest, userId);

    // Validate assigned user if being updated
    if (updateDto.assignedToId) {
      await this.validateUserExists(updateDto.assignedToId);
    }

    // Update version for optimistic locking
    if (updateDto.version !== undefined && updateDto.version !== changeRequest.version) {
      throw new ConflictException('Change request has been modified by another user');
    }

    // Apply updates
    Object.assign(changeRequest, updateDto);
    changeRequest.version += 1;

    const updatedChangeRequest = await this.changeRequestRepository.save(changeRequest);

    this.logger.log(`Change request updated: ${id}`);

    return plainToClass(ChangeRequestResponseDto, updatedChangeRequest, {
      excludeExtraneousValues: true,
    });
  }

  /**
   * Update change request status
   */
  async updateStatus(
    id: string,
    statusDto: UpdateChangeRequestStatusDto,
    userId: string,
  ): Promise<ChangeRequestResponseDto> {
    const changeRequest = await this.findChangeRequestById(id);
    
    // Check permissions
    await this.validateChangeRequestAccess(changeRequest, userId);

    // Validate status transition
    this.validateStatusTransition(changeRequest.status, statusDto.status);

    // Update status and related fields
    changeRequest.status = statusDto.status;
    
    if (statusDto.status === ChangeRequestStatus.APPROVED) {
      changeRequest.approvedAt = new Date();
      changeRequest.approvedById = userId;
    } else if (statusDto.status === ChangeRequestStatus.REJECTED) {
      changeRequest.rejectedAt = new Date();
      changeRequest.rejectionReason = statusDto.reason;
    } else if (statusDto.status === ChangeRequestStatus.COMPLETED) {
      changeRequest.completedAt = new Date();
      changeRequest.actualCompletionDate = new Date();
    }

    changeRequest.version += 1;

    const updatedChangeRequest = await this.changeRequestRepository.save(changeRequest);

    this.logger.log(`Change request status updated: ${id} -> ${statusDto.status}`);

    return plainToClass(ChangeRequestResponseDto, updatedChangeRequest, {
      excludeExtraneousValues: true,
    });
  }

  /**
   * Private helper methods
   */
  private async validateProjectAccess(projectId: string, userId: string): Promise<Project> {
    const project = await this.projectRepository.findOne({
      where: { id: projectId },
      relations: ['client'],
    });

    if (!project) {
      throw new NotFoundException('Project not found');
    }

    if (project.clientId !== userId) {
      throw new ForbiddenException('Access denied to this project');
    }

    return project;
  }

  private async validateUserExists(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  private async findChangeRequestById(id: string): Promise<ChangeRequest> {
    const changeRequest = await this.changeRequestRepository.findOne({
      where: { id },
      relations: ['project'],
    });

    if (!changeRequest) {
      throw new NotFoundException('Change request not found');
    }

    return changeRequest;
  }

  private async validateChangeRequestAccess(changeRequest: ChangeRequest, userId: string): Promise<void> {
    // User can access if they are the requester, assigned to it, or own the project
    const hasAccess =
      changeRequest.requesterId === userId ||
      changeRequest.assignedToId === userId ||
      changeRequest.project?.clientId === userId;

    if (!hasAccess) {
      throw new ForbiddenException('Access denied to this change request');
    }
  }

  /**
   * Assign change request to user
   */
  async assign(
    id: string,
    assignDto: AssignChangeRequestDto,
    userId: string,
  ): Promise<ChangeRequestResponseDto> {
    const changeRequest = await this.findChangeRequestById(id);

    // Check permissions
    await this.validateChangeRequestAccess(changeRequest, userId);

    // Validate assigned user exists
    await this.validateUserExists(assignDto.assignedToId);

    // Update assignment
    changeRequest.assignedToId = assignDto.assignedToId;

    if (assignDto.newPriority) {
      changeRequest.priority = assignDto.newPriority;
    }

    changeRequest.version += 1;

    const updatedChangeRequest = await this.changeRequestRepository.save(changeRequest);

    this.logger.log(`Change request assigned: ${id} -> ${assignDto.assignedToId}`);

    return plainToClass(ChangeRequestResponseDto, updatedChangeRequest, {
      excludeExtraneousValues: true,
    });
  }

  /**
   * Approve change request
   */
  async approve(
    id: string,
    approveDto: ApproveChangeRequestDto,
    userId: string,
  ): Promise<ChangeRequestResponseDto> {
    const changeRequest = await this.findChangeRequestById(id);

    // Check permissions
    await this.validateChangeRequestAccess(changeRequest, userId);

    // Validate current status allows approval
    if (changeRequest.status !== ChangeRequestStatus.PENDING_APPROVAL) {
      throw new BadRequestException('Change request is not in pending approval status');
    }

    // Update approval fields
    changeRequest.status = ChangeRequestStatus.APPROVED;
    changeRequest.approvedAt = new Date();
    changeRequest.approvedById = userId;

    if (approveDto.approvedCompletionDate) {
      changeRequest.estimatedCompletionDate = new Date(approveDto.approvedCompletionDate);
    }

    if (approveDto.approvedBudget) {
      changeRequest.estimatedCost = approveDto.approvedBudget;
    }

    changeRequest.version += 1;

    const updatedChangeRequest = await this.changeRequestRepository.save(changeRequest);

    this.logger.log(`Change request approved: ${id} by ${userId}`);

    return plainToClass(ChangeRequestResponseDto, updatedChangeRequest, {
      excludeExtraneousValues: true,
    });
  }

  /**
   * Reject change request
   */
  async reject(
    id: string,
    rejectDto: RejectChangeRequestDto,
    userId: string,
  ): Promise<ChangeRequestResponseDto> {
    const changeRequest = await this.findChangeRequestById(id);

    // Check permissions
    await this.validateChangeRequestAccess(changeRequest, userId);

    // Update rejection fields
    changeRequest.status = ChangeRequestStatus.REJECTED;
    changeRequest.rejectedAt = new Date();
    changeRequest.rejectionReason = rejectDto.rejectionReason;

    changeRequest.version += 1;

    const updatedChangeRequest = await this.changeRequestRepository.save(changeRequest);

    this.logger.log(`Change request rejected: ${id} by ${userId}`);

    return plainToClass(ChangeRequestResponseDto, updatedChangeRequest, {
      excludeExtraneousValues: true,
    });
  }

  /**
   * Bulk update change requests
   */
  async bulkUpdate(
    bulkUpdateDto: BulkUpdateChangeRequestDto,
    userId: string,
  ): Promise<ChangeRequestListResponseDto> {
    this.logger.log(`Bulk updating ${bulkUpdateDto.changeRequestIds.length} change requests`);

    const changeRequests = await this.changeRequestRepository.find({
      where: { id: In(bulkUpdateDto.changeRequestIds) },
      relations: ['project'],
    });

    if (changeRequests.length !== bulkUpdateDto.changeRequestIds.length) {
      throw new NotFoundException('Some change requests were not found');
    }

    // Validate access to all change requests
    for (const changeRequest of changeRequests) {
      await this.validateChangeRequestAccess(changeRequest, userId);
    }

    // Apply bulk updates
    const updates: Partial<ChangeRequest> = {};

    if (bulkUpdateDto.status) {
      updates.status = bulkUpdateDto.status;
    }

    if (bulkUpdateDto.priority) {
      updates.priority = bulkUpdateDto.priority;
    }

    if (bulkUpdateDto.assignedToId) {
      await this.validateUserExists(bulkUpdateDto.assignedToId);
      updates.assignedToId = bulkUpdateDto.assignedToId;
    }

    // Update all change requests
    await this.changeRequestRepository.update(
      { id: In(bulkUpdateDto.changeRequestIds) },
      updates,
    );

    // Fetch updated change requests
    const updatedChangeRequests = await this.changeRequestRepository.find({
      where: { id: In(bulkUpdateDto.changeRequestIds) },
    });

    this.logger.log(`Bulk update completed for ${updatedChangeRequests.length} change requests`);

    return {
      items: updatedChangeRequests.map(item =>
        plainToClass(ChangeRequestResponseDto, item, {
          excludeExtraneousValues: true,
        })
      ),
      total: updatedChangeRequests.length,
      page: 1,
      limit: updatedChangeRequests.length,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    };
  }

  /**
   * Delete change request
   */
  async delete(id: string, userId: string): Promise<void> {
    const changeRequest = await this.findChangeRequestById(id);

    // Check permissions
    await this.validateChangeRequestAccess(changeRequest, userId);

    // Only allow deletion of draft or cancelled requests
    if (![ChangeRequestStatus.DRAFT, ChangeRequestStatus.CANCELLED].includes(changeRequest.status)) {
      throw new BadRequestException('Only draft or cancelled change requests can be deleted');
    }

    await this.changeRequestRepository.remove(changeRequest);

    this.logger.log(`Change request deleted: ${id}`);
  }

  /**
   * Get change request statistics
   */
  async getStatistics(
    userId: string,
    options: {
      projectId?: string;
      dateFrom?: string;
      dateTo?: string;
    } = {},
  ): Promise<ChangeRequestStatisticsDto> {
    const { projectId, dateFrom, dateTo } = options;

    const queryBuilder = this.changeRequestRepository
      .createQueryBuilder('cr')
      .leftJoin('cr.project', 'project');

    // Apply filters
    if (projectId) {
      queryBuilder.andWhere('cr.projectId = :projectId', { projectId });
    }

    if (dateFrom) {
      queryBuilder.andWhere('cr.createdAt >= :dateFrom', { dateFrom });
    }

    if (dateTo) {
      queryBuilder.andWhere('cr.createdAt <= :dateTo', { dateTo });
    }

    const changeRequests = await queryBuilder.getMany();

    // Calculate statistics
    const totalChangeRequests = changeRequests.length;

    const byStatus = changeRequests.reduce((acc, cr) => {
      acc[cr.status] = (acc[cr.status] || 0) + 1;
      return acc;
    }, {} as Record<ChangeRequestStatus, number>);

    const byPriority = changeRequests.reduce((acc, cr) => {
      acc[cr.priority] = (acc[cr.priority] || 0) + 1;
      return acc;
    }, {} as Record<ChangeRequestPriority, number>);

    const byType = changeRequests.reduce((acc, cr) => {
      acc[cr.type] = (acc[cr.type] || 0) + 1;
      return acc;
    }, {} as Record<ChangeRequestType, number>);

    const completedRequests = changeRequests.filter(cr => cr.status === ChangeRequestStatus.COMPLETED);
    const totalEstimatedCost = changeRequests.reduce((sum, cr) => sum + (cr.estimatedCost || 0), 0);
    const totalActualCost = changeRequests.reduce((sum, cr) => sum + (cr.actualCost || 0), 0);

    const overdueCount = changeRequests.filter(cr => {
      if (!cr.requestedCompletionDate) return false;
      return new Date() > cr.requestedCompletionDate &&
             ![ChangeRequestStatus.COMPLETED, ChangeRequestStatus.CANCELLED, ChangeRequestStatus.REJECTED].includes(cr.status);
    }).length;

    const completionRate = totalChangeRequests > 0 ? (completedRequests.length / totalChangeRequests) * 100 : 0;
    const approvedRequests = changeRequests.filter(cr => cr.status === ChangeRequestStatus.APPROVED || cr.status === ChangeRequestStatus.COMPLETED);
    const approvalRate = totalChangeRequests > 0 ? (approvedRequests.length / totalChangeRequests) * 100 : 0;

    return plainToClass(ChangeRequestStatisticsDto, {
      totalChangeRequests,
      byStatus,
      byPriority,
      byType,
      byComplexity: {}, // TODO: Calculate complexity stats
      averageCompletionDays: 0, // TODO: Calculate average completion time
      totalEstimatedCost,
      totalActualCost,
      costVariancePercentage: totalEstimatedCost > 0 ? ((totalActualCost - totalEstimatedCost) / totalEstimatedCost) * 100 : 0,
      overdueCount,
      completionRate,
      approvalRate,
      averageApprovalTime: 0, // TODO: Calculate average approval time
      periodStart: dateFrom || new Date(0).toISOString(),
      periodEnd: dateTo || new Date().toISOString(),
    }, {
      excludeExtraneousValues: true,
    });
  }

  private validateStatusTransition(currentStatus: ChangeRequestStatus, newStatus: ChangeRequestStatus): void {
    const validTransitions: Record<ChangeRequestStatus, ChangeRequestStatus[]> = {
      [ChangeRequestStatus.DRAFT]: [ChangeRequestStatus.SUBMITTED, ChangeRequestStatus.CANCELLED],
      [ChangeRequestStatus.SUBMITTED]: [ChangeRequestStatus.UNDER_REVIEW, ChangeRequestStatus.CANCELLED],
      [ChangeRequestStatus.UNDER_REVIEW]: [ChangeRequestStatus.IMPACT_ASSESSMENT, ChangeRequestStatus.REJECTED, ChangeRequestStatus.CANCELLED],
      [ChangeRequestStatus.IMPACT_ASSESSMENT]: [ChangeRequestStatus.PENDING_APPROVAL, ChangeRequestStatus.REJECTED, ChangeRequestStatus.CANCELLED],
      [ChangeRequestStatus.PENDING_APPROVAL]: [ChangeRequestStatus.APPROVED, ChangeRequestStatus.REJECTED, ChangeRequestStatus.ON_HOLD],
      [ChangeRequestStatus.APPROVED]: [ChangeRequestStatus.IN_PROGRESS, ChangeRequestStatus.ON_HOLD],
      [ChangeRequestStatus.IN_PROGRESS]: [ChangeRequestStatus.COMPLETED, ChangeRequestStatus.ON_HOLD, ChangeRequestStatus.CANCELLED],
      [ChangeRequestStatus.ON_HOLD]: [ChangeRequestStatus.IN_PROGRESS, ChangeRequestStatus.CANCELLED],
      [ChangeRequestStatus.REJECTED]: [],
      [ChangeRequestStatus.COMPLETED]: [],
      [ChangeRequestStatus.CANCELLED]: [],
    };

    const allowedTransitions = validTransitions[currentStatus] || [];

    if (!allowedTransitions.includes(newStatus)) {
      throw new BadRequestException(
        `Invalid status transition from ${currentStatus} to ${newStatus}`
      );
    }
  }
}
