import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { ChangeRequestService } from '../change-request.service';
import { ChangeRequest } from '../../entities/change-request.entity';
import { ImpactAssessment } from '../../entities/impact-assessment.entity';
import { ApprovalWorkflow } from '../../entities/approval-workflow.entity';
import {
  ChangeRequestStatus,
  ChangeRequestType,
  ChangeRequestPriority,
  ChangeRequestComplexity,
  ChangeRequestImpactArea,
  ChangeRequestSource,
} from '../../enums';
import { Project } from '../../../projects/entities/project.entity';
import { User } from '../../../users/entities/user.entity';

/**
 * Basic Change Request Service Unit Tests
 * Sprint 7: Change Request Management System
 * 
 * Simplified unit tests for the ChangeRequestService
 */
describe('ChangeRequestService - Basic Tests', () => {
  let service: ChangeRequestService;
  let changeRequestRepository: Repository<ChangeRequest>;

  // Mock data
  const mockChangeRequest = {
    id: 'change-123',
    title: 'Test Change Request',
    description: 'Test description',
    type: ChangeRequestType.FEATURE_ADDITION,
    priority: ChangeRequestPriority.MEDIUM,
    complexity: ChangeRequestComplexity.MODERATE,
    status: ChangeRequestStatus.DRAFT,
    projectId: 'project-123',
    requesterId: 'user-123',
    impactAreas: [ChangeRequestImpactArea.FRONTEND],
    businessJustification: 'Business need',
    technicalDetails: 'Technical details',
    estimatedCost: 5000,
    estimatedHours: 40,
    version: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Mock repositories
  const mockChangeRequestRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  };

  const mockImpactAssessmentRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockWorkflowRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  // Mock external dependencies
  const mockProjectRepository = {
    findOne: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChangeRequestService,
        {
          provide: getRepositoryToken(ChangeRequest),
          useValue: mockChangeRequestRepository,
        },
        {
          provide: getRepositoryToken(ImpactAssessment),
          useValue: mockImpactAssessmentRepository,
        },
        {
          provide: getRepositoryToken(ApprovalWorkflow),
          useValue: mockWorkflowRepository,
        },
        {
          provide: getRepositoryToken(Project),
          useValue: mockProjectRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<ChangeRequestService>(ChangeRequestService);
    changeRequestRepository = module.get<Repository<ChangeRequest>>(
      getRepositoryToken(ChangeRequest),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should have repository injected', () => {
      expect(changeRequestRepository).toBeDefined();
    });
  });

  describe('Basic CRUD Operations', () => {
    it('should create a change request', async () => {
      // Arrange
      const createDto = {
        title: 'New Change Request',
        description: 'New description',
        type: ChangeRequestType.FEATURE_ADDITION,
        priority: ChangeRequestPriority.HIGH,
        complexity: ChangeRequestComplexity.MAJOR,
        source: ChangeRequestSource.CLIENT_REQUEST,
        projectId: 'project-123',
        impactAreas: [ChangeRequestImpactArea.FRONTEND],
        businessJustification: 'Business need',
        technicalDetails: 'Technical details',
        estimatedCost: 10000,
        estimatedHours: 80,
      };

      mockProjectRepository.findOne.mockResolvedValue({ id: 'project-123', clientId: 'user-123' });
      mockChangeRequestRepository.create.mockReturnValue(mockChangeRequest);
      mockChangeRequestRepository.save.mockResolvedValue(mockChangeRequest);

      // Act
      const result = await service.create(createDto, 'user-123');

      // Assert
      expect(mockProjectRepository.findOne).toHaveBeenCalled();
      expect(mockChangeRequestRepository.create).toHaveBeenCalled();
      expect(mockChangeRequestRepository.save).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it('should find a change request by id', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue({
        ...mockChangeRequest,
        project: { clientId: 'user-123' },
      });

      // Act
      const result = await service.findById('change-123', 'user-123');

      // Assert
      expect(mockChangeRequestRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'change-123' },
        relations: ['project', 'requester', 'assignedTo', 'approvedBy'],
      });
      expect(result).toBeDefined();
      expect(result.id).toBe('change-123');
    });

    it('should throw NotFoundException when change request not found', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findById('non-existent', 'user-123')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should update a change request', async () => {
      // Arrange
      const updateDto = {
        title: 'Updated Title',
        description: 'Updated description',
        priority: ChangeRequestPriority.HIGH,
        version: 1,
      };

      const existingChangeRequest = {
        ...mockChangeRequest,
        project: { clientId: 'user-123' },
      };

      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);
      mockChangeRequestRepository.save.mockResolvedValue({
        ...existingChangeRequest,
        ...updateDto,
        version: 2,
      });

      // Act
      const result = await service.update('change-123', updateDto, 'user-123');

      // Assert
      expect(mockChangeRequestRepository.findOne).toHaveBeenCalled();
      expect(mockChangeRequestRepository.save).toHaveBeenCalled();
      expect(result.title).toBe(updateDto.title);
    });
  });

  describe('Status Management', () => {
    it('should update change request status', async () => {
      // Arrange
      const statusDto = {
        status: ChangeRequestStatus.SUBMITTED,
        reason: 'Ready for review',
      };

      const existingChangeRequest = {
        ...mockChangeRequest,
        project: { clientId: 'user-123' },
      };

      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);
      mockChangeRequestRepository.save.mockResolvedValue({
        ...existingChangeRequest,
        status: statusDto.status,
        submittedAt: new Date(),
      });

      // Act
      const result = await service.updateStatus('change-123', statusDto, 'user-123');

      // Assert
      expect(result.status).toBe(statusDto.status);
      expect(result.submittedAt).toBeDefined();
    });
  });

  describe('Query Operations', () => {
    it('should find all change requests with pagination', async () => {
      // Arrange
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockChangeRequest]),
        getManyAndCount: jest.fn().mockResolvedValue([[mockChangeRequest], 1]),
      };

      mockChangeRequestRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Act
      const result = await service.findAll('user-123', {
        page: 1,
        limit: 20,
        status: ChangeRequestStatus.DRAFT,
      });

      // Assert
      expect(result.items).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(20);
    });

    it('should get statistics', async () => {
      // Arrange
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockChangeRequest]),
        getRawMany: jest.fn().mockResolvedValue([
          { status: 'draft', count: '5' },
          { status: 'submitted', count: '3' },
        ]),
      };

      mockChangeRequestRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Act
      const result = await service.getStatistics('user-123', {});

      // Assert
      expect(result).toBeDefined();
      expect(result.totalChangeRequests).toBeDefined();
      expect(result.byStatus).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle repository errors gracefully', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.findById('change-123', 'user-123')).rejects.toThrow('Database error');
    });
  });
});
