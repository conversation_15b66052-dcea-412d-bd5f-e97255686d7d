import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

/**
 * Change Management API Documentation Configuration
 * Sprint 7: Change Request Management System
 * 
 * Comprehensive Swagger/OpenAPI documentation for all change management endpoints
 */
export function setupChangeManagementDocs(app: INestApplication): void {
  const config = new DocumentBuilder()
    .setTitle('Stria Change Management API')
    .setDescription(`
# Change Management System API

## Overview
The Stria Change Management System provides a comprehensive solution for managing change requests in software development projects. This API enables intelligent impact assessment, automated workflow processing, multi-channel notifications, and external system integrations.

## Key Features
- 🔄 **Complete Change Request Lifecycle Management**
- 🤖 **AI-Powered Impact Assessment**
- ⚡ **Automated Workflow Processing**
- 📢 **Multi-Channel Notifications**
- 🔗 **External System Integrations**
- 📊 **Advanced Analytics & Reporting**

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
\`\`\`
Authorization: Bearer <your-jwt-token>
\`\`\`

## Rate Limiting
API requests are rate-limited to 100 requests per 15-minute window per user.

## Error Handling
The API uses standard HTTP status codes and returns detailed error information:

### Success Codes
- \`200 OK\` - Request successful
- \`201 Created\` - Resource created successfully
- \`204 No Content\` - Request successful, no content returned

### Error Codes
- \`400 Bad Request\` - Invalid request data
- \`401 Unauthorized\` - Authentication required
- \`403 Forbidden\` - Insufficient permissions
- \`404 Not Found\` - Resource not found
- \`409 Conflict\` - Resource conflict (e.g., duplicate)
- \`422 Unprocessable Entity\` - Validation errors
- \`429 Too Many Requests\` - Rate limit exceeded
- \`500 Internal Server Error\` - Server error

### Error Response Format
\`\`\`json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "title",
      "message": "Title must be at least 3 characters long"
    }
  ],
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/change-requests"
}
\`\`\`

## Pagination
List endpoints support pagination with the following query parameters:
- \`page\` - Page number (default: 1)
- \`limit\` - Items per page (default: 20, max: 100)

### Pagination Response Format
\`\`\`json
{
  "items": [...],
  "total": 150,
  "page": 1,
  "limit": 20,
  "totalPages": 8,
  "hasNext": true,
  "hasPrev": false
}
\`\`\`

## Filtering and Sorting
Most list endpoints support filtering and sorting:

### Common Filters
- \`status\` - Filter by status
- \`priority\` - Filter by priority
- \`type\` - Filter by type
- \`assignedToId\` - Filter by assigned user
- \`dateFrom\` - Filter from date (ISO string)
- \`dateTo\` - Filter to date (ISO string)
- \`search\` - Text search in title/description

### Sorting
- \`sortBy\` - Field to sort by (default: createdAt)
- \`sortOrder\` - Sort direction: ASC or DESC (default: DESC)

## Webhooks
The system supports outbound webhooks for real-time notifications:

### Webhook Events
- \`change_request.created\`
- \`change_request.updated\`
- \`change_request.status_changed\`
- \`impact_assessment.completed\`
- \`workflow.started\`
- \`workflow.completed\`
- \`approval.required\`
- \`approval.overdue\`

### Webhook Payload Format
\`\`\`json
{
  "event": "change_request.created",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "data": {
    "changeRequest": {...},
    "user": {...},
    "metadata": {...}
  }
}
\`\`\`

## Data Export
Several endpoints support data export in multiple formats:
- \`CSV\` - Comma-separated values
- \`JSON\` - JavaScript Object Notation
- \`XML\` - Extensible Markup Language

## External Integrations
The API supports integration with external systems:

### Supported Systems
- **Jira** - Issue tracking and project management
- **GitHub** - Code repository and issue management
- **Slack** - Team communication and notifications
- **Custom Webhooks** - Third-party system integration

### Integration Configuration
External integrations can be configured through environment variables or the admin API.

## SDK and Libraries
Official SDKs are available for:
- JavaScript/TypeScript
- Python
- Java
- C#

## Support and Resources
- **API Reference**: This documentation
- **Postman Collection**: Available for download
- **Code Examples**: See individual endpoint documentation
- **Support**: Contact <EMAIL>
    `)
    .setVersion('1.0.0')
    .setContact(
      'Stria Development Team',
      'https://stria.com',
      '<EMAIL>'
    )
    .setLicense('MIT', 'https://opensource.org/licenses/MIT')
    .addServer('https://api.stria.com', 'Production Server')
    .addServer('https://staging-api.stria.com', 'Staging Server')
    .addServer('http://localhost:3000', 'Development Server')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth'
    )
    .addTag('Change Requests', 'Change request lifecycle management')
    .addTag('Impact Assessments', 'Intelligent impact assessment and analysis')
    .addTag('Approval Workflows', 'Workflow management and approval processing')
    .addTag('Change History', 'Audit trail and change tracking')
    .addTag('Analytics', 'Performance metrics and reporting')
    .addTag('Integrations', 'External system integrations')
    .addTag('Notifications', 'Notification management')
    .addTag('Administration', 'System administration and configuration')
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    include: [], // Include all modules
    deepScanRoutes: true,
    operationIdFactory: (controllerKey: string, methodKey: string) => {
      return `${controllerKey}_${methodKey}`;
    },
  });

  // Add custom schemas and examples
  addCustomSchemas(document);
  addResponseExamples(document);

  SwaggerModule.setup('api/docs/change-management', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
      docExpansion: 'none',
      defaultModelsExpandDepth: 2,
      defaultModelExpandDepth: 2,
      displayOperationId: true,
      tryItOutEnabled: true,
    },
    customSiteTitle: 'Stria Change Management API Documentation',
    customfavIcon: '/favicon.ico',
    customJs: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-bundle.min.js',
    ],
    customCssUrl: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui.min.css',
    ],
  });
}

/**
 * Add custom schemas to the OpenAPI document
 */
function addCustomSchemas(document: any): void {
  // Add common error schema
  document.components.schemas.ErrorResponse = {
    type: 'object',
    properties: {
      statusCode: {
        type: 'integer',
        example: 400,
      },
      message: {
        type: 'string',
        example: 'Validation failed',
      },
      error: {
        type: 'string',
        example: 'Bad Request',
      },
      details: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            field: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
      timestamp: {
        type: 'string',
        format: 'date-time',
      },
      path: {
        type: 'string',
        example: '/change-requests',
      },
    },
  };

  // Add pagination schema
  document.components.schemas.PaginationMeta = {
    type: 'object',
    properties: {
      total: { type: 'integer', example: 150 },
      page: { type: 'integer', example: 1 },
      limit: { type: 'integer', example: 20 },
      totalPages: { type: 'integer', example: 8 },
      hasNext: { type: 'boolean', example: true },
      hasPrev: { type: 'boolean', example: false },
    },
  };

  // Add webhook payload schema
  document.components.schemas.WebhookPayload = {
    type: 'object',
    properties: {
      event: {
        type: 'string',
        enum: [
          'change_request.created',
          'change_request.updated',
          'change_request.status_changed',
          'impact_assessment.completed',
          'workflow.started',
          'workflow.completed',
          'approval.required',
          'approval.overdue',
        ],
      },
      timestamp: {
        type: 'string',
        format: 'date-time',
      },
      data: {
        type: 'object',
        description: 'Event-specific data payload',
      },
    },
  };
}

/**
 * Add response examples to the OpenAPI document
 */
function addResponseExamples(document: any): void {
  // Add common response examples
  const examples = {
    ChangeRequestCreated: {
      summary: 'Change Request Created',
      value: {
        id: 'cr_1234567890',
        title: 'Add User Dashboard',
        description: 'Implement comprehensive user dashboard with analytics',
        type: 'feature_addition',
        priority: 'high',
        complexity: 'major',
        status: 'draft',
        projectId: 'proj_1234567890',
        requesterId: 'user_1234567890',
        impactAreas: ['frontend', 'backend', 'database'],
        businessJustification: 'Users need better visibility into their data',
        technicalDetails: 'React dashboard with real-time data visualization',
        estimatedCost: 15000,
        estimatedHours: 120,
        version: 1,
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-15T10:30:00.000Z',
      },
    },
    ImpactAssessmentCompleted: {
      summary: 'Impact Assessment Completed',
      value: {
        id: 'ia_1234567890',
        changeRequestId: 'cr_1234567890',
        status: 'completed',
        method: 'automated',
        overallImpact: 'medium',
        confidenceLevel: 'high',
        assessmentQuality: 'good',
        timeImpactCategory: 'moderate_delay',
        estimatedDelayDays: 7,
        costImpactCategory: 'moderate_cost',
        estimatedAdditionalCost: 3000,
        riskImpactCategory: 'medium_risk',
        assessmentSummary: 'Moderate impact with manageable risks',
        assessmentCompletedAt: '2024-01-15T10:35:00.000Z',
      },
    },
    WorkflowStarted: {
      summary: 'Approval Workflow Started',
      value: {
        id: 'wf_1234567890',
        changeRequestId: 'cr_1234567890',
        name: 'Sequential Approval Workflow',
        workflowType: 'sequential_approval',
        status: 'active',
        priority: 'high',
        currentStepOrder: 1,
        totalSteps: 3,
        completedSteps: 0,
        progressPercentage: 0,
        startedAt: '2024-01-15T10:40:00.000Z',
      },
    },
  };

  // Add examples to document
  if (!document.components.examples) {
    document.components.examples = {};
  }
  Object.assign(document.components.examples, examples);
}

/**
 * Generate API documentation in JSON format
 */
export function generateApiDocumentation(app: INestApplication): any {
  const config = new DocumentBuilder()
    .setTitle('Stria Change Management API')
    .setDescription('Comprehensive change management system API')
    .setVersion('1.0.0')
    .addBearerAuth()
    .build();

  return SwaggerModule.createDocument(app, config);
}

/**
 * Export API documentation to file
 */
export async function exportApiDocumentation(
  app: INestApplication,
  outputPath: string
): Promise<void> {
  const document = generateApiDocumentation(app);
  const fs = await import('fs');
  const path = await import('path');

  // Ensure directory exists
  const dir = path.dirname(outputPath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  // Write JSON documentation
  fs.writeFileSync(outputPath, JSON.stringify(document, null, 2));

  // Generate additional formats
  const baseName = path.basename(outputPath, '.json');
  const baseDir = path.dirname(outputPath);

  // Generate YAML format
  const yaml = await import('yaml');
  const yamlContent = yaml.stringify(document);
  fs.writeFileSync(path.join(baseDir, `${baseName}.yaml`), yamlContent);

  // Generate Markdown format
  const markdownContent = generateMarkdownDocumentation(document);
  fs.writeFileSync(path.join(baseDir, `${baseName}.md`), markdownContent);

  console.log(`API documentation exported to: ${outputPath}`);
  console.log(`Additional formats generated: YAML, Markdown`);
}

/**
 * Generate Markdown documentation from OpenAPI spec
 */
function generateMarkdownDocumentation(document: any): string {
  let markdown = `# ${document.info.title}\n\n`;
  markdown += `${document.info.description}\n\n`;
  markdown += `**Version:** ${document.info.version}\n\n`;

  // Add servers
  if (document.servers && document.servers.length > 0) {
    markdown += '## Servers\n\n';
    document.servers.forEach((server: any) => {
      markdown += `- **${server.description || 'Server'}**: ${server.url}\n`;
    });
    markdown += '\n';
  }

  // Add paths
  if (document.paths) {
    markdown += '## API Endpoints\n\n';
    Object.entries(document.paths).forEach(([path, methods]: [string, any]) => {
      markdown += `### ${path}\n\n`;
      Object.entries(methods).forEach(([method, operation]: [string, any]) => {
        markdown += `#### ${method.toUpperCase()}\n\n`;
        if (operation.summary) {
          markdown += `**Summary:** ${operation.summary}\n\n`;
        }
        if (operation.description) {
          markdown += `**Description:** ${operation.description}\n\n`;
        }
        if (operation.tags && operation.tags.length > 0) {
          markdown += `**Tags:** ${operation.tags.join(', ')}\n\n`;
        }
      });
    });
  }

  return markdown;
}
