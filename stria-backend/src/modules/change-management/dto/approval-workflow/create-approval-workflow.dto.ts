import {
  IsString,
  IsOptional,
  IsUUID,
  IsEnum,
  IsArray,
  IsNumber,
  IsDateString,
  IsBoolean,
  <PERSON><PERSON>ength,
  <PERSON><PERSON>ength,
  Min,
  <PERSON>,
  ArrayNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import {
  WorkflowType,
  WorkflowPriority,
  ApprovalUrgency,
  ApprovalStepType,
  ApprovalAuthority,
  ApprovalMethod,
  ApprovalCondition,
} from '../../enums';

/**
 * Create Approval Workflow DTO
 * Sprint 7: Change Request Management System
 * 
 * Used to create a new approval workflow for a change request
 */
export class CreateApprovalWorkflowDto {
  @IsUUID(4, { message: 'Invalid change request ID format' })
  changeRequestId: string;

  @IsString({ message: 'Workflow name must be a string' })
  @MinLength(3, { message: 'Workflow name must be at least 3 characters long' })
  @MaxLength(255, { message: 'Workflow name must not exceed 255 characters' })
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Description must not exceed 1000 characters' })
  description?: string;

  @IsEnum(WorkflowType, { message: 'Invalid workflow type' })
  workflowType: WorkflowType;

  @IsOptional()
  @IsEnum(WorkflowPriority, { message: 'Invalid workflow priority' })
  priority?: WorkflowPriority;

  @IsOptional()
  @IsEnum(ApprovalUrgency, { message: 'Invalid urgency level' })
  urgency?: ApprovalUrgency;

  @IsOptional()
  @IsDateString({}, { message: 'Deadline must be a valid date' })
  deadline?: string;

  @IsOptional()
  @IsNumber({}, { message: 'Estimated duration hours must be a number' })
  @Min(1, { message: 'Estimated duration must be at least 1 hour' })
  @Max(8760, { message: 'Estimated duration cannot exceed 1 year' })
  estimatedDurationHours?: number;

  @IsOptional()
  @IsNumber({}, { message: 'Auto approve after hours must be a number' })
  @Min(1, { message: 'Auto approve time must be at least 1 hour' })
  autoApproveAfterHours?: number;

  @IsOptional()
  @IsNumber({}, { message: 'Escalation after hours must be a number' })
  @Min(1, { message: 'Escalation time must be at least 1 hour' })
  escalationAfterHours?: number;

  @IsArray({ message: 'Steps must be an array' })
  @ArrayNotEmpty({ message: 'At least one approval step must be provided' })
  @ValidateNested({ each: true })
  @Type(() => CreateApprovalStepDto)
  steps: CreateApprovalStepDto[];

  @IsOptional()
  configuration?: {
    requireAllApprovals?: boolean;
    allowParallelApprovals?: boolean;
    enableEscalation?: boolean;
    enableDelegation?: boolean;
    enableAutoApproval?: boolean;
    notificationSettings?: {
      sendReminders?: boolean;
      reminderIntervalHours?: number;
      escalationNotifications?: boolean;
      completionNotifications?: boolean;
    };
    approvalConditions?: Array<{
      condition: ApprovalCondition;
      required: boolean;
      description?: string;
    }>;
    customRules?: Array<{
      ruleId: string;
      condition: string;
      action: string;
      parameters?: Record<string, any>;
    }>;
  };
}

/**
 * Create Approval Step DTO
 * Represents individual steps in the approval workflow
 */
export class CreateApprovalStepDto {
  @IsString({ message: 'Step name must be a string' })
  @MinLength(2, { message: 'Step name must be at least 2 characters long' })
  @MaxLength(255, { message: 'Step name must not exceed 255 characters' })
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Description must not exceed 1000 characters' })
  description?: string;

  @IsNumber({}, { message: 'Step order must be a number' })
  @Min(1, { message: 'Step order must be at least 1' })
  stepOrder: number;

  @IsEnum(ApprovalStepType, { message: 'Invalid step type' })
  stepType: ApprovalStepType;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid assigned to user ID format' })
  assignedToId?: string;

  @IsOptional()
  @IsEnum(ApprovalAuthority, { message: 'Invalid approval authority' })
  assignedRole?: ApprovalAuthority;

  @IsOptional()
  @IsDateString({}, { message: 'Due date must be a valid date' })
  dueDate?: string;

  @IsOptional()
  configuration?: {
    isRequired?: boolean;
    canDelegate?: boolean;
    canSkip?: boolean;
    requiresComment?: boolean;
    allowedDecisions?: string[];
    conditions?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
  };
}

/**
 * Quick Approval Workflow DTO
 * For creating simple approval workflows with predefined templates
 */
export class QuickApprovalWorkflowDto {
  @IsUUID(4, { message: 'Invalid change request ID format' })
  changeRequestId: string;

  @IsEnum(WorkflowType, { message: 'Invalid workflow type' })
  workflowType: WorkflowType;

  @IsArray({ message: 'Approvers must be an array' })
  @ArrayNotEmpty({ message: 'At least one approver must be provided' })
  @IsUUID(4, { each: true, message: 'Each approver ID must be a valid UUID' })
  approverIds: string[];

  @IsOptional()
  @IsEnum(WorkflowPriority, { message: 'Invalid workflow priority' })
  priority?: WorkflowPriority;

  @IsOptional()
  @IsDateString({}, { message: 'Deadline must be a valid date' })
  deadline?: string;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  requireAllApprovals?: boolean = false;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  enableEscalation?: boolean = true;

  @IsOptional()
  @IsNumber({}, { message: 'Escalation hours must be a number' })
  @Min(1, { message: 'Escalation time must be at least 1 hour' })
  escalationAfterHours?: number = 24;
}

/**
 * Workflow Template DTO
 * For creating workflows from predefined templates
 */
export class WorkflowTemplateDto {
  @IsUUID(4, { message: 'Invalid change request ID format' })
  changeRequestId: string;

  @IsString({ message: 'Template ID must be a string' })
  templateId: string;

  @IsOptional()
  templateParameters?: {
    approvers?: string[];
    deadline?: string;
    priority?: WorkflowPriority;
    customFields?: Record<string, any>;
  };

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  customizeSteps?: boolean = false;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateApprovalStepDto)
  additionalSteps?: CreateApprovalStepDto[];
}

/**
 * Bulk Workflow Creation DTO
 * For creating workflows for multiple change requests
 */
export class BulkWorkflowCreationDto {
  @IsArray({ message: 'Change request IDs must be an array' })
  @ArrayNotEmpty({ message: 'At least one change request ID must be provided' })
  @IsUUID(4, { each: true, message: 'Each change request ID must be a valid UUID' })
  changeRequestIds: string[];

  @IsEnum(WorkflowType, { message: 'Invalid workflow type' })
  workflowType: WorkflowType;

  @IsString({ message: 'Workflow name template must be a string' })
  @MaxLength(255, { message: 'Workflow name template must not exceed 255 characters' })
  workflowNameTemplate: string;

  @IsArray({ message: 'Approvers must be an array' })
  @ArrayNotEmpty({ message: 'At least one approver must be provided' })
  @IsUUID(4, { each: true, message: 'Each approver ID must be a valid UUID' })
  approverIds: string[];

  @IsOptional()
  @IsEnum(WorkflowPriority, { message: 'Invalid workflow priority' })
  priority?: WorkflowPriority;

  @IsOptional()
  @IsDateString({}, { message: 'Deadline must be a valid date' })
  deadline?: string;

  @IsOptional()
  configuration?: {
    requireAllApprovals?: boolean;
    enableEscalation?: boolean;
    escalationAfterHours?: number;
    enableAutoApproval?: boolean;
    autoApproveAfterHours?: number;
  };

  @IsOptional()
  @IsString()
  @MaxLength(255, { message: 'Batch name must not exceed 255 characters' })
  batchName?: string;
}

/**
 * Conditional Workflow DTO
 * For creating workflows with conditional logic
 */
export class ConditionalWorkflowDto {
  @IsUUID(4, { message: 'Invalid change request ID format' })
  changeRequestId: string;

  @IsString({ message: 'Workflow name must be a string' })
  @MaxLength(255, { message: 'Workflow name must not exceed 255 characters' })
  name: string;

  @IsArray({ message: 'Conditions must be an array' })
  @ArrayNotEmpty({ message: 'At least one condition must be provided' })
  conditions: Array<{
    field: string;
    operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in';
    value: any;
    stepId?: string;
    approverIds?: string[];
    skipStep?: boolean;
    escalate?: boolean;
  }>;

  @IsArray({ message: 'Default steps must be an array' })
  @ValidateNested({ each: true })
  @Type(() => CreateApprovalStepDto)
  defaultSteps: CreateApprovalStepDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateApprovalStepDto)
  conditionalSteps?: CreateApprovalStepDto[];

  @IsOptional()
  configuration?: {
    evaluateConditionsOnStart?: boolean;
    reevaluateOnStepCompletion?: boolean;
    allowDynamicStepCreation?: boolean;
  };
}
