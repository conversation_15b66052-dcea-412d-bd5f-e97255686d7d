import { Expose, Transform, Type } from 'class-transformer';
import {
  WorkflowStatus,
  WorkflowType,
  WorkflowPriority,
  ApprovalUrgency,
  ApprovalStepStatus,
  ApprovalStepType,
  ApprovalDecision as ApprovalDecisionEnum,
  ApprovalAuthority,
} from '../../enums';

/**
 * Approval Workflow Response DTO
 * Sprint 7: Change Request Management System
 * 
 * Used to return approval workflow data to clients
 */
export class ApprovalWorkflowResponseDto {
  @Expose()
  id: string;

  @Expose()
  changeRequestId: string;

  @Expose()
  name: string;

  @Expose()
  description?: string;

  @Expose()
  workflowType: WorkflowType;

  @Expose()
  status: WorkflowStatus;

  @Expose()
  priority: WorkflowPriority;

  @Expose()
  urgency: ApprovalUrgency;

  @Expose()
  createdById: string;

  @Expose()
  currentApproverId?: string;

  @Expose()
  @Transform(({ value }) => value ? new Date(value).toISOString() : null)
  deadline?: string;

  @Expose()
  estimatedDurationHours?: number;

  @Expose()
  autoApproveAfterHours?: number;

  @Expose()
  escalationAfterHours?: number;

  @Expose()
  currentStepOrder: number;

  @Expose()
  totalSteps: number;

  @Expose()
  completedSteps: number;

  @Expose()
  finalDecision?: string;

  @Expose()
  finalDecisionReason?: string;

  @Expose()
  finalApproverId?: string;

  @Expose()
  @Transform(({ value }) => value ? new Date(value).toISOString() : null)
  startedAt?: string;

  @Expose()
  @Transform(({ value }) => value ? new Date(value).toISOString() : null)
  completedAt?: string;

  @Expose()
  @Transform(({ value }) => value ? new Date(value).toISOString() : null)
  cancelledAt?: string;

  @Expose()
  @Transform(({ value }) => value ? new Date(value).toISOString() : null)
  lastActivityAt?: string;

  @Expose()
  configuration: Record<string, any>;

  @Expose()
  metadata: Record<string, any>;

  @Expose()
  version: number;

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  createdAt: string;

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  updatedAt: string;

  @Expose()
  @Type(() => ApprovalStepResponseDto)
  steps?: ApprovalStepResponseDto[];

  // Computed properties
  @Expose()
  get isActive(): boolean {
    return this.status === WorkflowStatus.ACTIVE;
  }

  @Expose()
  get isCompleted(): boolean {
    return this.status === WorkflowStatus.COMPLETED;
  }

  @Expose()
  get isCancelled(): boolean {
    return this.status === WorkflowStatus.CANCELLED;
  }

  @Expose()
  get isOverdue(): boolean {
    if (!this.deadline) return false;
    return new Date() > new Date(this.deadline) && !this.isCompleted && !this.isCancelled;
  }

  @Expose()
  get progressPercentage(): number {
    if (this.totalSteps === 0) return 0;
    return Math.round((this.completedSteps / this.totalSteps) * 100);
  }

  @Expose()
  get durationMinutes(): number | null {
    if (!this.startedAt) return null;
    const endTime = this.completedAt ? new Date(this.completedAt) : new Date();
    const startTime = new Date(this.startedAt);
    return Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
  }

  @Expose()
  get hoursUntilDeadline(): number | null {
    if (!this.deadline) return null;
    const now = new Date();
    const deadline = new Date(this.deadline);
    const diffMs = deadline.getTime() - now.getTime();
    return Math.round(diffMs / (1000 * 60 * 60));
  }

  @Expose()
  get requiresEscalation(): boolean {
    if (!this.escalationAfterHours || !this.lastActivityAt) return false;
    const hoursSinceLastActivity = (new Date().getTime() - new Date(this.lastActivityAt).getTime()) / (1000 * 60 * 60);
    return hoursSinceLastActivity >= this.escalationAfterHours;
  }
}

/**
 * Approval Step Response DTO
 * Represents individual steps in the approval workflow
 */
export class ApprovalStepResponseDto {
  @Expose()
  id: string;

  @Expose()
  workflowId: string;

  @Expose()
  name: string;

  @Expose()
  description?: string;

  @Expose()
  stepOrder: number;

  @Expose()
  status: ApprovalStepStatus;

  @Expose()
  stepType: ApprovalStepType;

  @Expose()
  assignedToId?: string;

  @Expose()
  assignedRole?: string;

  @Expose()
  @Transform(({ value }) => value ? new Date(value).toISOString() : null)
  dueDate?: string;

  @Expose()
  @Transform(({ value }) => value ? new Date(value).toISOString() : null)
  startedAt?: string;

  @Expose()
  @Transform(({ value }) => value ? new Date(value).toISOString() : null)
  completedAt?: string;

  @Expose()
  configuration: Record<string, any>;

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  createdAt: string;

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  updatedAt: string;

  @Expose()
  @Type(() => ApprovalDecisionResponseDto)
  decisions?: ApprovalDecisionResponseDto[];

  @Expose()
  get isOverdue(): boolean {
    if (!this.dueDate) return false;
    return new Date() > new Date(this.dueDate) && 
           ![ApprovalStepStatus.APPROVED, ApprovalStepStatus.REJECTED, ApprovalStepStatus.SKIPPED].includes(this.status);
  }

  @Expose()
  get durationMinutes(): number | null {
    if (!this.startedAt) return null;
    const endTime = this.completedAt ? new Date(this.completedAt) : new Date();
    const startTime = new Date(this.startedAt);
    return Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
  }
}

/**
 * Approval Decision Response DTO
 * Represents decisions made at each approval step
 */
export class ApprovalDecisionResponseDto {
  @Expose()
  id: string;

  @Expose()
  stepId: string;

  @Expose()
  decision: ApprovalDecisionEnum;

  @Expose()
  comments?: string;

  @Expose()
  conditions?: string;

  @Expose()
  decidedById: string;

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  decidedAt: string;

  @Expose()
  delegatedToId?: string;

  @Expose()
  delegationReason?: string;

  @Expose()
  metadata: Record<string, any>;

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  createdAt: string;

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  updatedAt: string;
}

/**
 * Workflow Summary DTO
 * For dashboard and overview displays
 */
export class WorkflowSummaryDto {
  @Expose()
  id: string;

  @Expose()
  changeRequestId: string;

  @Expose()
  name: string;

  @Expose()
  workflowType: WorkflowType;

  @Expose()
  status: WorkflowStatus;

  @Expose()
  priority: WorkflowPriority;

  @Expose()
  currentStepOrder: number;

  @Expose()
  totalSteps: number;

  @Expose()
  completedSteps: number;

  @Expose()
  currentApproverId?: string;

  @Expose()
  @Transform(({ value }) => value ? new Date(value).toISOString() : null)
  deadline?: string;

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  createdAt: string;

  @Expose()
  get progressPercentage(): number {
    if (this.totalSteps === 0) return 0;
    return Math.round((this.completedSteps / this.totalSteps) * 100);
  }

  @Expose()
  get isOverdue(): boolean {
    if (!this.deadline) return false;
    return new Date() > new Date(this.deadline) && 
           ![WorkflowStatus.COMPLETED, WorkflowStatus.CANCELLED].includes(this.status);
  }
}

/**
 * Workflow List Response DTO
 * For paginated lists of workflows
 */
export class WorkflowListResponseDto {
  @Expose()
  @Type(() => ApprovalWorkflowResponseDto)
  items: ApprovalWorkflowResponseDto[];

  @Expose()
  total: number;

  @Expose()
  page: number;

  @Expose()
  limit: number;

  @Expose()
  totalPages: number;

  @Expose()
  hasNext: boolean;

  @Expose()
  hasPrev: boolean;

  @Expose()
  summary?: {
    byStatus: Record<WorkflowStatus, number>;
    byType: Record<WorkflowType, number>;
    byPriority: Record<WorkflowPriority, number>;
    averageCompletionTime: number;
    overdueCount: number;
    pendingApprovalCount: number;
    completionRate: number;
  };
}

/**
 * Workflow Analytics DTO
 * For workflow performance analytics
 */
export class WorkflowAnalyticsDto {
  @Expose()
  totalWorkflows: number;

  @Expose()
  byStatus: Record<WorkflowStatus, number>;

  @Expose()
  byType: Record<WorkflowType, number>;

  @Expose()
  byPriority: Record<WorkflowPriority, number>;

  @Expose()
  averageCompletionTime: number;

  @Expose()
  averageStepsPerWorkflow: number;

  @Expose()
  completionRate: number;

  @Expose()
  escalationRate: number;

  @Expose()
  approvalRate: number;

  @Expose()
  rejectionRate: number;

  @Expose()
  bottleneckAnalysis: Array<{
    stepType: ApprovalStepType;
    averageTime: number;
    delayCount: number;
    authority: ApprovalAuthority;
  }>;

  @Expose()
  performanceMetrics: {
    fastestWorkflow: number;
    slowestWorkflow: number;
    mostEfficientType: WorkflowType;
    leastEfficientType: WorkflowType;
  };

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  periodStart: string;

  @Expose()
  @Transform(({ value }) => new Date(value).toISOString())
  periodEnd: string;
}
