import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChangeRequest } from '../entities/change-request.entity';
import { ImpactAssessment } from '../entities/impact-assessment.entity';
import { ApprovalWorkflow } from '../entities/approval-workflow.entity';
import { ChangeHistory } from '../entities/change-history.entity';
import {
  ChangeRequestStatus,
  ChangeRequestPriority,
  ChangeRequestType,
  ImpactAssessmentStatus,
  ImpactAssessmentMethod,
  WorkflowStatus,
  WorkflowType
} from '../enums';

describe('Change Management Simple Integration (e2e)', () => {
  let module: TestingModule;
  let changeRequestRepository: Repository<ChangeRequest>;
  let impactAssessmentRepository: Repository<ImpactAssessment>;
  let approvalWorkflowRepository: Repository<ApprovalWorkflow>;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: 'localhost',
          port: 5433, // Test database port from docker-compose.test.yml
          username: 'postgres',
          password: 'password',
          database: 'stria_test',
          entities: [
            ChangeRequest,
            ImpactAssessment,
            ApprovalWorkflow,
            ChangeHistory,
          ],
          synchronize: true,
          logging: false,
          dropSchema: true, // Clean database for each test run
        }),
        TypeOrmModule.forFeature([
          ChangeRequest,
          ImpactAssessment,
          ApprovalWorkflow,
          ChangeHistory,
        ]),
      ],
    }).compile();

    changeRequestRepository = module.get<Repository<ChangeRequest>>(getRepositoryToken(ChangeRequest));
    impactAssessmentRepository = module.get<Repository<ImpactAssessment>>(getRepositoryToken(ImpactAssessment));
    approvalWorkflowRepository = module.get<Repository<ApprovalWorkflow>>(getRepositoryToken(ApprovalWorkflow));
  });

  afterAll(async () => {
    if (module) {
      await module.close();
    }
  });

  beforeEach(async () => {
    // Clean up data before each test
    await changeRequestRepository.clear();
    await impactAssessmentRepository.clear();
    await approvalWorkflowRepository.clear();
  });

  describe('Change Request Core Operations', () => {
    it('should create a change request with minimal data', async () => {
      const createDto = {
        title: 'Test Change Request',
        description: 'This is a test change request',
        type: ChangeRequestType.FEATURE_ADDITION,
        priority: ChangeRequestPriority.MEDIUM,
        projectId: 'project-123',
        requesterId: 'user-123',
      };

      const changeRequest = changeRequestRepository.create({
        ...createDto,
        status: ChangeRequestStatus.DRAFT,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const savedChangeRequest = await changeRequestRepository.save(changeRequest);

      expect(savedChangeRequest).toBeDefined();
      expect(savedChangeRequest.id).toBeDefined();
      expect(savedChangeRequest.title).toBe(createDto.title);
      expect(savedChangeRequest.status).toBe(ChangeRequestStatus.DRAFT);
    });

    it('should update change request status', async () => {
      // Create a change request
      const changeRequest = changeRequestRepository.create({
        title: 'Test Change Request',
        description: 'Test description',
        type: ChangeRequestType.FEATURE_ADDITION,
        priority: ChangeRequestPriority.MEDIUM,
        projectId: 'project-123',
        requesterId: 'user-123',
        status: ChangeRequestStatus.DRAFT,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const savedChangeRequest = await changeRequestRepository.save(changeRequest);

      // Update status
      savedChangeRequest.status = ChangeRequestStatus.PENDING_APPROVAL;
      savedChangeRequest.updatedAt = new Date();
      const updatedChangeRequest = await changeRequestRepository.save(savedChangeRequest);

      expect(updatedChangeRequest.status).toBe(ChangeRequestStatus.PENDING_APPROVAL);
    });
  });

  describe('Impact Assessment Core Operations', () => {
    it('should create an impact assessment', async () => {
      // First create a change request
      const changeRequest = changeRequestRepository.create({
        title: 'Test Change Request',
        description: 'Test description',
        type: ChangeRequestType.FEATURE_ADDITION,
        priority: ChangeRequestPriority.MEDIUM,
        projectId: 'project-123',
        requesterId: 'user-123',
        status: ChangeRequestStatus.DRAFT,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const savedChangeRequest = await changeRequestRepository.save(changeRequest);

      // Create impact assessment
      const assessment = impactAssessmentRepository.create({
        changeRequestId: savedChangeRequest.id,
        method: ImpactAssessmentMethod.AUTOMATED,
        status: ImpactAssessmentStatus.IN_PROGRESS,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const savedAssessment = await impactAssessmentRepository.save(assessment);

      expect(savedAssessment).toBeDefined();
      expect(savedAssessment.id).toBeDefined();
      expect(savedAssessment.changeRequestId).toBe(savedChangeRequest.id);
      expect(savedAssessment.status).toBe(ImpactAssessmentStatus.IN_PROGRESS);
    });
  });

  describe('Approval Workflow Core Operations', () => {
    it('should create an approval workflow', async () => {
      // First create a change request
      const changeRequest = changeRequestRepository.create({
        title: 'Test Change Request',
        description: 'Test description',
        type: ChangeRequestType.FEATURE_ADDITION,
        priority: ChangeRequestPriority.MEDIUM,
        projectId: 'project-123',
        requesterId: 'user-123',
        status: ChangeRequestStatus.DRAFT,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const savedChangeRequest = await changeRequestRepository.save(changeRequest);

      // Create approval workflow
      const workflow = approvalWorkflowRepository.create({
        changeRequestId: savedChangeRequest.id,
        workflowType: WorkflowType.SEQUENTIAL_APPROVAL,
        status: WorkflowStatus.ACTIVE,
        createdById: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const savedWorkflow = await approvalWorkflowRepository.save(workflow);

      expect(savedWorkflow).toBeDefined();
      expect(savedWorkflow.id).toBeDefined();
      expect(savedWorkflow.changeRequestId).toBe(savedChangeRequest.id);
      expect(savedWorkflow.status).toBe(WorkflowStatus.ACTIVE);
    });
  });

  describe('Repository Operations', () => {
    it('should find change requests by status', async () => {
      // Create multiple change requests with different statuses
      const changeRequest1 = changeRequestRepository.create({
        title: 'Draft Change Request',
        description: 'Test description',
        type: ChangeRequestType.FEATURE_ADDITION,
        priority: ChangeRequestPriority.MEDIUM,
        projectId: 'project-123',
        requesterId: 'user-123',
        status: ChangeRequestStatus.DRAFT,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const changeRequest2 = changeRequestRepository.create({
        title: 'Pending Change Request',
        description: 'Test description',
        type: ChangeRequestType.BUG_FIX,
        priority: ChangeRequestPriority.HIGH,
        projectId: 'project-123',
        requesterId: 'user-123',
        status: ChangeRequestStatus.PENDING_APPROVAL,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await changeRequestRepository.save([changeRequest1, changeRequest2]);

      // Find by status
      const draftRequests = await changeRequestRepository.find({
        where: { status: ChangeRequestStatus.DRAFT },
      });

      const pendingRequests = await changeRequestRepository.find({
        where: { status: ChangeRequestStatus.PENDING_APPROVAL },
      });

      expect(draftRequests).toHaveLength(1);
      expect(draftRequests[0].title).toBe('Draft Change Request');
      expect(pendingRequests).toHaveLength(1);
      expect(pendingRequests[0].title).toBe('Pending Change Request');
    });
  });
});
