import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePrototypeEntities1706000000000 implements MigrationInterface {
  name = 'CreatePrototypeEntities1706000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create prototype status enum
    await queryRunner.query(`
      CREATE TYPE "prototype_status_enum" AS ENUM(
        'draft', 'in_review', 'approved', 'rejected',
        'in_development', 'completed', 'archived'
      )
    `);

    // Create prototype type enum
    await queryRunner.query(`
      CREATE TYPE "prototype_type_enum" AS ENUM(
        'wireframe', 'low_fidelity', 'high_fidelity',
        'interactive', 'mobile', 'web', 'desktop'
      )
    `);

    // Create figma integration status enum
    await queryRunner.query(`
      CREATE TYPE "figma_integration_status_enum" AS ENUM(
        'not_connected', 'connecting', 'connected', 'sync_pending',
        'syncing', 'synced', 'sync_error', 'disconnected'
      )
    `);

    // Create version status enum
    await queryRunner.query(`
      CREATE TYPE "version_status_enum" AS ENUM(
        'draft', 'pending_review', 'in_review', 'approved',
        'rejected', 'merged', 'archived'
      )
    `);

    // Create version type enum
    await queryRunner.query(`
      CREATE TYPE "version_type_enum" AS ENUM(
        'major', 'minor', 'patch', 'hotfix',
        'feature', 'bugfix', 'experimental'
      )
    `);

    // Create feedback status enum
    await queryRunner.query(`
      CREATE TYPE "feedback_status_enum" AS ENUM(
        'open', 'in_progress', 'pending_review',
        'resolved', 'closed', 'rejected', 'deferred'
      )
    `);

    // Create feedback type enum
    await queryRunner.query(`
      CREATE TYPE "feedback_type_enum" AS ENUM(
        'bug', 'improvement', 'suggestion', 'question', 'praise',
        'design_issue', 'usability', 'accessibility', 'content', 'functionality'
      )
    `);

    // Create feedback priority enum
    await queryRunner.query(`
      CREATE TYPE "feedback_priority_enum" AS ENUM(
        'low', 'medium', 'high', 'critical', 'blocker'
      )
    `);

    // Create annotation type enum
    await queryRunner.query(`
      CREATE TYPE "annotation_type_enum" AS ENUM(
        'point', 'rectangle', 'circle', 'arrow', 'line',
        'highlight', 'text_box', 'sticky_note', 'freehand'
      )
    `);

    // Create prototypes table
    await queryRunner.query(`
      CREATE TABLE "prototypes" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "description" text,
        "type" "prototype_type_enum" NOT NULL DEFAULT 'low_fidelity',
        "status" "prototype_status_enum" NOT NULL DEFAULT 'draft',
        "figma_file_id" character varying(255),
        "figma_file_url" text,
        "figma_embed_url" text,
        "figma_integration_status" "figma_integration_status_enum" NOT NULL DEFAULT 'not_connected',
        "figma_last_sync" TIMESTAMP,
        "current_version" character varying(50) NOT NULL DEFAULT '1.0.0',
        "version_count" integer NOT NULL DEFAULT '1',
        "project_id" uuid NOT NULL,
        "created_by" uuid NOT NULL,
        "updated_by" uuid,
        "metadata" jsonb NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "deleted_by" uuid,
        CONSTRAINT "PK_prototypes" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for prototypes table
    await queryRunner.query(`CREATE INDEX "idx_prototype_type" ON "prototypes" ("type")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_status" ON "prototypes" ("status")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_figma_file_id" ON "prototypes" ("figma_file_id")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_figma_status" ON "prototypes" ("figma_integration_status")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_project_id" ON "prototypes" ("project_id")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_created_by" ON "prototypes" ("created_by")`);

    // Add foreign key constraints for prototypes
    await queryRunner.query(`
      ALTER TABLE "prototypes"
      ADD CONSTRAINT "FK_prototypes_project_id"
      FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "prototypes"
      ADD CONSTRAINT "FK_prototypes_created_by"
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "prototypes"
      ADD CONSTRAINT "FK_prototypes_updated_by"
      FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // Create prototype_versions table
    await queryRunner.query(`
      CREATE TABLE "prototype_versions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "version_number" character varying(50) NOT NULL,
        "version_name" character varying(255),
        "description" text,
        "type" "version_type_enum" NOT NULL DEFAULT 'minor',
        "status" "version_status_enum" NOT NULL DEFAULT 'draft',
        "parent_version_id" uuid,
        "branch_name" character varying(100),
        "is_main_branch" boolean NOT NULL DEFAULT false,
        "figma_version_id" character varying(255),
        "figma_snapshot_url" text,
        "figma_thumbnail_url" text,
        "changes_summary" text,
        "diff_data" jsonb NOT NULL DEFAULT '{}',
        "prototype_id" uuid NOT NULL,
        "created_by" uuid NOT NULL,
        "approved_by" uuid,
        "approved_at" TIMESTAMP,
        "metadata" jsonb NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "deleted_by" uuid,
        CONSTRAINT "PK_prototype_versions" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for prototype_versions table
    await queryRunner.query(`CREATE INDEX "idx_prototype_version_number" ON "prototype_versions" ("version_number")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_version_type" ON "prototype_versions" ("type")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_version_status" ON "prototype_versions" ("status")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_version_parent" ON "prototype_versions" ("parent_version_id")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_version_branch" ON "prototype_versions" ("branch_name")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_version_main_branch" ON "prototype_versions" ("is_main_branch")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_version_figma_id" ON "prototype_versions" ("figma_version_id")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_version_prototype_id" ON "prototype_versions" ("prototype_id")`);
    await queryRunner.query(`CREATE INDEX "idx_prototype_version_created_by" ON "prototype_versions" ("created_by")`);

    // Add foreign key constraints for prototype_versions
    await queryRunner.query(`
      ALTER TABLE "prototype_versions"
      ADD CONSTRAINT "FK_prototype_versions_parent_version_id"
      FOREIGN KEY ("parent_version_id") REFERENCES "prototype_versions"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "prototype_versions"
      ADD CONSTRAINT "FK_prototype_versions_prototype_id"
      FOREIGN KEY ("prototype_id") REFERENCES "prototypes"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "prototype_versions"
      ADD CONSTRAINT "FK_prototype_versions_created_by"
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "prototype_versions"
      ADD CONSTRAINT "FK_prototype_versions_approved_by"
      FOREIGN KEY ("approved_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // Create visual_feedbacks table
    await queryRunner.query(`
      CREATE TABLE "visual_feedbacks" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "title" character varying(255),
        "content" text NOT NULL,
        "type" "feedback_type_enum" NOT NULL DEFAULT 'suggestion',
        "status" "feedback_status_enum" NOT NULL DEFAULT 'open',
        "priority" "feedback_priority_enum" NOT NULL DEFAULT 'medium',
        "annotation_type" "annotation_type_enum" NOT NULL DEFAULT 'point',
        "position_x" decimal(10,2) NOT NULL,
        "position_y" decimal(10,2) NOT NULL,
        "position_z" integer NOT NULL DEFAULT '0',
        "width" decimal(10,2),
        "height" decimal(10,2),
        "color" character varying(7) NOT NULL DEFAULT '#FF6B6B',
        "border_color" character varying(7),
        "background_color" character varying(7),
        "opacity" decimal(3,2) NOT NULL DEFAULT '1.0',
        "target_element_id" character varying(255),
        "target_element_type" character varying(100),
        "target_element_name" character varying(255),
        "prototype_id" uuid NOT NULL,
        "version_id" uuid,
        "created_by" uuid NOT NULL,
        "assigned_to" uuid,
        "resolved_by" uuid,
        "resolved_at" TIMESTAMP,
        "metadata" jsonb NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "deleted_by" uuid,
        CONSTRAINT "PK_visual_feedbacks" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for visual_feedbacks table
    await queryRunner.query(`CREATE INDEX "idx_visual_feedback_type" ON "visual_feedbacks" ("type")`);
    await queryRunner.query(`CREATE INDEX "idx_visual_feedback_status" ON "visual_feedbacks" ("status")`);
    await queryRunner.query(`CREATE INDEX "idx_visual_feedback_priority" ON "visual_feedbacks" ("priority")`);
    await queryRunner.query(`CREATE INDEX "idx_visual_feedback_annotation_type" ON "visual_feedbacks" ("annotation_type")`);
    await queryRunner.query(`CREATE INDEX "idx_visual_feedback_target_element" ON "visual_feedbacks" ("target_element_id")`);
    await queryRunner.query(`CREATE INDEX "idx_visual_feedback_prototype_id" ON "visual_feedbacks" ("prototype_id")`);
    await queryRunner.query(`CREATE INDEX "idx_visual_feedback_version_id" ON "visual_feedbacks" ("version_id")`);
    await queryRunner.query(`CREATE INDEX "idx_visual_feedback_created_by" ON "visual_feedbacks" ("created_by")`);
    await queryRunner.query(`CREATE INDEX "idx_visual_feedback_assigned_to" ON "visual_feedbacks" ("assigned_to")`);

    // Add foreign key constraints for visual_feedbacks
    await queryRunner.query(`
      ALTER TABLE "visual_feedbacks"
      ADD CONSTRAINT "FK_visual_feedbacks_prototype_id"
      FOREIGN KEY ("prototype_id") REFERENCES "prototypes"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "visual_feedbacks"
      ADD CONSTRAINT "FK_visual_feedbacks_version_id"
      FOREIGN KEY ("version_id") REFERENCES "prototype_versions"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "visual_feedbacks"
      ADD CONSTRAINT "FK_visual_feedbacks_created_by"
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "visual_feedbacks"
      ADD CONSTRAINT "FK_visual_feedbacks_assigned_to"
      FOREIGN KEY ("assigned_to") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "visual_feedbacks"
      ADD CONSTRAINT "FK_visual_feedbacks_resolved_by"
      FOREIGN KEY ("resolved_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints for visual_feedbacks
    await queryRunner.query(`ALTER TABLE "visual_feedbacks" DROP CONSTRAINT "FK_visual_feedbacks_resolved_by"`);
    await queryRunner.query(`ALTER TABLE "visual_feedbacks" DROP CONSTRAINT "FK_visual_feedbacks_assigned_to"`);
    await queryRunner.query(`ALTER TABLE "visual_feedbacks" DROP CONSTRAINT "FK_visual_feedbacks_created_by"`);
    await queryRunner.query(`ALTER TABLE "visual_feedbacks" DROP CONSTRAINT "FK_visual_feedbacks_version_id"`);
    await queryRunner.query(`ALTER TABLE "visual_feedbacks" DROP CONSTRAINT "FK_visual_feedbacks_prototype_id"`);

    // Drop indexes for visual_feedbacks
    await queryRunner.query(`DROP INDEX "idx_visual_feedback_assigned_to"`);
    await queryRunner.query(`DROP INDEX "idx_visual_feedback_created_by"`);
    await queryRunner.query(`DROP INDEX "idx_visual_feedback_version_id"`);
    await queryRunner.query(`DROP INDEX "idx_visual_feedback_prototype_id"`);
    await queryRunner.query(`DROP INDEX "idx_visual_feedback_target_element"`);
    await queryRunner.query(`DROP INDEX "idx_visual_feedback_annotation_type"`);
    await queryRunner.query(`DROP INDEX "idx_visual_feedback_priority"`);
    await queryRunner.query(`DROP INDEX "idx_visual_feedback_status"`);
    await queryRunner.query(`DROP INDEX "idx_visual_feedback_type"`);

    // Drop visual_feedbacks table
    await queryRunner.query(`DROP TABLE "visual_feedbacks"`);

    // Drop foreign key constraints for prototype_versions
    await queryRunner.query(`ALTER TABLE "prototype_versions" DROP CONSTRAINT "FK_prototype_versions_approved_by"`);
    await queryRunner.query(`ALTER TABLE "prototype_versions" DROP CONSTRAINT "FK_prototype_versions_created_by"`);
    await queryRunner.query(`ALTER TABLE "prototype_versions" DROP CONSTRAINT "FK_prototype_versions_prototype_id"`);
    await queryRunner.query(`ALTER TABLE "prototype_versions" DROP CONSTRAINT "FK_prototype_versions_parent_version_id"`);

    // Drop indexes for prototype_versions
    await queryRunner.query(`DROP INDEX "idx_prototype_version_created_by"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_version_prototype_id"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_version_figma_id"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_version_main_branch"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_version_branch"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_version_parent"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_version_status"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_version_type"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_version_number"`);

    // Drop prototype_versions table
    await queryRunner.query(`DROP TABLE "prototype_versions"`);

    // Drop foreign key constraints for prototypes
    await queryRunner.query(`ALTER TABLE "prototypes" DROP CONSTRAINT "FK_prototypes_updated_by"`);
    await queryRunner.query(`ALTER TABLE "prototypes" DROP CONSTRAINT "FK_prototypes_created_by"`);
    await queryRunner.query(`ALTER TABLE "prototypes" DROP CONSTRAINT "FK_prototypes_project_id"`);

    // Drop indexes for prototypes
    await queryRunner.query(`DROP INDEX "idx_prototype_created_by"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_project_id"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_figma_status"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_figma_file_id"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_status"`);
    await queryRunner.query(`DROP INDEX "idx_prototype_type"`);

    // Drop prototypes table
    await queryRunner.query(`DROP TABLE "prototypes"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "annotation_type_enum"`);
    await queryRunner.query(`DROP TYPE "feedback_priority_enum"`);
    await queryRunner.query(`DROP TYPE "feedback_type_enum"`);
    await queryRunner.query(`DROP TYPE "feedback_status_enum"`);
    await queryRunner.query(`DROP TYPE "version_type_enum"`);
    await queryRunner.query(`DROP TYPE "version_status_enum"`);
    await queryRunner.query(`DROP TYPE "figma_integration_status_enum"`);
    await queryRunner.query(`DROP TYPE "prototype_type_enum"`);
    await queryRunner.query(`DROP TYPE "prototype_status_enum"`);
  }
}
