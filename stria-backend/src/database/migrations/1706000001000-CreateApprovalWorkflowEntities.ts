import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateApprovalWorkflowEntities1706000001000 implements MigrationInterface {
  name = 'CreateApprovalWorkflowEntities1706000001000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create workflow status enum
    await queryRunner.query(`
      CREATE TYPE "workflow_status_enum" AS ENUM(
        'draft', 'active', 'in_progress', 'pending', 'approved', 
        'rejected', 'cancelled', 'completed', 'expired'
      )
    `);

    // Create workflow type enum
    await queryRunner.query(`
      CREATE TYPE "workflow_type_enum" AS ENUM(
        'sequential', 'parallel', 'hybrid', 'conditional', 'escalation', 'consensus'
      )
    `);

    // Create approval strategy enum
    await queryRunner.query(`
      CREATE TYPE "approval_strategy_enum" AS ENUM(
        'all_required', 'majority_required', 'any_required', 
        'threshold_based', 'weighted_voting', 'role_based'
      )
    `);

    // Create step status enum
    await queryRunner.query(`
      CREATE TYPE "step_status_enum" AS ENUM(
        'pending', 'in_progress', 'waiting_for_approval', 'approved', 
        'rejected', 'skipped', 'cancelled', 'completed', 'overdue'
      )
    `);

    // Create step type enum
    await queryRunner.query(`
      CREATE TYPE "step_type_enum" AS ENUM(
        'review', 'approval', 'feedback', 'validation', 
        'sign_off', 'notification', 'conditional', 'automated'
      )
    `);

    // Create approval decision enum
    await queryRunner.query(`
      CREATE TYPE "approval_decision_enum" AS ENUM(
        'approved', 'rejected', 'approved_with_conditions', 
        'needs_changes', 'deferred', 'abstain', 'delegated', 'escalated'
      )
    `);

    // Create action type enum
    await queryRunner.query(`
      CREATE TYPE "action_type_enum" AS ENUM(
        'approve', 'reject', 'request_changes', 'comment', 
        'delegate', 'escalate', 'skip', 'defer'
      )
    `);

    // Create design_approval_workflows table
    await queryRunner.query(`
      CREATE TABLE "design_approval_workflows" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(255) NOT NULL,
        "description" text,
        "type" "workflow_type_enum" NOT NULL DEFAULT 'sequential',
        "status" "workflow_status_enum" NOT NULL DEFAULT 'draft',
        "approval_strategy" "approval_strategy_enum" NOT NULL DEFAULT 'all_required',
        "required_approvals" integer NOT NULL DEFAULT '1',
        "allow_parallel_approval" boolean NOT NULL DEFAULT true,
        "auto_approve_on_threshold" boolean NOT NULL DEFAULT false,
        "approval_threshold_percentage" decimal(5,2) NOT NULL DEFAULT '100.0',
        "due_date" TIMESTAMP,
        "reminder_interval_hours" integer NOT NULL DEFAULT '24',
        "escalation_hours" integer,
        "current_step" integer NOT NULL DEFAULT '1',
        "total_steps" integer NOT NULL DEFAULT '1',
        "completed_steps" integer NOT NULL DEFAULT '0',
        "approval_percentage" decimal(5,2) NOT NULL DEFAULT '0.0',
        "prototype_id" uuid NOT NULL,
        "created_by" uuid NOT NULL,
        "assigned_to" uuid,
        "completed_by" uuid,
        "completed_at" TIMESTAMP,
        "escalated_to" uuid,
        "escalated_at" TIMESTAMP,
        "metadata" jsonb NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "started_at" TIMESTAMP,
        "deleted_at" TIMESTAMP,
        "deleted_by" uuid,
        CONSTRAINT "PK_design_approval_workflows" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for design_approval_workflows table
    await queryRunner.query(`CREATE INDEX "idx_approval_workflow_type" ON "design_approval_workflows" ("type")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_workflow_status" ON "design_approval_workflows" ("status")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_workflow_strategy" ON "design_approval_workflows" ("approval_strategy")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_workflow_prototype_id" ON "design_approval_workflows" ("prototype_id")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_workflow_created_by" ON "design_approval_workflows" ("created_by")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_workflow_assigned_to" ON "design_approval_workflows" ("assigned_to")`);

    // Add foreign key constraints for design_approval_workflows
    await queryRunner.query(`
      ALTER TABLE "design_approval_workflows" 
      ADD CONSTRAINT "FK_design_approval_workflows_prototype_id" 
      FOREIGN KEY ("prototype_id") REFERENCES "prototypes"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "design_approval_workflows" 
      ADD CONSTRAINT "FK_design_approval_workflows_created_by" 
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "design_approval_workflows" 
      ADD CONSTRAINT "FK_design_approval_workflows_assigned_to" 
      FOREIGN KEY ("assigned_to") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "design_approval_workflows" 
      ADD CONSTRAINT "FK_design_approval_workflows_completed_by" 
      FOREIGN KEY ("completed_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "design_approval_workflows"
      ADD CONSTRAINT "FK_design_approval_workflows_escalated_to"
      FOREIGN KEY ("escalated_to") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // Create approval_steps table
    await queryRunner.query(`
      CREATE TABLE "approval_steps" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "step_number" integer NOT NULL,
        "step_name" character varying(255) NOT NULL,
        "description" text,
        "type" "step_type_enum" NOT NULL DEFAULT 'review',
        "status" "step_status_enum" NOT NULL DEFAULT 'pending',
        "is_required" boolean NOT NULL DEFAULT true,
        "allow_skip" boolean NOT NULL DEFAULT false,
        "auto_approve" boolean NOT NULL DEFAULT false,
        "required_approvers" integer NOT NULL DEFAULT '1',
        "due_date" TIMESTAMP,
        "estimated_hours" decimal(8,2),
        "actual_hours" decimal(8,2),
        "depends_on_steps" jsonb NOT NULL DEFAULT '[]',
        "blocks_steps" jsonb NOT NULL DEFAULT '[]',
        "approval_count" integer NOT NULL DEFAULT '0',
        "rejection_count" integer NOT NULL DEFAULT '0',
        "final_decision" "approval_decision_enum",
        "workflow_id" uuid NOT NULL,
        "created_by" uuid NOT NULL,
        "assigned_to" uuid,
        "completed_by" uuid,
        "completed_at" TIMESTAMP,
        "metadata" jsonb NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "started_at" TIMESTAMP,
        "deleted_at" TIMESTAMP,
        "deleted_by" uuid,
        CONSTRAINT "PK_approval_steps" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for approval_steps table
    await queryRunner.query(`CREATE INDEX "idx_approval_step_number" ON "approval_steps" ("step_number")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_step_type" ON "approval_steps" ("type")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_step_status" ON "approval_steps" ("status")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_step_workflow_id" ON "approval_steps" ("workflow_id")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_step_created_by" ON "approval_steps" ("created_by")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_step_assigned_to" ON "approval_steps" ("assigned_to")`);

    // Add foreign key constraints for approval_steps
    await queryRunner.query(`
      ALTER TABLE "approval_steps"
      ADD CONSTRAINT "FK_approval_steps_workflow_id"
      FOREIGN KEY ("workflow_id") REFERENCES "design_approval_workflows"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "approval_steps"
      ADD CONSTRAINT "FK_approval_steps_created_by"
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "approval_steps"
      ADD CONSTRAINT "FK_approval_steps_assigned_to"
      FOREIGN KEY ("assigned_to") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "approval_steps"
      ADD CONSTRAINT "FK_approval_steps_completed_by"
      FOREIGN KEY ("completed_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // Create approval_actions table
    await queryRunner.query(`
      CREATE TABLE "approval_actions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "type" "action_type_enum" NOT NULL DEFAULT 'comment',
        "decision" "approval_decision_enum" NOT NULL,
        "comment" text,
        "reason" text,
        "is_conditional" boolean NOT NULL DEFAULT false,
        "conditions" jsonb NOT NULL DEFAULT '[]',
        "step_id" uuid NOT NULL,
        "user_id" uuid NOT NULL,
        "delegated_by" uuid,
        "delegation_reason" text,
        "metadata" jsonb NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "deleted_by" uuid,
        CONSTRAINT "PK_approval_actions" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for approval_actions table
    await queryRunner.query(`CREATE INDEX "idx_approval_action_type" ON "approval_actions" ("type")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_action_decision" ON "approval_actions" ("decision")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_action_step_id" ON "approval_actions" ("step_id")`);
    await queryRunner.query(`CREATE INDEX "idx_approval_action_user_id" ON "approval_actions" ("user_id")`);

    // Add foreign key constraints for approval_actions
    await queryRunner.query(`
      ALTER TABLE "approval_actions"
      ADD CONSTRAINT "FK_approval_actions_step_id"
      FOREIGN KEY ("step_id") REFERENCES "approval_steps"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "approval_actions"
      ADD CONSTRAINT "FK_approval_actions_user_id"
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "approval_actions"
      ADD CONSTRAINT "FK_approval_actions_delegated_by"
      FOREIGN KEY ("delegated_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints for approval_actions
    await queryRunner.query(`ALTER TABLE "approval_actions" DROP CONSTRAINT "FK_approval_actions_delegated_by"`);
    await queryRunner.query(`ALTER TABLE "approval_actions" DROP CONSTRAINT "FK_approval_actions_user_id"`);
    await queryRunner.query(`ALTER TABLE "approval_actions" DROP CONSTRAINT "FK_approval_actions_step_id"`);

    // Drop indexes for approval_actions
    await queryRunner.query(`DROP INDEX "idx_approval_action_user_id"`);
    await queryRunner.query(`DROP INDEX "idx_approval_action_step_id"`);
    await queryRunner.query(`DROP INDEX "idx_approval_action_decision"`);
    await queryRunner.query(`DROP INDEX "idx_approval_action_type"`);

    // Drop approval_actions table
    await queryRunner.query(`DROP TABLE "approval_actions"`);

    // Drop foreign key constraints for approval_steps
    await queryRunner.query(`ALTER TABLE "approval_steps" DROP CONSTRAINT "FK_approval_steps_completed_by"`);
    await queryRunner.query(`ALTER TABLE "approval_steps" DROP CONSTRAINT "FK_approval_steps_assigned_to"`);
    await queryRunner.query(`ALTER TABLE "approval_steps" DROP CONSTRAINT "FK_approval_steps_created_by"`);
    await queryRunner.query(`ALTER TABLE "approval_steps" DROP CONSTRAINT "FK_approval_steps_workflow_id"`);

    // Drop indexes for approval_steps
    await queryRunner.query(`DROP INDEX "idx_approval_step_assigned_to"`);
    await queryRunner.query(`DROP INDEX "idx_approval_step_created_by"`);
    await queryRunner.query(`DROP INDEX "idx_approval_step_workflow_id"`);
    await queryRunner.query(`DROP INDEX "idx_approval_step_status"`);
    await queryRunner.query(`DROP INDEX "idx_approval_step_type"`);
    await queryRunner.query(`DROP INDEX "idx_approval_step_number"`);

    // Drop approval_steps table
    await queryRunner.query(`DROP TABLE "approval_steps"`);

    // Drop foreign key constraints for design_approval_workflows
    await queryRunner.query(`ALTER TABLE "design_approval_workflows" DROP CONSTRAINT "FK_design_approval_workflows_escalated_to"`);
    await queryRunner.query(`ALTER TABLE "design_approval_workflows" DROP CONSTRAINT "FK_design_approval_workflows_completed_by"`);
    await queryRunner.query(`ALTER TABLE "design_approval_workflows" DROP CONSTRAINT "FK_design_approval_workflows_assigned_to"`);
    await queryRunner.query(`ALTER TABLE "design_approval_workflows" DROP CONSTRAINT "FK_design_approval_workflows_created_by"`);
    await queryRunner.query(`ALTER TABLE "design_approval_workflows" DROP CONSTRAINT "FK_design_approval_workflows_prototype_id"`);

    // Drop indexes for design_approval_workflows
    await queryRunner.query(`DROP INDEX "idx_approval_workflow_assigned_to"`);
    await queryRunner.query(`DROP INDEX "idx_approval_workflow_created_by"`);
    await queryRunner.query(`DROP INDEX "idx_approval_workflow_prototype_id"`);
    await queryRunner.query(`DROP INDEX "idx_approval_workflow_strategy"`);
    await queryRunner.query(`DROP INDEX "idx_approval_workflow_status"`);
    await queryRunner.query(`DROP INDEX "idx_approval_workflow_type"`);

    // Drop design_approval_workflows table
    await queryRunner.query(`DROP TABLE "design_approval_workflows"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "action_type_enum"`);
    await queryRunner.query(`DROP TYPE "approval_decision_enum"`);
    await queryRunner.query(`DROP TYPE "step_type_enum"`);
    await queryRunner.query(`DROP TYPE "step_status_enum"`);
    await queryRunner.query(`DROP TYPE "approval_strategy_enum"`);
    await queryRunner.query(`DROP TYPE "workflow_type_enum"`);
    await queryRunner.query(`DROP TYPE "workflow_status_enum"`);
  }
}
