import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCommentDiscussionEntities1706000002000 implements MigrationInterface {
  name = 'CreateCommentDiscussionEntities1706000002000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create comment status enum
    await queryRunner.query(`
      CREATE TYPE "comment_status_enum" AS ENUM(
        'active', 'edited', 'deleted', 'hidden', 
        'flagged', 'approved', 'pending_moderation'
      )
    `);

    // Create comment type enum
    await queryRunner.query(`
      CREATE TYPE "comment_type_enum" AS ENUM(
        'general', 'feedback', 'question', 'suggestion', 'issue',
        'praise', 'clarification', 'decision', 'action_item'
      )
    `);

    // Create discussion status enum
    await queryRunner.query(`
      CREATE TYPE "discussion_status_enum" AS ENUM(
        'open', 'active', 'resolved', 'closed', 'archived', 'locked'
      )
    `);

    // Create mention type enum
    await queryRunner.query(`
      CREATE TYPE "mention_type_enum" AS ENUM(
        'user', 'team', 'role', 'everyone', 'here'
      )
    `);

    // Create mention status enum
    await queryRunner.query(`
      CREATE TYPE "mention_status_enum" AS ENUM(
        'pending', 'notified', 'read', 'acknowledged', 'ignored', 'expired'
      )
    `);

    // Create comment_discussions table
    await queryRunner.query(`
      CREATE TABLE "comment_discussions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "content" text NOT NULL,
        "formatted_content" text,
        "type" "comment_type_enum" NOT NULL DEFAULT 'general',
        "status" "comment_status_enum" NOT NULL DEFAULT 'active',
        "discussion_status" "discussion_status_enum" NOT NULL DEFAULT 'open',
        "thread_depth" integer NOT NULL DEFAULT '0',
        "reply_count" integer NOT NULL DEFAULT '0',
        "visual_feedback_id" uuid,
        "prototype_id" uuid,
        "author_id" uuid NOT NULL,
        "updated_by" uuid,
        "resolved_by" uuid,
        "resolved_at" TIMESTAMP,
        "like_count" integer NOT NULL DEFAULT '0',
        "dislike_count" integer NOT NULL DEFAULT '0',
        "view_count" integer NOT NULL DEFAULT '0',
        "is_edited" boolean NOT NULL DEFAULT false,
        "edit_count" integer NOT NULL DEFAULT '0',
        "last_edited_at" TIMESTAMP,
        "is_pinned" boolean NOT NULL DEFAULT false,
        "is_highlighted" boolean NOT NULL DEFAULT false,
        "metadata" jsonb NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "deleted_by" uuid,
        "mpath" character varying DEFAULT '',
        "parent_id" uuid,
        CONSTRAINT "PK_comment_discussions" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for comment_discussions table
    await queryRunner.query(`CREATE INDEX "idx_comment_discussion_type" ON "comment_discussions" ("type")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_discussion_status" ON "comment_discussions" ("status")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_discussion_discussion_status" ON "comment_discussions" ("discussion_status")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_discussion_thread_depth" ON "comment_discussions" ("thread_depth")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_discussion_visual_feedback_id" ON "comment_discussions" ("visual_feedback_id")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_discussion_prototype_id" ON "comment_discussions" ("prototype_id")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_discussion_author_id" ON "comment_discussions" ("author_id")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_discussion_mpath" ON "comment_discussions" ("mpath")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_discussion_parent_id" ON "comment_discussions" ("parent_id")`);

    // Add foreign key constraints for comment_discussions
    await queryRunner.query(`
      ALTER TABLE "comment_discussions" 
      ADD CONSTRAINT "FK_comment_discussions_visual_feedback_id" 
      FOREIGN KEY ("visual_feedback_id") REFERENCES "visual_feedbacks"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "comment_discussions" 
      ADD CONSTRAINT "FK_comment_discussions_prototype_id" 
      FOREIGN KEY ("prototype_id") REFERENCES "prototypes"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "comment_discussions" 
      ADD CONSTRAINT "FK_comment_discussions_author_id" 
      FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "comment_discussions" 
      ADD CONSTRAINT "FK_comment_discussions_updated_by" 
      FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "comment_discussions" 
      ADD CONSTRAINT "FK_comment_discussions_resolved_by" 
      FOREIGN KEY ("resolved_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "comment_discussions" 
      ADD CONSTRAINT "FK_comment_discussions_parent_id" 
      FOREIGN KEY ("parent_id") REFERENCES "comment_discussions"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Create comment_mentions table
    await queryRunner.query(`
      CREATE TABLE "comment_mentions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "type" "mention_type_enum" NOT NULL DEFAULT 'user',
        "status" "mention_status_enum" NOT NULL DEFAULT 'pending',
        "start_position" integer NOT NULL,
        "end_position" integer NOT NULL,
        "mention_text" character varying(255) NOT NULL,
        "comment_id" uuid NOT NULL,
        "mentioned_user_id" uuid NOT NULL,
        "mentioned_by_user_id" uuid NOT NULL,
        "notification_sent" boolean NOT NULL DEFAULT false,
        "notification_sent_at" TIMESTAMP,
        "notification_read" boolean NOT NULL DEFAULT false,
        "notification_read_at" TIMESTAMP,
        "metadata" jsonb NOT NULL DEFAULT '{}',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "deleted_by" uuid,
        CONSTRAINT "PK_comment_mentions" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for comment_mentions table
    await queryRunner.query(`CREATE INDEX "idx_comment_mention_type" ON "comment_mentions" ("type")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_mention_status" ON "comment_mentions" ("status")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_mention_comment_id" ON "comment_mentions" ("comment_id")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_mention_mentioned_user_id" ON "comment_mentions" ("mentioned_user_id")`);
    await queryRunner.query(`CREATE INDEX "idx_comment_mention_mentioned_by_user_id" ON "comment_mentions" ("mentioned_by_user_id")`);

    // Add foreign key constraints for comment_mentions
    await queryRunner.query(`
      ALTER TABLE "comment_mentions" 
      ADD CONSTRAINT "FK_comment_mentions_comment_id" 
      FOREIGN KEY ("comment_id") REFERENCES "comment_discussions"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "comment_mentions" 
      ADD CONSTRAINT "FK_comment_mentions_mentioned_user_id" 
      FOREIGN KEY ("mentioned_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "comment_mentions" 
      ADD CONSTRAINT "FK_comment_mentions_mentioned_by_user_id" 
      FOREIGN KEY ("mentioned_by_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints for comment_mentions
    await queryRunner.query(`ALTER TABLE "comment_mentions" DROP CONSTRAINT "FK_comment_mentions_mentioned_by_user_id"`);
    await queryRunner.query(`ALTER TABLE "comment_mentions" DROP CONSTRAINT "FK_comment_mentions_mentioned_user_id"`);
    await queryRunner.query(`ALTER TABLE "comment_mentions" DROP CONSTRAINT "FK_comment_mentions_comment_id"`);

    // Drop indexes for comment_mentions
    await queryRunner.query(`DROP INDEX "idx_comment_mention_mentioned_by_user_id"`);
    await queryRunner.query(`DROP INDEX "idx_comment_mention_mentioned_user_id"`);
    await queryRunner.query(`DROP INDEX "idx_comment_mention_comment_id"`);
    await queryRunner.query(`DROP INDEX "idx_comment_mention_status"`);
    await queryRunner.query(`DROP INDEX "idx_comment_mention_type"`);

    // Drop comment_mentions table
    await queryRunner.query(`DROP TABLE "comment_mentions"`);

    // Drop foreign key constraints for comment_discussions
    await queryRunner.query(`ALTER TABLE "comment_discussions" DROP CONSTRAINT "FK_comment_discussions_parent_id"`);
    await queryRunner.query(`ALTER TABLE "comment_discussions" DROP CONSTRAINT "FK_comment_discussions_resolved_by"`);
    await queryRunner.query(`ALTER TABLE "comment_discussions" DROP CONSTRAINT "FK_comment_discussions_updated_by"`);
    await queryRunner.query(`ALTER TABLE "comment_discussions" DROP CONSTRAINT "FK_comment_discussions_author_id"`);
    await queryRunner.query(`ALTER TABLE "comment_discussions" DROP CONSTRAINT "FK_comment_discussions_prototype_id"`);
    await queryRunner.query(`ALTER TABLE "comment_discussions" DROP CONSTRAINT "FK_comment_discussions_visual_feedback_id"`);

    // Drop indexes for comment_discussions
    await queryRunner.query(`DROP INDEX "idx_comment_discussion_parent_id"`);
    await queryRunner.query(`DROP INDEX "idx_comment_discussion_mpath"`);
    await queryRunner.query(`DROP INDEX "idx_comment_discussion_author_id"`);
    await queryRunner.query(`DROP INDEX "idx_comment_discussion_prototype_id"`);
    await queryRunner.query(`DROP INDEX "idx_comment_discussion_visual_feedback_id"`);
    await queryRunner.query(`DROP INDEX "idx_comment_discussion_thread_depth"`);
    await queryRunner.query(`DROP INDEX "idx_comment_discussion_discussion_status"`);
    await queryRunner.query(`DROP INDEX "idx_comment_discussion_status"`);
    await queryRunner.query(`DROP INDEX "idx_comment_discussion_type"`);

    // Drop comment_discussions table
    await queryRunner.query(`DROP TABLE "comment_discussions"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "mention_status_enum"`);
    await queryRunner.query(`DROP TYPE "mention_type_enum"`);
    await queryRunner.query(`DROP TYPE "discussion_status_enum"`);
    await queryRunner.query(`DROP TYPE "comment_type_enum"`);
    await queryRunner.query(`DROP TYPE "comment_status_enum"`);
  }
}
