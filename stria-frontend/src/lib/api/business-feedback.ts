/**
 * Business Feedback API Client
 * STRIA-142: 轻量级反馈收集方案
 * 
 * API client for lightweight business feedback management
 */

import { apiClient } from './client';

// Types
export interface BusinessFeedback {
  id: string;
  title: string;
  description: string;
  type: 'approval_decision' | 'change_request' | 'clarification_needed' | 'milestone_feedback';
  priority: 'low' | 'medium' | 'high' | 'blocking';
  status: 'submitted' | 'under_review' | 'approved' | 'changes_requested' | 'resolved';
  prototypeId: string;
  submittedBy: {
    id: string;
    name: string;
    email: string;
  };
  assignedTo?: {
    id: string;
    name: string;
    email: string;
  };
  resolvedBy?: {
    id: string;
    name: string;
    email: string;
  };
  workflowStepId?: string;
  milestoneId?: string;
  dueDate?: string;
  figmaPageUrl?: string;
  screenReference?: string;
  resolutionNotes?: string;
  metadata: {
    attachments?: string[];
    relatedFeedbackIds?: string[];
    estimatedDelay?: number;
    resourceRequirement?: string;
    riskLevel?: string;
    customProperties?: Record<string, any>;
  };
  submittedAt: string;
  updatedAt: string;
  resolvedAt?: string;
  deletedAt?: string;
}

export interface CreateBusinessFeedbackData {
  title: string;
  description: string;
  type: BusinessFeedback['type'];
  priority: BusinessFeedback['priority'];
  prototypeId: string;
  workflowStepId?: string;
  milestoneId?: string;
  assignedTo?: string;
  dueDate?: string;
  figmaPageUrl?: string;
  screenReference?: string;
  attachments?: string[];
  relatedFeedbackIds?: string[];
  estimatedDelay?: number;
  resourceRequirement?: string;
  riskLevel?: 'low' | 'medium' | 'high';
  customProperties?: Record<string, any>;
}

export interface UpdateBusinessFeedbackData {
  title?: string;
  description?: string;
  type?: BusinessFeedback['type'];
  priority?: BusinessFeedback['priority'];
  status?: BusinessFeedback['status'];
  assignedTo?: string;
  dueDate?: string;
  figmaPageUrl?: string;
  screenReference?: string;
  attachments?: string[];
  relatedFeedbackIds?: string[];
  estimatedDelay?: number;
  resourceRequirement?: string;
  riskLevel?: 'low' | 'medium' | 'high';
  resolutionNotes?: string;
  resolvedAt?: string;
  customProperties?: Record<string, any>;
}

export interface BusinessFeedbackFilters {
  prototypeId?: string;
  submittedBy?: string;
  assignedTo?: string;
  workflowStepId?: string;
  milestoneId?: string;
  type?: BusinessFeedback['type'];
  status?: BusinessFeedback['status'];
  priority?: BusinessFeedback['priority'];
  search?: string;
  screenReference?: string;
  submittedAfter?: string;
  submittedBefore?: string;
  dueAfter?: string;
  dueBefore?: string;
  resolvedAfter?: string;
  resolvedBefore?: string;
  overdueOnly?: boolean;
  unresolvedOnly?: boolean;
  assignedToMeOnly?: boolean;
  submittedByMeOnly?: boolean;
  highPriorityOnly?: boolean;
  blockingOnly?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  includePrototype?: boolean;
  includeSubmitter?: boolean;
  includeAssignee?: boolean;
  includeDeleted?: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface BusinessFeedbackStats {
  total: number;
  byType: Record<string, number>;
  byStatus: Record<string, number>;
  byPriority: Record<string, number>;
  overdue: number;
  resolved: number;
  averageResolutionTime: number;
}

export class BusinessFeedbackAPI {
  private static readonly BASE_PATH = '/business-feedback';

  /**
   * Create a new business feedback
   */
  static async createFeedback(data: CreateBusinessFeedbackData): Promise<BusinessFeedback> {
    const response = await apiClient.post<BusinessFeedback>(this.BASE_PATH, data);
    return response.data;
  }

  /**
   * Get all business feedback with filtering and pagination
   */
  static async getFeedbacks(filters?: BusinessFeedbackFilters): Promise<PaginatedResponse<BusinessFeedback>> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'boolean') {
            params.append(key, value.toString());
          } else if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }

    const response = await apiClient.get<PaginatedResponse<BusinessFeedback>>(
      `${this.BASE_PATH}?${params.toString()}`
    );
    return response.data;
  }

  /**
   * Get a single business feedback by ID
   */
  static async getFeedback(id: string): Promise<BusinessFeedback> {
    const response = await apiClient.get<BusinessFeedback>(`${this.BASE_PATH}/${id}`);
    return response.data;
  }

  /**
   * Update a business feedback
   */
  static async updateFeedback(id: string, data: UpdateBusinessFeedbackData): Promise<BusinessFeedback> {
    const response = await apiClient.patch<BusinessFeedback>(`${this.BASE_PATH}/${id}`, data);
    return response.data;
  }

  /**
   * Delete a business feedback (soft delete)
   */
  static async deleteFeedback(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_PATH}/${id}`);
  }

  /**
   * Assign feedback to a user
   */
  static async assignFeedback(id: string, assigneeId: string): Promise<BusinessFeedback> {
    const response = await apiClient.post<BusinessFeedback>(`${this.BASE_PATH}/${id}/assign`, {
      assigneeId
    });
    return response.data;
  }

  /**
   * Resolve feedback
   */
  static async resolveFeedback(id: string, resolutionNotes: string): Promise<BusinessFeedback> {
    const response = await apiClient.post<BusinessFeedback>(`${this.BASE_PATH}/${id}/resolve`, {
      resolutionNotes
    });
    return response.data;
  }

  /**
   * Get feedback by prototype ID
   */
  static async getFeedbackByPrototype(
    prototypeId: string, 
    filters?: Omit<BusinessFeedbackFilters, 'prototypeId'>
  ): Promise<PaginatedResponse<BusinessFeedback>> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'boolean') {
            params.append(key, value.toString());
          } else if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }

    const response = await apiClient.get<PaginatedResponse<BusinessFeedback>>(
      `${this.BASE_PATH}/prototype/${prototypeId}?${params.toString()}`
    );
    return response.data;
  }

  /**
   * Get feedback statistics
   */
  static async getFeedbackStats(filters?: Partial<BusinessFeedbackFilters>): Promise<BusinessFeedbackStats> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'boolean') {
            params.append(key, value.toString());
          } else if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }

    const response = await apiClient.get<BusinessFeedbackStats>(
      `${this.BASE_PATH}/stats/summary?${params.toString()}`
    );
    return response.data;
  }

  /**
   * Get my assigned feedback
   */
  static async getMyAssignedFeedback(filters?: BusinessFeedbackFilters): Promise<PaginatedResponse<BusinessFeedback>> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'boolean') {
            params.append(key, value.toString());
          } else if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }

    const response = await apiClient.get<PaginatedResponse<BusinessFeedback>>(
      `${this.BASE_PATH}/assigned/me?${params.toString()}`
    );
    return response.data;
  }

  /**
   * Get my submitted feedback
   */
  static async getMySubmittedFeedback(filters?: BusinessFeedbackFilters): Promise<PaginatedResponse<BusinessFeedback>> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'boolean') {
            params.append(key, value.toString());
          } else if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }

    const response = await apiClient.get<PaginatedResponse<BusinessFeedback>>(
      `${this.BASE_PATH}/submitted/me?${params.toString()}`
    );
    return response.data;
  }
}
