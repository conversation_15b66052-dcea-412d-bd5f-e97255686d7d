/**
 * Prototypes API Client
 * STRIA-141: Figma原型嵌入界面
 * 
 * API client for prototype management and Figma integration
 */

import { apiClient } from './client';

// Types
export interface Prototype {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  figmaFileId?: string;
  figmaFileUrl?: string;
  figmaIntegrationStatus: string;
  currentVersion: string;
  versionCount: number;
  createdAt: string;
  updatedAt: string;
  projectId: string;
  createdBy: string;
  project: {
    id: string;
    name: string;
  };
  creator: {
    id: string;
    name: string;
    email: string;
  };
  metadata: {
    figmaNodeIds?: string[];
    designSpecs?: {
      width?: number;
      height?: number;
      scale?: number;
    };
    collaborators?: string[];
    tags?: string[];
    customProperties?: Record<string, any>;
  };
}

export interface PrototypeVersion {
  id: string;
  prototypeId: string;
  version: string;
  name?: string;
  description?: string;
  figmaFileUrl?: string;
  figmaVersionId?: string;
  isActive: boolean;
  createdAt: string;
  createdBy: string;
  creator: {
    id: string;
    name: string;
    email: string;
  };
}

export interface CreatePrototypeData {
  name: string;
  description?: string;
  type: string;
  projectId: string;
  figmaFileUrl?: string;
  figmaFileId?: string;
  metadata?: {
    designSpecs?: {
      width?: number;
      height?: number;
      scale?: number;
    };
    tags?: string[];
    customProperties?: Record<string, any>;
  };
}

export interface UpdatePrototypeData {
  name?: string;
  description?: string;
  type?: string;
  status?: string;
  figmaFileUrl?: string;
  figmaFileId?: string;
  metadata?: {
    designSpecs?: {
      width?: number;
      height?: number;
      scale?: number;
    };
    tags?: string[];
    customProperties?: Record<string, any>;
  };
}

export interface PrototypeFilters {
  projectId?: string;
  type?: string;
  status?: string;
  createdBy?: string;
  search?: string;
  tags?: string[];
  figmaIntegrationStatus?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface FigmaSyncData {
  figmaFileUrl: string;
  syncVersions?: boolean;
  syncComponents?: boolean;
  syncStyles?: boolean;
}

export interface FigmaSyncResponse {
  success: boolean;
  message: string;
  syncedAt: string;
  figmaFileId: string;
  figmaFileUrl: string;
  versionInfo?: {
    version: string;
    name?: string;
    description?: string;
  };
  componentsCount?: number;
  stylesCount?: number;
}

export class PrototypesAPI {
  private static readonly BASE_PATH = '/prototypes';

  /**
   * Get all prototypes with filtering and pagination
   */
  static async getPrototypes(filters?: PrototypeFilters): Promise<PaginatedResponse<Prototype>> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }

    const response = await apiClient.get<PaginatedResponse<Prototype>>(
      `${this.BASE_PATH}?${params.toString()}`
    );
    return response.data;
  }

  /**
   * Get a single prototype by ID
   */
  static async getPrototype(id: string): Promise<Prototype> {
    const response = await apiClient.get<Prototype>(`${this.BASE_PATH}/${id}`);
    return response.data;
  }

  /**
   * Create a new prototype
   */
  static async createPrototype(data: CreatePrototypeData): Promise<Prototype> {
    const response = await apiClient.post<Prototype>(this.BASE_PATH, data);
    return response.data;
  }

  /**
   * Update an existing prototype
   */
  static async updatePrototype(id: string, data: UpdatePrototypeData): Promise<Prototype> {
    const response = await apiClient.patch<Prototype>(`${this.BASE_PATH}/${id}`, data);
    return response.data;
  }

  /**
   * Delete a prototype
   */
  static async deletePrototype(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_PATH}/${id}`);
  }

  /**
   * Get prototype versions
   */
  static async getPrototypeVersions(prototypeId: string): Promise<PrototypeVersion[]> {
    const response = await apiClient.get<PrototypeVersion[]>(`${this.BASE_PATH}/${prototypeId}/versions`);
    return response.data;
  }

  /**
   * Create a new prototype version
   */
  static async createPrototypeVersion(
    prototypeId: string, 
    data: { version: string; name?: string; description?: string; figmaFileUrl?: string }
  ): Promise<PrototypeVersion> {
    const response = await apiClient.post<PrototypeVersion>(
      `${this.BASE_PATH}/${prototypeId}/versions`, 
      data
    );
    return response.data;
  }

  /**
   * Sync prototype with Figma
   */
  static async syncWithFigma(prototypeId: string, data: FigmaSyncData): Promise<FigmaSyncResponse> {
    const response = await apiClient.post<FigmaSyncResponse>(
      `${this.BASE_PATH}/${prototypeId}/figma/sync`, 
      data
    );
    return response.data;
  }

  /**
   * Test Figma connection
   */
  static async testFigmaConnection(figmaFileUrl: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.post<{ success: boolean; message: string }>(
      `${this.BASE_PATH}/figma/test-connection`, 
      { figmaFileUrl }
    );
    return response.data;
  }

  /**
   * Get prototypes by project
   */
  static async getPrototypesByProject(projectId: string, filters?: Omit<PrototypeFilters, 'projectId'>): Promise<PaginatedResponse<Prototype>> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }

    const response = await apiClient.get<PaginatedResponse<Prototype>>(
      `${this.BASE_PATH}/project/${projectId}?${params.toString()}`
    );
    return response.data;
  }

  /**
   * Get prototype statistics
   */
  static async getPrototypeStats(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    figmaIntegrated: number;
    recentlyUpdated: number;
  }> {
    const response = await apiClient.get(`${this.BASE_PATH}/stats/summary`);
    return response.data;
  }
}
