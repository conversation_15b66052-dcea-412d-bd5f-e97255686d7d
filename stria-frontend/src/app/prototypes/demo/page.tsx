'use client';

import React from 'react';
import { FigmaPrototypeEmbed } from '@/components/prototypes/FigmaPrototypeEmbed';
import { PrototypeHeader } from '@/components/prototypes/PrototypeHeader';
import { PrototypeControls } from '@/components/prototypes/PrototypeControls';
import { PrototypeInfo } from '@/components/prototypes/PrototypeInfo';

/**
 * Demo Page for Prototype Components
 * STRIA-141: Figma原型嵌入界面
 * 
 * Demo page to test prototype viewing components
 */

const mockPrototype = {
  id: 'demo-prototype-123',
  name: 'Mobile App Design System',
  description: 'A comprehensive design system for our mobile application with components, patterns, and guidelines.',
  type: 'high_fidelity',
  status: 'in_review',
  figmaFileId: 'demo-file-id',
  figmaFileUrl: 'https://www.figma.com/file/demo',
  figmaIntegrationStatus: 'connected',
  currentVersion: '2.1.0',
  versionCount: 8,
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-20T14:30:00Z',
  project: {
    id: 'project-456',
    name: 'Stria Mobile App',
  },
  creator: {
    id: 'user-789',
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  metadata: {
    figmaNodeIds: ['1:2', '1:3', '1:4'],
    designSpecs: {
      width: 1200,
      height: 800,
      scale: 1.0,
    },
    collaborators: ['user-123', 'user-456'],
    tags: ['mobile', 'design-system', 'components', 'ios', 'android'],
  },
};

export default function PrototypeDemoPage() {
  const [zoom, setZoom] = React.useState(100);
  const [isFullscreen, setIsFullscreen] = React.useState(false);
  const [showInfo, setShowInfo] = React.useState(true);
  const [showComments, setShowComments] = React.useState(false);

  const handleZoomIn = () => setZoom(prev => Math.min(200, prev + 25));
  const handleZoomOut = () => setZoom(prev => Math.max(25, prev - 25));
  const handleZoomReset = () => setZoom(100);
  const handleFullscreenToggle = () => setIsFullscreen(prev => !prev);
  const handleInfoToggle = () => setShowInfo(prev => !prev);
  const handleCommentsToggle = () => setShowComments(prev => !prev);

  const handleBack = () => {
    window.history.back();
  };

  const handleShare = async () => {
    try {
      await navigator.share({
        title: mockPrototype.name,
        url: window.location.href,
      });
    } catch (error) {
      // Fallback to clipboard
      await navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
      {/* Header */}
      {!isFullscreen && (
        <PrototypeHeader
          prototype={mockPrototype}
          onBack={handleBack}
          onShare={handleShare}
        />
      )}

      {/* Main Content */}
      <div className={`flex ${isFullscreen ? 'h-screen' : 'h-[calc(100vh-4rem)]'}`}>
        {/* Sidebar - Info Panel */}
        {showInfo && !isFullscreen && (
          <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
            <PrototypeInfo prototype={mockPrototype} />
          </div>
        )}

        {/* Main Prototype View */}
        <div className="flex-1 flex flex-col">
          {/* Controls Bar */}
          <PrototypeControls
            zoom={zoom}
            isFullscreen={isFullscreen}
            showInfo={showInfo}
            showComments={showComments}
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
            onZoomReset={handleZoomReset}
            onFullscreenToggle={handleFullscreenToggle}
            onInfoToggle={handleInfoToggle}
            onCommentsToggle={handleCommentsToggle}
          />

          {/* Figma Embed */}
          <div className="flex-1 relative">
            <FigmaPrototypeEmbed
              figmaFileUrl={mockPrototype.figmaFileUrl}
              figmaFileId={mockPrototype.figmaFileId}
              zoom={zoom}
              isFullscreen={isFullscreen}
              designSpecs={mockPrototype.metadata.designSpecs}
            />
          </div>
        </div>

        {/* Comments Sidebar */}
        {showComments && !isFullscreen && (
          <div className="w-80 bg-white border-l border-gray-200 overflow-y-auto">
            <div className="p-4">
              <h3 className="font-semibold mb-4">Comments & Feedback</h3>
              <p className="text-gray-500 text-sm">
                Comments and feedback will be displayed here.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
