'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search,
  Plus,
  Eye,
  Edit,
  Figma,
  Calendar,
  User,
  Filter,
  Grid,
  List,
  Loader2
} from 'lucide-react';
import { PrototypesAPI, Prototype, PrototypeFilters } from '@/lib/api/prototypes';
import { cn } from '@/lib/utils';

/**
 * Prototypes List Page
 * STRIA-141: Figma原型嵌入界面
 * 
 * Main page for browsing and managing prototypes
 */

interface ViewState {
  isLoading: boolean;
  error: string | null;
  viewMode: 'grid' | 'list';
}

export default function PrototypesPage() {
  const [prototypes, setPrototypes] = useState<Prototype[]>([]);
  const [viewState, setViewState] = useState<ViewState>({
    isLoading: true,
    error: null,
    viewMode: 'grid',
  });
  const [filters, setFilters] = useState<PrototypeFilters>({
    page: 1,
    limit: 12,
    sortBy: 'updatedAt',
    sortOrder: 'DESC',
  });
  const [searchQuery, setSearchQuery] = useState('');

  // Load prototypes
  useEffect(() => {
    loadPrototypes();
  }, [filters]);

  const loadPrototypes = async () => {
    try {
      setViewState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const response = await PrototypesAPI.getPrototypes({
        ...filters,
        search: searchQuery || undefined,
      });
      
      setPrototypes(response.data);
    } catch (error) {
      console.error('Error loading prototypes:', error);
      setViewState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to load prototypes' 
      }));
    } finally {
      setViewState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setFilters(prev => ({ ...prev, search: query, page: 1 }));
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'in_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'published':
        return 'bg-blue-100 text-blue-800';
      case 'archived':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Loading state
  if (viewState.isLoading && prototypes.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading prototypes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Prototypes</h1>
              <p className="text-gray-600 mt-1">Manage and view your design prototypes</p>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Prototype
            </Button>
          </div>

          {/* Search and Filters */}
          <div className="mt-6 flex items-center gap-4">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search prototypes..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>

            <div className="flex items-center gap-1">
              <Button
                variant={viewState.viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewState(prev => ({ ...prev, viewMode: 'grid' }))}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewState.viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewState(prev => ({ ...prev, viewMode: 'list' }))}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {viewState.error ? (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{viewState.error}</p>
            <Button onClick={loadPrototypes} variant="outline">
              Try Again
            </Button>
          </div>
        ) : prototypes.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 mb-4">No prototypes found</p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Prototype
            </Button>
          </div>
        ) : (
          <div className={cn(
            viewState.viewMode === 'grid' 
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              : "space-y-4"
          )}>
            {prototypes.map((prototype) => (
              <Card key={prototype.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg line-clamp-1">
                        {prototype.name}
                      </CardTitle>
                      {prototype.description && (
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {prototype.description}
                        </p>
                      )}
                    </div>
                    {prototype.figmaFileUrl && (
                      <Figma className="h-5 w-5 text-gray-400 ml-2" />
                    )}
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Status and Type */}
                  <div className="flex gap-2">
                    <Badge className={cn("text-xs", getStatusColor(prototype.status))}>
                      {prototype.status.replace('_', ' ')}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {prototype.type.replace('_', ' ')}
                    </Badge>
                  </div>

                  {/* Meta Info */}
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <span>{prototype.creator.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>Updated {formatDate(prototype.updatedAt)}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button asChild size="sm" className="flex-1">
                      <Link href={`/prototypes/${prototype.id}/view`}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Link>
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
