'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Maximize2,
  Minimize2,
  Info,
  MessageSquare,
  Settings,
  Download,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Prototype Controls Component
 * STRIA-141: Figma原型嵌入界面
 * 
 * Control bar for prototype viewing with zoom, fullscreen, and panel toggles
 */

interface PrototypeControlsProps {
  zoom: number;
  isFullscreen: boolean;
  showInfo: boolean;
  showComments: boolean;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onZoomReset: () => void;
  onFullscreenToggle: () => void;
  onInfoToggle: () => void;
  onCommentsToggle: () => void;
  className?: string;
}

export function PrototypeControls({
  zoom,
  isFullscreen,
  showInfo,
  showComments,
  onZoomIn,
  onZoomOut,
  onZoomReset,
  onFullscreenToggle,
  onInfoToggle,
  onCommentsToggle,
  className
}: PrototypeControlsProps) {
  return (
    <div className={cn(
      "bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between",
      className
    )}>
      {/* Left Section - Zoom Controls */}
      <div className="flex items-center gap-2">
        {/* Zoom Out */}
        <Button
          onClick={onZoomOut}
          variant="ghost"
          size="sm"
          disabled={zoom <= 25}
          className="h-8 w-8 p-0"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>

        {/* Zoom Display */}
        <Button
          onClick={onZoomReset}
          variant="ghost"
          size="sm"
          className="min-w-[60px] h-8 text-sm font-mono"
        >
          {zoom}%
        </Button>

        {/* Zoom In */}
        <Button
          onClick={onZoomIn}
          variant="ghost"
          size="sm"
          disabled={zoom >= 200}
          className="h-8 w-8 p-0"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>

        {/* Reset Zoom */}
        <Button
          onClick={onZoomReset}
          variant="ghost"
          size="sm"
          className="h-8 px-2 text-xs"
          disabled={zoom === 100}
        >
          <RotateCcw className="h-3 w-3 mr-1" />
          Reset
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Device Preview Modes */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            title="Desktop View"
          >
            <Monitor className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            title="Tablet View"
          >
            <Tablet className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            title="Mobile View"
          >
            <Smartphone className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Center Section - Status Indicators */}
      <div className="flex items-center gap-3">
        {/* Zoom Level Indicator */}
        {zoom !== 100 && (
          <Badge variant="outline" className="text-xs">
            Zoom: {zoom}%
          </Badge>
        )}

        {/* Fullscreen Indicator */}
        {isFullscreen && (
          <Badge variant="outline" className="text-xs">
            Fullscreen
          </Badge>
        )}

        {/* Interactive Mode Indicator */}
        <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
          Interactive
        </Badge>
      </div>

      {/* Right Section - View Controls */}
      <div className="flex items-center gap-2">
        {/* Panel Toggles */}
        {!isFullscreen && (
          <>
            <Button
              onClick={onInfoToggle}
              variant={showInfo ? "default" : "ghost"}
              size="sm"
              className="h-8"
            >
              <Info className="h-4 w-4 mr-1" />
              Info
            </Button>

            <Button
              onClick={onCommentsToggle}
              variant={showComments ? "default" : "ghost"}
              size="sm"
              className="h-8"
            >
              <MessageSquare className="h-4 w-4 mr-1" />
              Comments
            </Button>

            <Separator orientation="vertical" className="h-6" />
          </>
        )}

        {/* Settings */}
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          title="Settings"
        >
          <Settings className="h-4 w-4" />
        </Button>

        {/* Download */}
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          title="Download"
        >
          <Download className="h-4 w-4" />
        </Button>

        {/* Fullscreen Toggle */}
        <Button
          onClick={onFullscreenToggle}
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
        >
          {isFullscreen ? (
            <Minimize2 className="h-4 w-4" />
          ) : (
            <Maximize2 className="h-4 w-4" />
          )}
        </Button>
      </div>
    </div>
  );
}
