'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { 
  ExternalLink, 
  AlertTriangle, 
  Loader2,
  RefreshCw,
  Maximize2
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Figma Prototype Embed Component
 * STRIA-141: Figma原型嵌入界面
 * 
 * Embeds Figma prototypes using iframe with interaction support
 * Handles loading states, errors, and responsive scaling
 */

interface FigmaPrototypeEmbedProps {
  figmaFileUrl?: string;
  figmaFileId?: string;
  zoom?: number;
  isFullscreen?: boolean;
  designSpecs?: {
    width?: number;
    height?: number;
    scale?: number;
  };
  className?: string;
}

interface EmbedState {
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  isInteractive: boolean;
}

export function FigmaPrototypeEmbed({
  figmaFileUrl,
  figmaFileId,
  zoom = 100,
  isFullscreen = false,
  designSpecs,
  className
}: FigmaPrototypeEmbedProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [embedState, setEmbedState] = useState<EmbedState>({
    isLoading: true,
    hasError: false,
    isInteractive: false,
  });

  // Generate Figma embed URL
  const getEmbedUrl = () => {
    if (!figmaFileUrl && !figmaFileId) {
      return null;
    }

    let baseUrl = figmaFileUrl;
    
    // If we only have fileId, construct the URL
    if (!baseUrl && figmaFileId) {
      baseUrl = `https://www.figma.com/file/${figmaFileId}`;
    }

    if (!baseUrl) {
      return null;
    }

    // Convert to embed URL
    const embedUrl = baseUrl.replace('figma.com/file/', 'figma.com/embed?embed_host=stria&url=')
      + '&hide-ui=1'
      + '&chrome=DOCUMENTATION'
      + '&scaling=min-zoom'
      + '&page-id=0%3A1'
      + '&node-id=1%3A2'
      + '&viewport=241%2C48%2C0.02'
      + '&t=1234567890';

    return embedUrl;
  };

  const embedUrl = getEmbedUrl();

  // Handle iframe load
  const handleIframeLoad = () => {
    setEmbedState(prev => ({
      ...prev,
      isLoading: false,
      hasError: false,
      isInteractive: true,
    }));
  };

  // Handle iframe error
  const handleIframeError = () => {
    setEmbedState(prev => ({
      ...prev,
      isLoading: false,
      hasError: true,
      errorMessage: 'Failed to load Figma prototype',
      isInteractive: false,
    }));
  };

  // Retry loading
  const handleRetry = () => {
    setEmbedState(prev => ({
      ...prev,
      isLoading: true,
      hasError: false,
      errorMessage: undefined,
    }));

    // Force iframe reload
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  };

  // Open in Figma
  const handleOpenInFigma = () => {
    if (figmaFileUrl) {
      window.open(figmaFileUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // Calculate iframe dimensions based on zoom and design specs
  const getIframeDimensions = () => {
    const zoomFactor = zoom / 100;
    const baseWidth = designSpecs?.width || 1200;
    const baseHeight = designSpecs?.height || 800;

    return {
      width: baseWidth * zoomFactor,
      height: baseHeight * zoomFactor,
    };
  };

  const dimensions = getIframeDimensions();

  // No embed URL available
  if (!embedUrl) {
    return (
      <div className={cn("flex items-center justify-center h-full bg-gray-50", className)}>
        <div className="text-center max-w-md">
          <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No Figma Prototype Available
          </h3>
          <p className="text-gray-600 mb-4">
            This prototype doesn't have a Figma file associated with it yet.
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>
    );
  }

  // Error state
  if (embedState.hasError) {
    return (
      <div className={cn("flex items-center justify-center h-full bg-gray-50", className)}>
        <div className="text-center max-w-md">
          <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Failed to Load Prototype
          </h3>
          <p className="text-gray-600 mb-4">
            {embedState.errorMessage || 'There was an error loading the Figma prototype.'}
          </p>
          <div className="flex gap-2 justify-center">
            <Button onClick={handleRetry} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
            {figmaFileUrl && (
              <Button onClick={handleOpenInFigma}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Open in Figma
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative h-full bg-gray-50", className)}>
      {/* Loading Overlay */}
      {embedState.isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Loading Figma prototype...</p>
          </div>
        </div>
      )}

      {/* Iframe Container */}
      <div className={cn(
        "h-full overflow-auto",
        isFullscreen ? "p-0" : "p-4"
      )}>
        <div 
          className="mx-auto bg-white shadow-lg rounded-lg overflow-hidden"
          style={{
            width: isFullscreen ? '100%' : `${dimensions.width}px`,
            height: isFullscreen ? '100%' : `${dimensions.height}px`,
            minWidth: isFullscreen ? 'auto' : '800px',
            minHeight: isFullscreen ? 'auto' : '600px',
          }}
        >
          <iframe
            ref={iframeRef}
            src={embedUrl}
            width="100%"
            height="100%"
            allowFullScreen
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            className="border-0"
            title="Figma Prototype"
            sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
            style={{
              transform: isFullscreen ? 'none' : `scale(${zoom / 100})`,
              transformOrigin: 'top left',
            }}
          />
        </div>
      </div>

      {/* Interaction Overlay for Non-Interactive Mode */}
      {!embedState.isInteractive && !embedState.isLoading && !embedState.hasError && (
        <div className="absolute inset-0 bg-transparent cursor-not-allowed" />
      )}

      {/* Quick Actions */}
      {embedState.isInteractive && figmaFileUrl && (
        <Button
          onClick={handleOpenInFigma}
          className="absolute top-4 right-4 z-10"
          variant="outline"
          size="sm"
        >
          <ExternalLink className="h-4 w-4 mr-2" />
          Open in Figma
        </Button>
      )}

      {/* Zoom Info */}
      {!isFullscreen && zoom !== 100 && (
        <div className="absolute bottom-4 left-4 bg-black/75 text-white px-2 py-1 rounded text-sm">
          {zoom}%
        </div>
      )}
    </div>
  );
}
