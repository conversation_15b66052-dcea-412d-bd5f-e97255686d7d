'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  MessageSquare,
  Bug,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  Search,
  Filter,
  SortAsc,
  Eye,
  Edit3,
  Trash2,
  Clock,
  User,
  MapPin,
  MoreHorizontal
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { FeedbackAnnotation } from './VisualFeedbackTool';

/**
 * Feedback Panel Component
 * STRIA-142: 可视化标注反馈工具
 * 
 * Panel for managing and viewing visual feedback annotations
 * Supports filtering, sorting, and bulk operations
 */

interface FeedbackPanelProps {
  annotations: FeedbackAnnotation[];
  selectedAnnotation?: string | null;
  onAnnotationSelect: (id: string) => void;
  onAnnotationUpdate: (id: string, updates: Partial<FeedbackAnnotation>) => void;
  onAnnotationDelete: (id: string) => void;
  onAnnotationJumpTo: (id: string) => void;
  className?: string;
}

interface FilterState {
  search: string;
  type: FeedbackAnnotation['type'] | 'all';
  status: FeedbackAnnotation['status'] | 'all';
  priority: FeedbackAnnotation['priority'] | 'all';
  sortBy: 'createdAt' | 'priority' | 'type' | 'status';
  sortOrder: 'asc' | 'desc';
}

export function FeedbackPanel({
  annotations,
  selectedAnnotation,
  onAnnotationSelect,
  onAnnotationUpdate,
  onAnnotationDelete,
  onAnnotationJumpTo,
  className
}: FeedbackPanelProps) {
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    type: 'all',
    status: 'all',
    priority: 'all',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  // Filter and sort annotations
  const filteredAnnotations = React.useMemo(() => {
    let filtered = annotations.filter(annotation => {
      // Search filter
      if (filters.search && !annotation.content.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }
      
      // Type filter
      if (filters.type !== 'all' && annotation.type !== filters.type) {
        return false;
      }
      
      // Status filter
      if (filters.status !== 'all' && annotation.status !== filters.status) {
        return false;
      }
      
      // Priority filter
      if (filters.priority !== 'all' && annotation.priority !== filters.priority) {
        return false;
      }
      
      return true;
    });

    // Sort annotations
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filters.sortBy) {
        case 'createdAt':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'priority':
          const priorityOrder = { low: 1, medium: 2, high: 3, critical: 4 };
          aValue = priorityOrder[a.priority];
          bValue = priorityOrder[b.priority];
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          return 0;
      }
      
      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [annotations, filters]);

  // Get feedback type icon
  const getFeedbackIcon = (type: FeedbackAnnotation['type']) => {
    const icons = {
      comment: MessageSquare,
      bug: Bug,
      suggestion: Lightbulb,
      question: AlertTriangle,
      approval: CheckCircle,
    };
    return icons[type];
  };

  // Get priority color
  const getPriorityColor = (priority: FeedbackAnnotation['priority']) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      critical: 'bg-red-100 text-red-800',
    };
    return colors[priority];
  };

  // Get status color
  const getStatusColor = (status: FeedbackAnnotation['status']) => {
    const colors = {
      open: 'bg-red-100 text-red-800',
      in_progress: 'bg-yellow-100 text-yellow-800',
      resolved: 'bg-green-100 text-green-800',
      closed: 'bg-gray-100 text-gray-800',
    };
    return colors[status];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get annotation counts by type
  const getAnnotationCounts = () => {
    const counts = {
      all: annotations.length,
      comment: 0,
      bug: 0,
      suggestion: 0,
      question: 0,
      approval: 0,
    };

    annotations.forEach(annotation => {
      counts[annotation.type]++;
    });

    return counts;
  };

  const counts = getAnnotationCounts();

  return (
    <div className={cn("h-full flex flex-col", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <h3 className="font-semibold text-lg mb-3">Visual Feedback</h3>
        
        {/* Search */}
        <div className="relative mb-3">
          <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search feedback..."
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            className="pl-10"
          />
        </div>

        {/* Quick Filters */}
        <div className="flex gap-2 mb-3">
          <Button
            variant={filters.type === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilters(prev => ({ ...prev, type: 'all' }))}
          >
            All ({counts.all})
          </Button>
          <Button
            variant={filters.type === 'bug' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilters(prev => ({ ...prev, type: 'bug' }))}
          >
            <Bug className="h-3 w-3 mr-1" />
            {counts.bug}
          </Button>
          <Button
            variant={filters.type === 'suggestion' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilters(prev => ({ ...prev, type: 'suggestion' }))}
          >
            <Lightbulb className="h-3 w-3 mr-1" />
            {counts.suggestion}
          </Button>
        </div>

        {/* Advanced Filters */}
        <div className="flex gap-2">
          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as any }))}
            className="text-xs border rounded px-2 py-1"
          >
            <option value="all">All Status</option>
            <option value="open">Open</option>
            <option value="in_progress">In Progress</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
          </select>
          
          <select
            value={filters.priority}
            onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value as any }))}
            className="text-xs border rounded px-2 py-1"
          >
            <option value="all">All Priority</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setFilters(prev => ({ 
              ...prev, 
              sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc' 
            }))}
          >
            <SortAsc className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Feedback List */}
      <div className="flex-1 overflow-y-auto">
        {filteredAnnotations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No feedback found</p>
          </div>
        ) : (
          <div className="space-y-2 p-2">
            {filteredAnnotations.map((annotation) => {
              const Icon = getFeedbackIcon(annotation.type);
              const isSelected = selectedAnnotation === annotation.id;

              return (
                <Card
                  key={annotation.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    isSelected && "ring-2 ring-blue-400 bg-blue-50"
                  )}
                  onClick={() => onAnnotationSelect(annotation.id)}
                >
                  <CardContent className="p-3">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4 text-gray-600" />
                        <Badge className={cn("text-xs", getPriorityColor(annotation.priority))}>
                          {annotation.priority}
                        </Badge>
                        <Badge className={cn("text-xs", getStatusColor(annotation.status))}>
                          {annotation.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onAnnotationJumpTo(annotation.id);
                        }}
                      >
                        <MapPin className="h-3 w-3" />
                      </Button>
                    </div>

                    {/* Content */}
                    <p className="text-sm text-gray-700 mb-2 line-clamp-2">
                      {annotation.content}
                    </p>

                    {/* Meta */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center gap-2">
                        <User className="h-3 w-3" />
                        <span>{annotation.author.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3" />
                        <span>{formatDate(annotation.createdAt)}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    {isSelected && (
                      <div className="flex gap-2 mt-3 pt-2 border-t">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle edit
                          }}
                        >
                          <Edit3 className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onAnnotationDelete(annotation.id);
                          }}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onAnnotationJumpTo(annotation.id);
                          }}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer Stats */}
      <div className="p-4 border-t bg-gray-50">
        <div className="text-xs text-gray-600 text-center">
          {filteredAnnotations.length} of {annotations.length} feedback items
        </div>
      </div>
    </div>
  );
}
