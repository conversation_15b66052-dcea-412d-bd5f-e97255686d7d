'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Calendar,
  User,
  GitBranch,
  Tag,
  ExternalLink,
  FileText,
  Layers,
  Monitor,
  Figma,
  Clock,
  Eye,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Prototype Info Component
 * STRIA-141: Figma原型嵌入界面
 * 
 * Information panel showing prototype details, metadata, and actions
 */

interface PrototypeInfoProps {
  prototype: {
    id: string;
    name: string;
    description?: string;
    type: string;
    status: string;
    figmaFileId?: string;
    figmaFileUrl?: string;
    figmaIntegrationStatus: string;
    currentVersion: string;
    versionCount: number;
    createdAt: string;
    updatedAt: string;
    project: {
      id: string;
      name: string;
    };
    creator: {
      id: string;
      name: string;
      email: string;
    };
    metadata: {
      figmaNodeIds?: string[];
      designSpecs?: {
        width?: number;
        height?: number;
        scale?: number;
      };
      collaborators?: string[];
      tags?: string[];
    };
  };
  className?: string;
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    case 'in_review':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'published':
      return 'bg-blue-100 text-blue-800';
    case 'archived':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getFigmaStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'connected':
      return 'bg-green-100 text-green-800';
    case 'syncing':
      return 'bg-yellow-100 text-yellow-800';
    case 'error':
      return 'bg-red-100 text-red-800';
    case 'disconnected':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export function PrototypeInfo({
  prototype,
  className
}: PrototypeInfoProps) {
  return (
    <div className={cn("p-4 space-y-4", className)}>
      {/* Basic Info */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Prototype Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Name */}
          <div>
            <label className="text-sm font-medium text-gray-700">Name</label>
            <p className="text-sm text-gray-900 mt-1">{prototype.name}</p>
          </div>

          {/* Description */}
          {prototype.description && (
            <div>
              <label className="text-sm font-medium text-gray-700">Description</label>
              <p className="text-sm text-gray-600 mt-1">{prototype.description}</p>
            </div>
          )}

          {/* Status & Type */}
          <div className="flex gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700">Status</label>
              <div className="mt-1">
                <Badge className={cn("text-xs", getStatusColor(prototype.status))}>
                  {prototype.status.replace('_', ' ')}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Type</label>
              <div className="mt-1">
                <Badge variant="outline" className="text-xs">
                  {prototype.type.replace('_', ' ')}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Version Info */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Version Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Current Version</span>
            <Badge variant="outline">v{prototype.currentVersion}</Badge>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Total Versions</span>
            <span className="text-sm text-gray-600">{prototype.versionCount}</span>
          </div>
          <Button variant="outline" size="sm" className="w-full">
            <GitBranch className="h-4 w-4 mr-2" />
            View Version History
          </Button>
        </CardContent>
      </Card>

      {/* Figma Integration */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Figma className="h-5 w-5" />
            Figma Integration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Status</span>
            <Badge className={cn("text-xs", getFigmaStatusColor(prototype.figmaIntegrationStatus))}>
              {prototype.figmaIntegrationStatus}
            </Badge>
          </div>
          
          {prototype.figmaFileId && (
            <div>
              <label className="text-sm font-medium text-gray-700">File ID</label>
              <p className="text-xs text-gray-600 mt-1 font-mono">{prototype.figmaFileId}</p>
            </div>
          )}

          {prototype.figmaFileUrl && (
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => window.open(prototype.figmaFileUrl, '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Open in Figma
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Design Specifications */}
      {prototype.metadata.designSpecs && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Design Specs
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {prototype.metadata.designSpecs.width && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Width</span>
                <span className="text-sm text-gray-600">{prototype.metadata.designSpecs.width}px</span>
              </div>
            )}
            {prototype.metadata.designSpecs.height && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Height</span>
                <span className="text-sm text-gray-600">{prototype.metadata.designSpecs.height}px</span>
              </div>
            )}
            {prototype.metadata.designSpecs.scale && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Scale</span>
                <span className="text-sm text-gray-600">{prototype.metadata.designSpecs.scale}x</span>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Project & Creator */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <User className="h-5 w-5" />
            Project & Creator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <label className="text-sm font-medium text-gray-700">Project</label>
            <p className="text-sm text-gray-900 mt-1">{prototype.project.name}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Created by</label>
            <p className="text-sm text-gray-900 mt-1">{prototype.creator.name}</p>
            <p className="text-xs text-gray-500">{prototype.creator.email}</p>
          </div>
        </CardContent>
      </Card>

      {/* Timestamps */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Timeline
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <label className="text-sm font-medium text-gray-700">Created</label>
            <p className="text-sm text-gray-600 mt-1">{formatDate(prototype.createdAt)}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Last Updated</label>
            <p className="text-sm text-gray-600 mt-1">{formatDate(prototype.updatedAt)}</p>
          </div>
        </CardContent>
      </Card>

      {/* Tags */}
      {prototype.metadata.tags?.length && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Tags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {prototype.metadata.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button variant="outline" size="sm" className="w-full">
            <Eye className="h-4 w-4 mr-2" />
            View Feedback
          </Button>
          <Button variant="outline" size="sm" className="w-full">
            <MessageSquare className="h-4 w-4 mr-2" />
            Add Comment
          </Button>
          <Button variant="outline" size="sm" className="w-full">
            <Layers className="h-4 w-4 mr-2" />
            Compare Versions
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
