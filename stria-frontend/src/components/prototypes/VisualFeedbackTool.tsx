'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  MessageSquare,
  MapPin,
  Square,
  Circle,
  ArrowRight,
  Highlighter,
  Bug,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  X,
  Save,
  Trash2,
  Edit3,
  Eye,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Visual Feedback Tool Component
 * STRIA-142: 可视化标注反馈工具
 * 
 * Interactive tool for adding visual feedback annotations on prototypes
 * Supports click positioning, drag annotation, multiple annotation types
 */

export interface FeedbackAnnotation {
  id: string;
  type: 'comment' | 'bug' | 'suggestion' | 'question' | 'approval';
  annotationType: 'point' | 'rectangle' | 'circle' | 'arrow' | 'highlight';
  positionX: number;
  positionY: number;
  width?: number;
  height?: number;
  content: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  createdAt: string;
  createdBy: string;
  author: {
    name: string;
    email: string;
  };
  metadata?: {
    color?: string;
    tags?: string[];
    attachments?: string[];
  };
}

interface VisualFeedbackToolProps {
  prototypeId: string;
  isEnabled: boolean;
  annotations: FeedbackAnnotation[];
  onAnnotationCreate: (annotation: Omit<FeedbackAnnotation, 'id' | 'createdAt' | 'createdBy' | 'author'>) => void;
  onAnnotationUpdate: (id: string, updates: Partial<FeedbackAnnotation>) => void;
  onAnnotationDelete: (id: string) => void;
  className?: string;
}

interface ToolState {
  mode: 'view' | 'point' | 'rectangle' | 'circle' | 'arrow' | 'highlight';
  isDrawing: boolean;
  currentAnnotation: Partial<FeedbackAnnotation> | null;
  selectedAnnotation: string | null;
  showAnnotations: boolean;
  feedbackType: FeedbackAnnotation['type'];
  priority: FeedbackAnnotation['priority'];
}

interface DrawingState {
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
}

export function VisualFeedbackTool({
  prototypeId,
  isEnabled,
  annotations,
  onAnnotationCreate,
  onAnnotationUpdate,
  onAnnotationDelete,
  className
}: VisualFeedbackToolProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [toolState, setToolState] = useState<ToolState>({
    mode: 'view',
    isDrawing: false,
    currentAnnotation: null,
    selectedAnnotation: null,
    showAnnotations: true,
    feedbackType: 'comment',
    priority: 'medium',
  });
  const [drawingState, setDrawingState] = useState<DrawingState | null>(null);
  const [commentInput, setCommentInput] = useState('');
  const [showCommentForm, setShowCommentForm] = useState(false);

  // Handle mouse down for starting annotation
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (!isEnabled || toolState.mode === 'view') return;

    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    if (toolState.mode === 'point') {
      // For point annotations, create immediately
      setToolState(prev => ({ ...prev, currentAnnotation: {
        type: prev.feedbackType,
        annotationType: 'point',
        positionX: x,
        positionY: y,
        priority: prev.priority,
        status: 'open',
        content: '',
      }}));
      setShowCommentForm(true);
    } else {
      // For shape annotations, start drawing
      setDrawingState({ startX: x, startY: y, currentX: x, currentY: y });
      setToolState(prev => ({ ...prev, isDrawing: true }));
    }
  }, [isEnabled, toolState.mode, toolState.feedbackType, toolState.priority]);

  // Handle mouse move for drawing
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!toolState.isDrawing || !drawingState) return;

    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setDrawingState(prev => prev ? { ...prev, currentX: x, currentY: y } : null);
  }, [toolState.isDrawing, drawingState]);

  // Handle mouse up for finishing annotation
  const handleMouseUp = useCallback(() => {
    if (!toolState.isDrawing || !drawingState) return;

    const { startX, startY, currentX, currentY } = drawingState;
    const width = Math.abs(currentX - startX);
    const height = Math.abs(currentY - startY);

    // Only create annotation if it has meaningful size
    if (width > 10 || height > 10) {
      setToolState(prev => ({ ...prev, currentAnnotation: {
        type: prev.feedbackType,
        annotationType: prev.mode as FeedbackAnnotation['annotationType'],
        positionX: Math.min(startX, currentX),
        positionY: Math.min(startY, currentY),
        width,
        height,
        priority: prev.priority,
        status: 'open',
        content: '',
      }}));
      setShowCommentForm(true);
    }

    setDrawingState(null);
    setToolState(prev => ({ ...prev, isDrawing: false }));
  }, [toolState.isDrawing, drawingState, toolState.feedbackType, toolState.priority, toolState.mode]);

  // Handle annotation creation
  const handleCreateAnnotation = () => {
    if (!toolState.currentAnnotation || !commentInput.trim()) return;

    onAnnotationCreate({
      ...toolState.currentAnnotation,
      content: commentInput.trim(),
    } as Omit<FeedbackAnnotation, 'id' | 'createdAt' | 'createdBy' | 'author'>);

    // Reset state
    setToolState(prev => ({ 
      ...prev, 
      currentAnnotation: null,
      mode: 'view'
    }));
    setCommentInput('');
    setShowCommentForm(false);
  };

  // Handle annotation selection
  const handleAnnotationClick = (annotationId: string) => {
    setToolState(prev => ({ 
      ...prev, 
      selectedAnnotation: prev.selectedAnnotation === annotationId ? null : annotationId 
    }));
  };

  // Get annotation style
  const getAnnotationStyle = (annotation: FeedbackAnnotation) => {
    const baseStyle = {
      position: 'absolute' as const,
      left: annotation.positionX,
      top: annotation.positionY,
      width: annotation.width || 'auto',
      height: annotation.height || 'auto',
      pointerEvents: 'all' as const,
      zIndex: 10,
    };

    const colors = {
      comment: 'border-blue-500 bg-blue-500/10',
      bug: 'border-red-500 bg-red-500/10',
      suggestion: 'border-green-500 bg-green-500/10',
      question: 'border-yellow-500 bg-yellow-500/10',
      approval: 'border-purple-500 bg-purple-500/10',
    };

    return {
      ...baseStyle,
      borderWidth: 2,
      borderStyle: 'solid',
      borderRadius: annotation.annotationType === 'circle' ? '50%' : '4px',
      className: colors[annotation.type],
    };
  };

  // Get feedback type icon
  const getFeedbackIcon = (type: FeedbackAnnotation['type']) => {
    const icons = {
      comment: MessageSquare,
      bug: Bug,
      suggestion: Lightbulb,
      question: AlertTriangle,
      approval: CheckCircle,
    };
    return icons[type];
  };

  // Get priority color
  const getPriorityColor = (priority: FeedbackAnnotation['priority']) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      critical: 'bg-red-100 text-red-800',
    };
    return colors[priority];
  };

  return (
    <div className={cn("relative", className)}>
      {/* Toolbar */}
      {isEnabled && (
        <div className="absolute top-4 left-4 z-20 bg-white rounded-lg shadow-lg border p-2">
          <div className="flex items-center gap-2 mb-2">
            {/* Mode Buttons */}
            <Button
              variant={toolState.mode === 'view' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setToolState(prev => ({ ...prev, mode: 'view' }))}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant={toolState.mode === 'point' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setToolState(prev => ({ ...prev, mode: 'point' }))}
            >
              <MapPin className="h-4 w-4" />
            </Button>
            <Button
              variant={toolState.mode === 'rectangle' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setToolState(prev => ({ ...prev, mode: 'rectangle' }))}
            >
              <Square className="h-4 w-4" />
            </Button>
            <Button
              variant={toolState.mode === 'circle' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setToolState(prev => ({ ...prev, mode: 'circle' }))}
            >
              <Circle className="h-4 w-4" />
            </Button>
            <Button
              variant={toolState.mode === 'highlight' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setToolState(prev => ({ ...prev, mode: 'highlight' }))}
            >
              <Highlighter className="h-4 w-4" />
            </Button>
          </div>

          {/* Feedback Type Selection */}
          <div className="flex items-center gap-1 mb-2">
            {(['comment', 'bug', 'suggestion', 'question', 'approval'] as const).map((type) => {
              const Icon = getFeedbackIcon(type);
              return (
                <Button
                  key={type}
                  variant={toolState.feedbackType === type ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setToolState(prev => ({ ...prev, feedbackType: type }))}
                  title={type}
                >
                  <Icon className="h-4 w-4" />
                </Button>
              );
            })}
          </div>

          {/* Priority Selection */}
          <div className="flex items-center gap-1">
            {(['low', 'medium', 'high', 'critical'] as const).map((priority) => (
              <Button
                key={priority}
                variant={toolState.priority === priority ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setToolState(prev => ({ ...prev, priority }))}
                className="text-xs"
              >
                {priority}
              </Button>
            ))}
          </div>

          {/* Toggle Annotations Visibility */}
          <div className="mt-2 pt-2 border-t">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setToolState(prev => ({ ...prev, showAnnotations: !prev.showAnnotations }))}
              className="w-full"
            >
              {toolState.showAnnotations ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
              {toolState.showAnnotations ? 'Hide' : 'Show'} Annotations
            </Button>
          </div>
        </div>
      )}

      {/* Main Container */}
      <div
        ref={containerRef}
        className="relative w-full h-full cursor-crosshair"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        style={{ cursor: toolState.mode === 'view' ? 'default' : 'crosshair' }}
      >
        {/* Existing Annotations */}
        {toolState.showAnnotations && annotations.map((annotation) => {
          const style = getAnnotationStyle(annotation);
          const Icon = getFeedbackIcon(annotation.type);
          const isSelected = toolState.selectedAnnotation === annotation.id;

          return (
            <div key={annotation.id}>
              {/* Annotation Shape */}
              <div
                style={style}
                className={cn(
                  style.className,
                  'border-2 cursor-pointer transition-all',
                  isSelected && 'ring-2 ring-blue-400 ring-offset-2'
                )}
                onClick={() => handleAnnotationClick(annotation.id)}
              />

              {/* Annotation Marker */}
              <div
                style={{
                  position: 'absolute',
                  left: annotation.positionX - 12,
                  top: annotation.positionY - 12,
                  zIndex: 15,
                }}
                className={cn(
                  'w-6 h-6 rounded-full border-2 border-white shadow-lg flex items-center justify-center cursor-pointer',
                  annotation.type === 'comment' && 'bg-blue-500',
                  annotation.type === 'bug' && 'bg-red-500',
                  annotation.type === 'suggestion' && 'bg-green-500',
                  annotation.type === 'question' && 'bg-yellow-500',
                  annotation.type === 'approval' && 'bg-purple-500',
                )}
                onClick={() => handleAnnotationClick(annotation.id)}
              >
                <Icon className="h-3 w-3 text-white" />
              </div>

              {/* Annotation Details */}
              {isSelected && (
                <div
                  style={{
                    position: 'absolute',
                    left: annotation.positionX + 20,
                    top: annotation.positionY - 10,
                    zIndex: 20,
                  }}
                  className="w-80"
                >
                  <Card className="shadow-lg">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          {annotation.type}
                        </CardTitle>
                        <div className="flex items-center gap-2">
                          <Badge className={cn("text-xs", getPriorityColor(annotation.priority))}>
                            {annotation.priority}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setToolState(prev => ({ ...prev, selectedAnnotation: null }))}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <p className="text-sm text-gray-700">{annotation.content}</p>
                      <div className="text-xs text-gray-500">
                        By {annotation.author.name} • {new Date(annotation.createdAt).toLocaleDateString()}
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit3 className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => onAnnotationDelete(annotation.id)}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          );
        })}

        {/* Current Drawing */}
        {toolState.isDrawing && drawingState && (
          <div
            style={{
              position: 'absolute',
              left: Math.min(drawingState.startX, drawingState.currentX),
              top: Math.min(drawingState.startY, drawingState.currentY),
              width: Math.abs(drawingState.currentX - drawingState.startX),
              height: Math.abs(drawingState.currentY - drawingState.startY),
              border: '2px dashed #3b82f6',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              borderRadius: toolState.mode === 'circle' ? '50%' : '4px',
              pointerEvents: 'none',
              zIndex: 10,
            }}
          />
        )}
      </div>

      {/* Comment Form Modal */}
      {showCommentForm && toolState.currentAnnotation && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-96">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {React.createElement(getFeedbackIcon(toolState.currentAnnotation.type!), { className: "h-5 w-5" })}
                Add {toolState.currentAnnotation.type}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Comment</label>
                <textarea
                  value={commentInput}
                  onChange={(e) => setCommentInput(e.target.value)}
                  placeholder="Describe your feedback..."
                  className="w-full mt-1 p-2 border rounded-md resize-none"
                  rows={3}
                  autoFocus
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowCommentForm(false);
                    setCommentInput('');
                    setToolState(prev => ({ ...prev, currentAnnotation: null, mode: 'view' }));
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateAnnotation}
                  disabled={!commentInput.trim()}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Feedback
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
