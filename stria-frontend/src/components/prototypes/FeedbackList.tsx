'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  CheckCircle,
  AlertTriangle,
  MessageSquare,
  Lightbulb,
  Clock,
  User,
  Calendar,
  Search,
  Filter,
  Eye,
  Edit3,
  Trash2,
  CheckSquare,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Feedback List Component
 * STRIA-142: 轻量级反馈收集方案
 * 
 * List component for displaying and managing business feedback
 */

export interface BusinessFeedbackItem {
  id: string;
  title: string;
  description: string;
  type: 'approval_decision' | 'change_request' | 'clarification_needed' | 'milestone_feedback';
  priority: 'low' | 'medium' | 'high' | 'blocking';
  status: 'submitted' | 'under_review' | 'approved' | 'changes_requested' | 'resolved';
  submittedBy: {
    id: string;
    name: string;
    email: string;
  };
  assignedTo?: {
    id: string;
    name: string;
    email: string;
  };
  submittedAt: string;
  dueDate?: string;
  screenReference?: string;
  isOverdue: boolean;
  daysSinceSubmission: number;
}

interface FeedbackListProps {
  feedbacks: BusinessFeedbackItem[];
  isLoading?: boolean;
  selectedFeedback?: string | null;
  onFeedbackSelect: (id: string) => void;
  onFeedbackUpdate: (id: string, updates: Partial<BusinessFeedbackItem>) => void;
  onFeedbackDelete: (id: string) => void;
  onFeedbackView: (id: string) => void;
  className?: string;
}

interface FilterState {
  search: string;
  type: string;
  status: string;
  priority: string;
  assignedTo: string;
}

export function FeedbackList({
  feedbacks,
  isLoading = false,
  selectedFeedback,
  onFeedbackSelect,
  onFeedbackUpdate,
  onFeedbackDelete,
  onFeedbackView,
  className
}: FeedbackListProps) {
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    type: 'all',
    status: 'all',
    priority: 'all',
    assignedTo: 'all',
  });

  // Get feedback type info
  const getFeedbackTypeInfo = (type: BusinessFeedbackItem['type']) => {
    const types = {
      approval_decision: { label: '批准决策', icon: CheckCircle, color: 'bg-green-100 text-green-800' },
      change_request: { label: '变更请求', icon: AlertTriangle, color: 'bg-orange-100 text-orange-800' },
      clarification_needed: { label: '需要澄清', icon: MessageSquare, color: 'bg-blue-100 text-blue-800' },
      milestone_feedback: { label: '里程碑反馈', icon: Lightbulb, color: 'bg-purple-100 text-purple-800' },
    };
    return types[type];
  };

  // Get priority color
  const getPriorityColor = (priority: BusinessFeedbackItem['priority']) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      blocking: 'bg-red-100 text-red-800',
    };
    return colors[priority];
  };

  // Get status color
  const getStatusColor = (status: BusinessFeedbackItem['status']) => {
    const colors = {
      submitted: 'bg-yellow-100 text-yellow-800',
      under_review: 'bg-blue-100 text-blue-800',
      approved: 'bg-green-100 text-green-800',
      changes_requested: 'bg-orange-100 text-orange-800',
      resolved: 'bg-gray-100 text-gray-800',
    };
    return colors[status];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Filter feedbacks
  const filteredFeedbacks = feedbacks.filter(feedback => {
    if (filters.search && !feedback.title.toLowerCase().includes(filters.search.toLowerCase()) &&
        !feedback.description.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }
    if (filters.type !== 'all' && feedback.type !== filters.type) return false;
    if (filters.status !== 'all' && feedback.status !== filters.status) return false;
    if (filters.priority !== 'all' && feedback.priority !== filters.priority) return false;
    if (filters.assignedTo !== 'all' && feedback.assignedTo?.id !== filters.assignedTo) return false;
    return true;
  });

  // Group feedbacks by status
  const groupedFeedbacks = filteredFeedbacks.reduce((groups, feedback) => {
    const status = feedback.status;
    if (!groups[status]) groups[status] = [];
    groups[status].push(feedback);
    return groups;
  }, {} as Record<string, BusinessFeedbackItem[]>);

  if (isLoading) {
    return (
      <div className={cn("p-6", className)}>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-24 bg-gray-200 rounded-lg"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">反馈筛选</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="搜索反馈..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10"
            />
          </div>

          {/* Filter Buttons */}
          <div className="flex flex-wrap gap-2">
            <select
              value={filters.type}
              onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
              className="text-sm border rounded px-3 py-1"
            >
              <option value="all">所有类型</option>
              <option value="approval_decision">批准决策</option>
              <option value="change_request">变更请求</option>
              <option value="clarification_needed">需要澄清</option>
              <option value="milestone_feedback">里程碑反馈</option>
            </select>

            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="text-sm border rounded px-3 py-1"
            >
              <option value="all">所有状态</option>
              <option value="submitted">已提交</option>
              <option value="under_review">审核中</option>
              <option value="approved">已批准</option>
              <option value="changes_requested">需要修改</option>
              <option value="resolved">已解决</option>
            </select>

            <select
              value={filters.priority}
              onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
              className="text-sm border rounded px-3 py-1"
            >
              <option value="all">所有优先级</option>
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
              <option value="blocking">阻塞</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Feedback Groups */}
      {Object.entries(groupedFeedbacks).map(([status, statusFeedbacks]) => (
        <div key={status}>
          <div className="flex items-center gap-2 mb-3">
            <h3 className="font-semibold text-lg">
              {status === 'submitted' ? '已提交' :
               status === 'under_review' ? '审核中' :
               status === 'approved' ? '已批准' :
               status === 'changes_requested' ? '需要修改' :
               status === 'resolved' ? '已解决' : status}
            </h3>
            <Badge variant="outline">{statusFeedbacks.length}</Badge>
          </div>

          <div className="space-y-3">
            {statusFeedbacks.map((feedback) => {
              const typeInfo = getFeedbackTypeInfo(feedback.type);
              const TypeIcon = typeInfo.icon;
              const isSelected = selectedFeedback === feedback.id;

              return (
                <Card
                  key={feedback.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    isSelected && "ring-2 ring-blue-400 bg-blue-50",
                    feedback.isOverdue && "border-l-4 border-l-red-500"
                  )}
                  onClick={() => onFeedbackSelect(feedback.id)}
                >
                  <CardContent className="p-4">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start gap-3 flex-1">
                        <TypeIcon className="h-5 w-5 mt-0.5 text-gray-600" />
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 truncate">
                            {feedback.title}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                            {feedback.description}
                          </p>
                        </div>
                      </div>
                      
                      {feedback.isOverdue && (
                        <AlertCircle className="h-5 w-5 text-red-500 ml-2" />
                      )}
                    </div>

                    {/* Badges */}
                    <div className="flex flex-wrap gap-2 mb-3">
                      <Badge className={cn("text-xs", typeInfo.color)}>
                        {typeInfo.label}
                      </Badge>
                      <Badge className={cn("text-xs", getPriorityColor(feedback.priority))}>
                        {feedback.priority === 'low' ? '低' :
                         feedback.priority === 'medium' ? '中' :
                         feedback.priority === 'high' ? '高' : '阻塞'}
                      </Badge>
                      <Badge className={cn("text-xs", getStatusColor(feedback.status))}>
                        {status === 'submitted' ? '已提交' :
                         status === 'under_review' ? '审核中' :
                         status === 'approved' ? '已批准' :
                         status === 'changes_requested' ? '需要修改' :
                         status === 'resolved' ? '已解决' : status}
                      </Badge>
                    </div>

                    {/* Meta Information */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>{feedback.submittedBy.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDate(feedback.submittedAt)}</span>
                        </div>
                        {feedback.screenReference && (
                          <div className="flex items-center gap-1">
                            <span>📍 {feedback.screenReference}</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{feedback.daysSinceSubmission}天前</span>
                      </div>
                    </div>

                    {/* Due Date Warning */}
                    {feedback.dueDate && (
                      <div className={cn(
                        "mt-2 text-xs flex items-center gap-1",
                        feedback.isOverdue ? "text-red-600" : "text-gray-500"
                      )}>
                        <Clock className="h-3 w-3" />
                        <span>
                          期望处理时间: {formatDate(feedback.dueDate)}
                          {feedback.isOverdue && " (已逾期)"}
                        </span>
                      </div>
                    )}

                    {/* Actions */}
                    {isSelected && (
                      <div className="flex gap-2 mt-3 pt-3 border-t">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onFeedbackView(feedback.id);
                          }}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          查看
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle edit
                          }}
                        >
                          <Edit3 className="h-3 w-3 mr-1" />
                          编辑
                        </Button>
                        {feedback.status !== 'resolved' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onFeedbackUpdate(feedback.id, { status: 'resolved' });
                            }}
                          >
                            <CheckSquare className="h-3 w-3 mr-1" />
                            标记解决
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onFeedbackDelete(feedback.id);
                          }}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          删除
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      ))}

      {/* Empty State */}
      {filteredFeedbacks.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <MessageSquare className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无反馈</h3>
            <p className="text-gray-600">
              {filters.search || filters.type !== 'all' || filters.status !== 'all' 
                ? '没有找到符合条件的反馈' 
                : '还没有收到任何反馈'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
