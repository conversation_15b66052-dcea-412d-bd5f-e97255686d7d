'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle,
  AlertTriangle,
  MessageSquare,
  Lightbulb,
  Clock,
  Save,
  X,
  Upload,
  Link,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Feedback Submission Form Component
 * STRIA-142: 轻量级反馈收集方案
 * 
 * Simple form for submitting structured business feedback
 * Focused on client-service provider communication
 */

export interface BusinessFeedback {
  title: string;
  description: string;
  type: 'approval_decision' | 'change_request' | 'clarification_needed' | 'milestone_feedback';
  priority: 'low' | 'medium' | 'high' | 'blocking';
  figmaPageUrl?: string;
  screenReference?: string;
  attachments?: string[];
  dueDate?: string;
  estimatedDelay?: number;
  resourceRequirement?: string;
  riskLevel?: 'low' | 'medium' | 'high';
}

interface FeedbackSubmissionFormProps {
  prototypeId: string;
  prototypeName: string;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (feedback: BusinessFeedback) => Promise<void>;
  className?: string;
}

interface FormState {
  currentStep: 1 | 2 | 3;
  isSubmitting: boolean;
  errors: Record<string, string>;
}

export function FeedbackSubmissionForm({
  prototypeId,
  prototypeName,
  isOpen,
  onClose,
  onSubmit,
  className
}: FeedbackSubmissionFormProps) {
  const [formData, setFormData] = useState<BusinessFeedback>({
    title: '',
    description: '',
    type: 'change_request',
    priority: 'medium',
    figmaPageUrl: '',
    screenReference: '',
    attachments: [],
    dueDate: '',
    estimatedDelay: 0,
    resourceRequirement: '',
    riskLevel: 'medium',
  });

  const [formState, setFormState] = useState<FormState>({
    currentStep: 1,
    isSubmitting: false,
    errors: {},
  });

  // Feedback type options
  const feedbackTypes = [
    {
      value: 'approval_decision' as const,
      label: '批准决策',
      description: '对当前设计的批准或拒绝决定',
      icon: CheckCircle,
      color: 'bg-green-100 text-green-800 border-green-200',
    },
    {
      value: 'change_request' as const,
      label: '变更请求',
      description: '需要修改或调整的具体要求',
      icon: AlertTriangle,
      color: 'bg-orange-100 text-orange-800 border-orange-200',
    },
    {
      value: 'clarification_needed' as const,
      label: '需要澄清',
      description: '对设计或功能的疑问和澄清需求',
      icon: MessageSquare,
      color: 'bg-blue-100 text-blue-800 border-blue-200',
    },
    {
      value: 'milestone_feedback' as const,
      label: '里程碑反馈',
      description: '项目阶段性成果的整体反馈',
      icon: Lightbulb,
      color: 'bg-purple-100 text-purple-800 border-purple-200',
    },
  ];

  // Priority options
  const priorityOptions = [
    { value: 'low' as const, label: '低', color: 'bg-gray-100 text-gray-800' },
    { value: 'medium' as const, label: '中', color: 'bg-blue-100 text-blue-800' },
    { value: 'high' as const, label: '高', color: 'bg-orange-100 text-orange-800' },
    { value: 'blocking' as const, label: '阻塞', color: 'bg-red-100 text-red-800' },
  ];

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    const errors: Record<string, string> = {};
    
    if (!formData.title.trim()) {
      errors.title = '请输入反馈标题';
    }
    
    if (!formData.description.trim()) {
      errors.description = '请输入详细描述';
    }

    if (Object.keys(errors).length > 0) {
      setFormState(prev => ({ ...prev, errors }));
      return;
    }

    try {
      setFormState(prev => ({ ...prev, isSubmitting: true, errors: {} }));
      await onSubmit(formData);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        type: 'change_request',
        priority: 'medium',
        figmaPageUrl: '',
        screenReference: '',
        attachments: [],
        dueDate: '',
        estimatedDelay: 0,
        resourceRequirement: '',
        riskLevel: 'medium',
      });
      setFormState({ currentStep: 1, isSubmitting: false, errors: {} });
      onClose();
    } catch (error) {
      setFormState(prev => ({ 
        ...prev, 
        isSubmitting: false, 
        errors: { submit: '提交失败，请重试' } 
      }));
    }
  };

  // Handle next step
  const handleNextStep = () => {
    if (formState.currentStep === 1) {
      // Validate step 1
      const errors: Record<string, string> = {};
      if (!formData.title.trim()) errors.title = '请输入反馈标题';
      if (!formData.description.trim()) errors.description = '请输入详细描述';
      
      if (Object.keys(errors).length > 0) {
        setFormState(prev => ({ ...prev, errors }));
        return;
      }
    }
    
    setFormState(prev => ({ 
      ...prev, 
      currentStep: (prev.currentStep + 1) as 1 | 2 | 3,
      errors: {} 
    }));
  };

  // Handle previous step
  const handlePrevStep = () => {
    setFormState(prev => ({ 
      ...prev, 
      currentStep: (prev.currentStep - 1) as 1 | 2 | 3,
      errors: {} 
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className={cn("w-full max-w-2xl max-h-[90vh] overflow-y-auto", className)}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">提交反馈</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                针对原型 "{prototypeName}" 的结构化反馈
              </p>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center gap-2 mt-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                  step <= formState.currentStep 
                    ? "bg-blue-600 text-white" 
                    : "bg-gray-200 text-gray-600"
                )}>
                  {step}
                </div>
                {step < 3 && (
                  <div className={cn(
                    "w-12 h-0.5 mx-2",
                    step < formState.currentStep ? "bg-blue-600" : "bg-gray-200"
                  )} />
                )}
              </div>
            ))}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Step 1: Feedback Type & Basic Info */}
          {formState.currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <h3 className="font-medium mb-3">选择反馈类型</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {feedbackTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                      <div
                        key={type.value}
                        className={cn(
                          "p-4 border-2 rounded-lg cursor-pointer transition-all",
                          formData.type === type.value 
                            ? type.color + " border-current" 
                            : "border-gray-200 hover:border-gray-300"
                        )}
                        onClick={() => setFormData(prev => ({ ...prev, type: type.value }))}
                      >
                        <div className="flex items-start gap-3">
                          <Icon className="h-5 w-5 mt-0.5" />
                          <div>
                            <div className="font-medium">{type.label}</div>
                            <div className="text-sm text-gray-600 mt-1">{type.description}</div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  反馈标题 <span className="text-red-500">*</span>
                </label>
                <Input
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="例如：登录页面布局需要调整"
                  className={formState.errors.title ? 'border-red-500' : ''}
                />
                {formState.errors.title && (
                  <p className="text-red-500 text-sm mt-1">{formState.errors.title}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  详细描述 <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="请详细描述您的反馈内容..."
                  rows={4}
                  className={cn(
                    "w-full px-3 py-2 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500",
                    formState.errors.description ? 'border-red-500' : 'border-gray-300'
                  )}
                />
                {formState.errors.description && (
                  <p className="text-red-500 text-sm mt-1">{formState.errors.description}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">优先级</label>
                <div className="flex gap-2">
                  {priorityOptions.map((option) => (
                    <Button
                      key={option.value}
                      variant={formData.priority === option.value ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setFormData(prev => ({ ...prev, priority: option.value }))}
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Context & References */}
          {formState.currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  <Link className="h-4 w-4 inline mr-2" />
                  Figma页面链接（可选）
                </label>
                <Input
                  value={formData.figmaPageUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, figmaPageUrl: e.target.value }))}
                  placeholder="https://www.figma.com/file/..."
                />
                <p className="text-sm text-gray-500 mt-1">
                  粘贴相关的Figma页面链接以便快速定位
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  页面/屏幕引用（可选）
                </label>
                <Input
                  value={formData.screenReference}
                  onChange={(e) => setFormData(prev => ({ ...prev, screenReference: e.target.value }))}
                  placeholder="例如：首页 > 登录模块"
                />
                <p className="text-sm text-gray-500 mt-1">
                  描述反馈涉及的具体页面或模块
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  <Calendar className="h-4 w-4 inline mr-2" />
                  期望处理时间（可选）
                </label>
                <Input
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  <Upload className="h-4 w-4 inline mr-2" />
                  附件（可选）
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                  <p className="text-sm text-gray-600">
                    点击上传参考图片或文档
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    支持 PNG, JPG, PDF 格式，最大 10MB
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Impact Assessment */}
          {formState.currentStep === 3 && (
            <div className="space-y-6">
              <div>
                <h3 className="font-medium mb-3">影响评估（可选）</h3>
                <p className="text-sm text-gray-600 mb-4">
                  帮助我们更好地理解此反馈的影响范围和处理优先级
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  预估延期时间（天）
                </label>
                <Input
                  type="number"
                  min="0"
                  value={formData.estimatedDelay}
                  onChange={(e) => setFormData(prev => ({ ...prev, estimatedDelay: parseInt(e.target.value) || 0 }))}
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  额外资源需求
                </label>
                <Input
                  value={formData.resourceRequirement}
                  onChange={(e) => setFormData(prev => ({ ...prev, resourceRequirement: e.target.value }))}
                  placeholder="例如：需要UI设计师额外2天时间"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">风险等级</label>
                <div className="flex gap-2">
                  {['low', 'medium', 'high'].map((risk) => (
                    <Button
                      key={risk}
                      variant={formData.riskLevel === risk ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setFormData(prev => ({ ...prev, riskLevel: risk as any }))}
                    >
                      {risk === 'low' ? '低' : risk === 'medium' ? '中' : '高'}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Summary */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">反馈摘要</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>类型：</strong>{feedbackTypes.find(t => t.value === formData.type)?.label}</div>
                  <div><strong>优先级：</strong>{priorityOptions.find(p => p.value === formData.priority)?.label}</div>
                  <div><strong>标题：</strong>{formData.title}</div>
                  <div><strong>描述：</strong>{formData.description.substring(0, 100)}{formData.description.length > 100 ? '...' : ''}</div>
                </div>
              </div>
            </div>
          )}

          {/* Error Message */}
          {formState.errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-700 text-sm">{formState.errors.submit}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <div>
              {formState.currentStep > 1 && (
                <Button variant="outline" onClick={handlePrevStep}>
                  上一步
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                取消
              </Button>
              {formState.currentStep < 3 ? (
                <Button onClick={handleNextStep}>
                  下一步
                </Button>
              ) : (
                <Button 
                  onClick={handleSubmit}
                  disabled={formState.isSubmitting}
                >
                  {formState.isSubmitting ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      提交中...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      提交反馈
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
