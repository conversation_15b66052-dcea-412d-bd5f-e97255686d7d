---
type: "always_apply"
description: "Core architecture standards and directory structure principles for Stria project"
---
# Stria项目架构标准规则

## 核心架构原则

### 1. 微服务模块化设计
- 使用NestJS模块化架构，按业务域划分服务
- 每个模块负责单一业务职责
- 模块间通过明确定义的接口通信
- 避免循环依赖，保持清晰的依赖关系

### 2. API-First开发方法论
- 所有功能先设计和实现API端点，再开发前端界面
- 使用OpenAPI/Swagger规范定义API
- API设计遵循RESTful原则
- 版本控制策略：URL版本控制 `/api/v1/`

### 3. 容器化部署架构
- 所有服务使用Docker容器化
- 使用docker-compose管理本地开发环境
- 生产环境使用AWS ECS或EKS
- 严格区分开发、测试、生产环境

## 目录结构核心原则

### 前端架构 (Next.js)
```
frontend/
├── src/
│   ├── app/                 # Next.js 14+ App Router
│   ├── components/          # 可复用组件
│   ├── lib/                # 工具函数和配置
│   ├── hooks/              # 自定义React Hooks
│   └── types/              # TypeScript类型定义
├── public/                 # 静态资源
└── docs/                   # 前端文档
```

### 后端架构 (NestJS)
```
backend/
├── src/
│   ├── modules/            # 业务模块
│   ├── common/             # 共享组件
│   ├── config/             # 配置文件
│   └── database/           # 数据库相关
├── test/                   # 测试文件
└── docs/                   # API文档
```

### 项目根目录
```
stria/
├── frontend/               # Next.js前端应用
├── backend/                # NestJS后端应用
├── shared/                 # 共享类型和工具
├── docker/                 # Docker配置文件
├── docs/                   # 项目文档
├── scripts/                # 构建和部署脚本
└── .github/                # GitHub Actions CI/CD
```

## 业务模块划分原则

### 核心业务域
- **auth** - 用户认证和授权
- **projects** - 项目管理
- **requirements** - 需求分析
- **prototypes** - 原型管理
- **communications** - 沟通中心
- **billing** - 账单和支付
- **admin** - 内部管理

### 模块结构标准
```
src/modules/[domain]/
├── controllers/            # 控制器
├── services/              # 业务逻辑
├── entities/              # 数据实体
├── dto/                   # 数据传输对象
├── guards/                # 权限守卫
├── decorators/            # 自定义装饰器
└── [domain].module.ts     # 模块定义
```

## 配置管理原则

### 环境配置
- 使用.env文件管理环境变量
- 敏感信息使用AWS Secrets Manager
- 配置验证使用Joi或class-validator
- 不同环境使用不同的配置文件

### Docker配置
- 开发环境：docker-compose.dev.yml
- 测试环境：docker-compose.test.yml
- 生产环境：docker-compose.prod.yml

## 安全架构原则

### 数据保护
- 所有敏感数据传输使用HTTPS
- 数据库连接使用SSL
- 密码使用bcrypt加密
- JWT token使用RS256签名

### 访问控制
- 实现基于角色的访问控制(RBAC)
- API端点使用Guards进行权限验证
- 审计日志记录所有关键操作

## 性能架构原则

### 缓存策略
- 使用Redis进行数据缓存
- API响应缓存使用适当的TTL
- 静态资源使用CDN分发

### 数据库优化
- 合理设计索引策略
- 避免N+1查询问题
- 使用连接池管理数据库连接

## 架构检查清单

### 新模块创建检查
- [ ] 模块职责单一且明确
- [ ] 遵循标准目录结构
- [ ] 包含完整的DTO定义
- [ ] 实现适当的错误处理
- [ ] 包含单元测试

### API设计检查
- [ ] 遵循RESTful设计原则
- [ ] 包含OpenAPI文档
- [ ] 实现适当的验证
- [ ] 包含错误响应定义
- [ ] 考虑版本控制策略

### 安全检查
- [ ] 实现适当的认证机制
- [ ] 包含权限验证
- [ ] 敏感数据正确加密
- [ ] 包含审计日志
- [ ] 通过安全扫描

## 详细指南参考

完整的架构指南请参考：
- `docs/directory-structure-guide.md` - 详细目录结构说明
- `docs/api-design-guide.md` - API设计详细指南
- `docs/database-design-guide.md` - 数据库设计规范

## Stria特定约束

### 业务架构约束
- 禁止实时通信功能（聊天、视频会议）
- 所有交互必须是异步的
- 使用结构化表单和评论系统

### 技术约束
- 支付功能必须符合PCI DSS标准
- 使用Stripe托管支付处理
- 视频文件使用第三方服务(Vimeo/Mux)
- 部署在AWS美国区域
