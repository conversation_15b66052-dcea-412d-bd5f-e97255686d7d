# Sprint 8: Prototype Integration & Feedback Tools - Database Schema

## Overview

This document describes the database schema for Sprint 8's prototype integration and visual feedback functionality. The schema supports Figma integration, version management, visual feedback annotations, approval workflows, and threaded discussions.

## Entity Relationship Diagram

```
┌─────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Projects  │────│   Prototypes     │────│ PrototypeVersions│
└─────────────┘    └──────────────────┘    └─────────────────┘
                           │                         │
                           │                         │
                   ┌───────┴────────┐               │
                   │                │               │
            ┌──────▼──────┐  ┌──────▼──────┐       │
            │VisualFeedback│  │DesignApproval│       │
            │             │  │  Workflows   │       │
            └─────────────┘  └─────────────┘       │
                   │                 │              │
                   │                 │              │
            ┌──────▼──────┐  ┌──────▼──────┐       │
            │CommentDiscuss│  │ApprovalSteps│       │
            │    ions     │  └─────────────┘       │
            └─────────────┘         │               │
                   │                │               │
            ┌──────▼──────┐  ┌──────▼──────┐       │
            │CommentMention│  │ApprovalActions│      │
            └─────────────┘  └─────────────┘       │
                                                   │
                            ┌──────────────────────┘
                            │
                    ┌───────▼────────┐
                    │  VisualFeedback │
                    │   (versions)    │
                    └────────────────┘
```

## Core Entities

### 1. Prototypes

**Purpose**: Main entity for design prototypes with Figma integration

**Key Fields**:
- `id` (UUID, PK): Unique identifier
- `name` (VARCHAR): Prototype name
- `description` (TEXT): Optional description
- `type` (ENUM): Prototype type (wireframe, low_fidelity, high_fidelity, etc.)
- `status` (ENUM): Current status (draft, in_review, approved, etc.)
- `figma_file_id` (VARCHAR): Figma file identifier
- `figma_file_url` (TEXT): Direct Figma file URL
- `figma_embed_url` (TEXT): Embeddable Figma URL
- `figma_integration_status` (ENUM): Sync status with Figma
- `figma_last_sync` (TIMESTAMP): Last synchronization time
- `current_version` (VARCHAR): Current version number
- `version_count` (INTEGER): Total number of versions
- `project_id` (UUID, FK): Reference to project
- `created_by` (UUID, FK): Creator user ID
- `metadata` (JSONB): Additional configuration data

**Relationships**:
- Belongs to Project (Many-to-One)
- Has many PrototypeVersions (One-to-Many)
- Has many VisualFeedbacks (One-to-Many)
- Has many DesignApprovalWorkflows (One-to-Many)

**Indexes**:
- `idx_prototype_type` on `type`
- `idx_prototype_status` on `status`
- `idx_prototype_figma_file_id` on `figma_file_id`
- `idx_prototype_project_id` on `project_id`

### 2. PrototypeVersions

**Purpose**: Version control for prototypes with branching support

**Key Fields**:
- `id` (UUID, PK): Unique identifier
- `version_number` (VARCHAR): Version identifier (e.g., "2.1.0")
- `version_name` (VARCHAR): Optional human-readable name
- `description` (TEXT): Version description
- `type` (ENUM): Version type (major, minor, patch, feature, etc.)
- `status` (ENUM): Version status (draft, approved, merged, etc.)
- `parent_version_id` (UUID, FK): Parent version for branching
- `branch_name` (VARCHAR): Branch identifier
- `is_main_branch` (BOOLEAN): Whether this is the main branch
- `figma_version_id` (VARCHAR): Figma version identifier
- `figma_snapshot_url` (TEXT): Snapshot URL from Figma
- `changes_summary` (TEXT): Summary of changes
- `diff_data` (JSONB): Detailed change information
- `prototype_id` (UUID, FK): Reference to prototype
- `created_by` (UUID, FK): Creator user ID

**Relationships**:
- Belongs to Prototype (Many-to-One)
- Self-referencing for parent-child version relationships
- Has many VisualFeedbacks (One-to-Many)

**Indexes**:
- `idx_prototype_version_number` on `version_number`
- `idx_prototype_version_prototype_id` on `prototype_id`
- `idx_prototype_version_parent` on `parent_version_id`

### 3. VisualFeedbacks

**Purpose**: Coordinate-based visual annotations and feedback on prototypes

**Key Fields**:
- `id` (UUID, PK): Unique identifier
- `title` (VARCHAR): Optional feedback title
- `content` (TEXT): Feedback content
- `type` (ENUM): Feedback type (bug, improvement, suggestion, etc.)
- `status` (ENUM): Current status (open, in_progress, resolved, etc.)
- `priority` (ENUM): Priority level (low, medium, high, critical, blocker)
- `annotation_type` (ENUM): Visual annotation type (point, rectangle, circle, etc.)
- `position_x` (DECIMAL): X coordinate
- `position_y` (DECIMAL): Y coordinate
- `position_z` (INTEGER): Z-index for layering
- `width` (DECIMAL): Annotation width (for shapes)
- `height` (DECIMAL): Annotation height (for shapes)
- `color` (VARCHAR): Annotation color (hex code)
- `border_color` (VARCHAR): Border color
- `background_color` (VARCHAR): Background color
- `opacity` (DECIMAL): Opacity level (0.0-1.0)
- `target_element_id` (VARCHAR): Target element identifier
- `prototype_id` (UUID, FK): Reference to prototype
- `version_id` (UUID, FK): Optional version reference
- `created_by` (UUID, FK): Creator user ID
- `assigned_to` (UUID, FK): Assigned user ID
- `resolved_by` (UUID, FK): Resolver user ID

**Relationships**:
- Belongs to Prototype (Many-to-One)
- Belongs to PrototypeVersion (Many-to-One, optional)
- Has many CommentDiscussions (One-to-Many)

**Indexes**:
- `idx_visual_feedback_type` on `type`
- `idx_visual_feedback_status` on `status`
- `idx_visual_feedback_prototype_id` on `prototype_id`
- `idx_visual_feedback_target_element` on `target_element_id`

### 4. DesignApprovalWorkflows

**Purpose**: Multi-step approval processes for design prototypes

**Key Fields**:
- `id` (UUID, PK): Unique identifier
- `name` (VARCHAR): Workflow name
- `description` (TEXT): Workflow description
- `type` (ENUM): Workflow type (sequential, parallel, hybrid, etc.)
- `status` (ENUM): Current status (draft, active, completed, etc.)
- `approval_strategy` (ENUM): How approvals are evaluated
- `required_approvals` (INTEGER): Number of required approvals
- `allow_parallel_approval` (BOOLEAN): Allow parallel processing
- `approval_threshold_percentage` (DECIMAL): Threshold for auto-approval
- `due_date` (TIMESTAMP): Workflow due date
- `current_step` (INTEGER): Current step number
- `total_steps` (INTEGER): Total number of steps
- `completed_steps` (INTEGER): Number of completed steps
- `prototype_id` (UUID, FK): Reference to prototype
- `created_by` (UUID, FK): Creator user ID

**Relationships**:
- Belongs to Prototype (Many-to-One)
- Has many ApprovalSteps (One-to-Many)

**Indexes**:
- `idx_approval_workflow_type` on `type`
- `idx_approval_workflow_status` on `status`
- `idx_approval_workflow_prototype_id` on `prototype_id`

### 5. ApprovalSteps

**Purpose**: Individual steps within approval workflows

**Key Fields**:
- `id` (UUID, PK): Unique identifier
- `step_number` (INTEGER): Step sequence number
- `step_name` (VARCHAR): Step name
- `description` (TEXT): Step description
- `type` (ENUM): Step type (review, approval, feedback, etc.)
- `status` (ENUM): Current status (pending, in_progress, completed, etc.)
- `is_required` (BOOLEAN): Whether step is required
- `required_approvers` (INTEGER): Number of required approvers
- `due_date` (TIMESTAMP): Step due date
- `depends_on_steps` (JSONB): Array of dependent step IDs
- `approval_count` (INTEGER): Current approval count
- `rejection_count` (INTEGER): Current rejection count
- `final_decision` (ENUM): Final decision for the step
- `workflow_id` (UUID, FK): Reference to workflow
- `assigned_to` (UUID, FK): Assigned user ID

**Relationships**:
- Belongs to DesignApprovalWorkflow (Many-to-One)
- Has many ApprovalActions (One-to-Many)

**Indexes**:
- `idx_approval_step_workflow_id` on `workflow_id`
- `idx_approval_step_number` on `step_number`
- `idx_approval_step_status` on `status`

### 6. ApprovalActions

**Purpose**: Individual approval actions taken by reviewers

**Key Fields**:
- `id` (UUID, PK): Unique identifier
- `type` (ENUM): Action type (approve, reject, comment, delegate, etc.)
- `decision` (ENUM): Approval decision
- `comment` (TEXT): Optional comment
- `reason` (TEXT): Reason for decision
- `is_conditional` (BOOLEAN): Whether approval is conditional
- `conditions` (JSONB): Array of conditions if conditional
- `step_id` (UUID, FK): Reference to approval step
- `user_id` (UUID, FK): User who took the action
- `delegated_by` (UUID, FK): User who delegated (if applicable)

**Relationships**:
- Belongs to ApprovalStep (Many-to-One)
- Belongs to User (Many-to-One)

**Indexes**:
- `idx_approval_action_step_id` on `step_id`
- `idx_approval_action_user_id` on `user_id`
- `idx_approval_action_type` on `type`

### 7. CommentDiscussions

**Purpose**: Threaded discussions and comments on prototypes and feedback

**Key Fields**:
- `id` (UUID, PK): Unique identifier
- `content` (TEXT): Comment content
- `formatted_content` (TEXT): HTML/Markdown formatted content
- `type` (ENUM): Comment type (general, feedback, question, etc.)
- `status` (ENUM): Comment status (active, edited, deleted, etc.)
- `discussion_status` (ENUM): Discussion thread status
- `thread_depth` (INTEGER): Depth in thread hierarchy
- `reply_count` (INTEGER): Number of replies
- `visual_feedback_id` (UUID, FK): Reference to visual feedback
- `prototype_id` (UUID, FK): Reference to prototype
- `author_id` (UUID, FK): Comment author
- `parent_id` (UUID, FK): Parent comment for threading
- `like_count` (INTEGER): Number of likes
- `is_pinned` (BOOLEAN): Whether comment is pinned

**Relationships**:
- Belongs to VisualFeedback (Many-to-One, optional)
- Belongs to Prototype (Many-to-One, optional)
- Self-referencing for threaded discussions
- Has many CommentMentions (One-to-Many)

**Indexes**:
- `idx_comment_discussion_visual_feedback_id` on `visual_feedback_id`
- `idx_comment_discussion_prototype_id` on `prototype_id`
- `idx_comment_discussion_author_id` on `author_id`
- `idx_comment_discussion_parent_id` on `parent_id`

### 8. CommentMentions

**Purpose**: User mentions in comments for notification purposes

**Key Fields**:
- `id` (UUID, PK): Unique identifier
- `type` (ENUM): Mention type (user, team, role, etc.)
- `status` (ENUM): Mention status (pending, notified, read, etc.)
- `start_position` (INTEGER): Start position in content
- `end_position` (INTEGER): End position in content
- `mention_text` (VARCHAR): The mention text (e.g., "@john.doe")
- `comment_id` (UUID, FK): Reference to comment
- `mentioned_user_id` (UUID, FK): Mentioned user
- `mentioned_by_user_id` (UUID, FK): User who made the mention
- `notification_sent` (BOOLEAN): Whether notification was sent
- `notification_read` (BOOLEAN): Whether notification was read

**Relationships**:
- Belongs to CommentDiscussion (Many-to-One)
- Belongs to User (Many-to-One) for mentioned user
- Belongs to User (Many-to-One) for mentioning user

**Indexes**:
- `idx_comment_mention_comment_id` on `comment_id`
- `idx_comment_mention_mentioned_user_id` on `mentioned_user_id`
- `idx_comment_mention_status` on `status`

## Performance Considerations

### Indexing Strategy

1. **Primary Access Patterns**:
   - Prototypes by project: `idx_prototype_project_id`
   - Visual feedback by prototype: `idx_visual_feedback_prototype_id`
   - Comments by feedback: `idx_comment_discussion_visual_feedback_id`
   - Approval workflows by prototype: `idx_approval_workflow_prototype_id`

2. **Status-based Queries**:
   - All entities have status indexes for filtering
   - Compound indexes on (entity_id, status) for common queries

3. **User-based Queries**:
   - Created by, assigned to, and updated by fields are indexed
   - Mention notifications indexed by user and status

### Data Archival

- All entities support soft delete with `deleted_at` and `deleted_by` fields
- Completed workflows and resolved feedback can be archived
- Version history is preserved for audit purposes

## Security Considerations

### Access Control

- All entities link to users through foreign keys
- Project-level access controls inherited by prototypes
- Approval workflows enforce role-based permissions
- Comment visibility controlled by prototype access

### Data Integrity

- Foreign key constraints ensure referential integrity
- Enum types prevent invalid status values
- JSONB validation for metadata fields
- Audit trail through created_by/updated_by fields

## Migration Strategy

The schema is implemented through three migration files:

1. `1706000000000-CreatePrototypeEntities.ts`: Core prototype and feedback entities
2. `1706000001000-CreateApprovalWorkflowEntities.ts`: Approval workflow entities
3. `1706000002000-CreateCommentDiscussionEntities.ts`: Comment and discussion entities

Each migration includes proper indexes, foreign key constraints, and enum definitions.
